import CorrespondenceRevertForwardLetterCell from 'components/awat-table-cells/correspondence/correspondence-revertForwardLetter-cell';
import { IColumnsRenderer } from '../../../../../../components/awat-table/interfaces';
import i18n from '../../../../../../i18n';
import CorrespondenceSubjectCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-subject-cell';
import CorrespondenceSenderCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-sender-cell';
import CorrespondenceRecipientCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-recipient-cell';
import CorrespondenceLetterTypeCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-letter-type';
import CorrespondenceNumberCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-number-cell';
import CorrespondenceDateCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-date-cell';
import CorrespondenceSignerStatusCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-signer-status-cell';
import CorrespondencePositionCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-position-cell';
import CorrespondenceForwardSenderCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-forward-sender-cell';
import CorrespondenceForwardRecipientCell from '../../../../../../components/awat-table-cells/correspondence/correspondence-forward-recipient-cell';

export const correspondenceColumnsRenderer: IColumnsRenderer = {
  subject: {
    renderHead: () => i18n.t('table.correspondence.subject'),
    renderCell: (row, args) => (
      <CorrespondenceSubjectCell
        row={row}
        column={args.column}
        highlightedParts={args.highlights}
      />
    ),
  },
  sender: {
    renderHead: () => i18n.t('table.correspondence.sender'),
    renderCell: (row) => <CorrespondenceSenderCell row={row} />,
  },
  recipient: {
    renderHead: () => i18n.t('table.correspondence.recipient'),
    renderCell: (row) => <CorrespondenceRecipientCell row={row} />,
  },
  type: {
    renderHead: () => i18n.t('table.correspondence.type'),
    renderCell: (row) => <CorrespondenceLetterTypeCell row={row} />,
  },
  number: {
    renderHead: () => i18n.t('table.correspondence.number'),
    renderCell: (row, args) => (
      <CorrespondenceNumberCell row={row} highlightedParts={args.highlights} />
    ),
  },
  numberingDate: {
    renderHead: () => i18n.t('table.correspondence.numberingDate'),
    renderCell: (row, args) => (
      <CorrespondenceDateCell row={row} column={args.column} key={args.column.id} />
    ),
  },
  signerStatus: {
    renderHead: () => i18n.t('table.correspondence.signerStatus'),
    renderCell: (row) => <CorrespondenceSignerStatusCell row={row} />,
  },
  positions: {
    renderHead: () => i18n.t('table.correspondence.positions'),
    renderCell: (row) => <CorrespondencePositionCell row={row} />,
  },
  forwardSender: {
    renderHead: () => i18n.t('table.correspondence.forwardSender'),
    renderCell: (row) => <CorrespondenceForwardSenderCell row={row} />,
  },
  forwardedToMeDate: {
    renderHead: () => i18n.t('table.correspondence.forwardedToMeDate'),
    renderCell: (row, args) => (
      <CorrespondenceDateCell row={row} column={args.column} key={args.column.id} />
    ),
  },
  createdAt: {
    renderHead: () => i18n.t('table.correspondence.createdAt'),
    renderCell: (row, args) => (
      <CorrespondenceDateCell row={row} column={args.column} key={args.column.id} />
    ),
  },
  terminatedDate: {
    renderHead: () => i18n.t('table.correspondence.terminatedDate'),
    renderCell: (row, args) => (
      <CorrespondenceDateCell row={row} column={args.column} key={args.column.id} />
    ),
  },
  discardedDate: {
    renderHead: () => i18n.t('table.correspondence.discardedDate'),
    renderCell: (row, args) => (
      <CorrespondenceDateCell row={row} column={args.column} key={args.column.id} />
    ),
  },
  forwardRecipient: {
    renderHead: () => i18n.t('table.correspondence.forwardRecipient'),
    renderCell: (row) => <CorrespondenceForwardRecipientCell row={row} />,
  },
  forwardDate: {
    renderHead: () => i18n.t('table.correspondence.forwardDate'),
    renderCell: (row, args) => (
      <CorrespondenceDateCell row={row} column={args.column} key={args.column.id} />
    ),
  },
  revertForwardLetter: {
    renderHead: () => i18n.t(''),
    renderCell: (row, args, actions) => (
      <CorrespondenceRevertForwardLetterCell
        row={row}
        onClick={() => {
          actions?.handleOpenForwardRevertDialog?.();
          actions?.setForwardedLetterData?.(row);
        }}
      />
    ),
  },
  sentVia: {
    renderHead: () => i18n.t('table.correspondence.sentVia'),
    renderCell: (row, args) => <></>,
  },
  receiptedBy: {
    renderHead: () => i18n.t('table.correspondence.receiptedBy'),
    renderCell: (row, args) => <></>,
  },
  receiptedAtDate: {
    renderHead: () => i18n.t('table.correspondence.receiptedAtDate'),
    renderCell: (row, args) => <></>,
  },
  receiptRegistrar: {
    renderHead: () => i18n.t('table.correspondence.receiptRegistrar'),
    renderCell: (row, args) => <></>,
  },
  trackingCode: {
    renderHead: () => i18n.t('table.correspondence.trackingCode'),
    renderCell: (row, args) => <></>,
  },
  letterDate: {
    renderHead: () => i18n.t('table.correspondence.letterDate'),
    renderCell: (row, args) => (
      <CorrespondenceDateCell row={row} column={args.column} key={args.column.id} />
    ),
  },

};
