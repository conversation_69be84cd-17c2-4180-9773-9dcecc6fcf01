import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import Button from '@mui/material/Button';
import DialogActions from '@mui/material/DialogActions';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import SwipeableDrawer from '@mui/material/SwipeableDrawer';
import CloseIcon from '@mui/icons-material/Close';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import { t } from 'i18next';
import { useLetter } from 'contexts/pageContext/letters/letterContext';
import Loading from 'components/Loading';
import AWDialog from 'components/AWComponents/AWDialog';
import AWButton from 'components/AWComponents/AWButton';
import PositionEmployeeInput from './components/position-employee-input';
import ForwardLetterTypes from './components/forward-letter-types';
import ForwardLetterTags from './components/forward-letter-tags';
import ForwardLetterAttachments from './components/forward-letter-attachments';
import Paraph from './components/paraph';
import ForwardRecipientList from './components/forward-recipient-list';

const useStyles = makeStyles()((theme) => ({
  dialogPaper: {
    padding: theme.spacing(2.5),
  },
  appBar: {
    background: '#fff',
    color: '#000',
    display: 'flex',
    position: 'relative',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: 'unset',
    '& button': {
      position: 'absolute',
      right: 0,
    },
  },
  dialogContent: {
    padding: 0,
    display: 'grid',
    alignItems: 'center',
  },
  contentBox: {
    marginTop: theme.spacing(2.5),
    display: 'flex',
    justifyContent: 'space-between',
  },
  marginLeft: {
    // marginLeft: theme.spacing(2),
    background: 'blue',
  },
  item: {
    border: `1px solid ${theme.palette.text.lightGray}`,
    borderRadius: '8px',
  },
  infoBox: {
    minHeight: '500px',
    maxHeight: '500px',
    padding: `${theme.spacing(3)} ${theme.spacing(1.5)}`,
    overflow: 'scroll',
  },

  swipeableDrawer: {
    height: '100vh',
    // height: 'calc(100vh - 64px)',
    overflowY: 'scroll',
  },
  topBar: {
    padding: '0 !important',
    display: 'flex',
    alignItems: 'center',
    background: theme.palette.background.innerPage,
    height: 64,
    textAlign: 'center',
    position: 'relative',
    '& svg': {
      height: 64,
      width: 64,
      position: 'absolute',
      padding: 16,
      color: theme.palette.text.gray,
      top: 0,
    },
    '& h6': {
      margin: ' 0 auto',
      height: 64,
      display: 'flex',
      alignItems: 'center',
      fontSize: '1rem',
    },
  },
  fixed: {
    position: 'fixed',
    zIndex: 10,
    width: '100%',
  },
  paperAnchorBottom: {
    // overflowY: 'scroll',
    margin: '64px 0 25px',
    color: theme.palette.text.gray,
    padding: theme.spacing(3, 2),
  },
  addToForwardListMobile: {
    textAlign: 'center',
    justifyContent: 'center',
    padding: theme.spacing(0, 2),
  },
  addButton: {
    // position: 'fixed',
    width: '100%',
    // bottom: theme.spacing(4),
  },
}));

const MultipleForwardLetter = ({ isMobile }) => {
  const {
    handleCloseForwardLetter,
    handleChangeForwardTypes,
    forwardType,
    paraph,
    handleChangeParaph,
    toggleDialogParaphList,
    forwardedRecipientList,
    deleteForwardListItem,
    organizationId,
    openForwardDialogRecipient,
    setOpenForwardDialogRecipient,
    forwardRecipientSelection,
    handleChangeForwardRecipient,
    addToForwardList,
    handleForwardLetter,
    forwardLetterLoading,
    forwardLetterTag,
    handleChangeForwardLetterTag,
    setForwardLetterAttachments,
    forwardLetterAttachments,
    forwardAttachmentsLoading,
    toggleDialogCreateParaph,
    security,
    validForwardNotesSample,
  } = useLetter();
  const { classes, cx } = useStyles();

  return isMobile ? (
    <SwipeableDrawer anchor="bottom" open onClose={handleCloseForwardLetter} disableSwipeToOpen>
      <div className={classes.swipeableDrawer}>
        <div className={cx(classes.fixed, classes.topBar)}>
          {/* <IconButton onClick={handleCloseForwardLetter} aria-label="close"> */}
          <CloseIcon onClick={handleCloseForwardLetter} />
          {/* </IconButton> */}
          <Typography variant="h6">{t('forwardLetter.labels.forwardRecipients')}</Typography>
        </div>
        <div className={classes.paperAnchorBottom}>
          <div>
            <PositionEmployeeInput
              organizationId={organizationId}
              isMobile={isMobile}
              label={t('forwardLetter.labels.recipients')}
              name="forwardRecipient"
              selectedPositionEmployee={forwardRecipientSelection}
              handleChangeAutocomplete={handleChangeForwardRecipient}
              openPositionEmployeeViewerDialog={openForwardDialogRecipient}
              handleOpenPositionEmployeeViewerDialog={() => setOpenForwardDialogRecipient(true)}
              handleClosePositionEmployeeViewerDialog={() => setOpenForwardDialogRecipient(false)}
              onSubmitPositionEmployee={handleChangeForwardRecipient}
              isLetterEditable={() => false}
              isForwardLetter
            />
            <ForwardLetterTypes
              forwardType={forwardType}
              handleChangeForwardTypes={handleChangeForwardTypes}
            />
            <Paraph
              paraph={paraph}
              handleChangeParaph={handleChangeParaph}
              toggleDialogParaphList={toggleDialogParaphList}
              toggleDialogCreateParaph={toggleDialogCreateParaph}
              security={security}
              forwardLetterLoading={forwardLetterLoading}
              validForwardNotesSample={validForwardNotesSample}
            />
            <ForwardLetterAttachments
              setForwardLetterAttachments={setForwardLetterAttachments}
              forwardLetterAttachments={forwardLetterAttachments}
              isDisabled={forwardLetterLoading}
            />
            <ForwardLetterTags
              forwardLetterTag={forwardLetterTag}
              handleChangeForwardLetterTag={handleChangeForwardLetterTag}
            />
            <ForwardRecipientList
              forwardedRecipientList={forwardedRecipientList}
              deleteForwardListItem={deleteForwardListItem}
              hasAttachment={Boolean(forwardLetterAttachments)}
              isMobile={isMobile}
            />
          </div>
          <AWButton
            id="add-forward-recipients"
            variant="contained"
            disabled={
              !forwardRecipientSelection.length || forwardLetterLoading || forwardAttachmentsLoading
            }
            onClick={() => addToForwardList()}
            className={classes.button}
            endIcon={!forwardAttachmentsLoading ?? <ArrowBackIosIcon />}
          >
            {forwardAttachmentsLoading ? (
              <Loading color="white" />
            ) : (
              t('forwardLetter.labels.addRecipients')
            )}
          </AWButton>
          {/* </DialogActions> */}
        </div>
      </div>
    </SwipeableDrawer>
  ) : (
    <AWDialog
      open
      onClose={handleCloseForwardLetter}
      classes={{ paper: classes.dialogPaper }}
      fullWidth
      maxWidth="lg"
      isTitleShow
      title={t('commands.letters.LetterForwarded')}
    >
      <Grid container columns={12} columnSpacing={2} className={classes.contentBox}>
        <Grid item md={6}>
          <div className={classes.item}>
            <div className={classes.infoBox}>
              <div>
                <PositionEmployeeInput
                  organizationId={organizationId}
                  isMobile={isMobile}
                  label={t('forwardLetter.labels.recipients')}
                  name="forwardRecipient"
                  forwardLetterLoading={forwardLetterLoading}
                  selectedPositionEmployee={forwardRecipientSelection}
                  handleChangeAutocomplete={handleChangeForwardRecipient}
                  openPositionEmployeeViewerDialog={openForwardDialogRecipient}
                  handleOpenPositionEmployeeViewerDialog={() => setOpenForwardDialogRecipient(true)}
                  handleClosePositionEmployeeViewerDialog={() =>
                    setOpenForwardDialogRecipient(false)
                  }
                  isForwardLetter
                  onSubmitPositionEmployee={handleChangeForwardRecipient}
                  isLetterEditable={() => false}
                />
              </div>
              <ForwardLetterTypes
                forwardType={forwardType}
                handleChangeForwardTypes={handleChangeForwardTypes}
                forwardLetterLoading={forwardLetterLoading}
              />
              <Paraph
                paraph={paraph}
                handleChangeParaph={handleChangeParaph}
                toggleDialogParaphList={toggleDialogParaphList}
                forwardLetterLoading={forwardLetterLoading}
                toggleDialogCreateParaph={toggleDialogCreateParaph}
                security={security}
                validForwardNotesSample={validForwardNotesSample}
              />
              <ForwardLetterAttachments
                setForwardLetterAttachments={setForwardLetterAttachments}
                forwardLetterAttachments={forwardLetterAttachments}
                isDisabled={forwardLetterLoading}
              />
              <ForwardLetterTags
                forwardLetterTag={forwardLetterTag}
                handleChangeForwardLetterTag={handleChangeForwardLetterTag}
                forwardLetterLoading={forwardLetterLoading}
              />
            </div>
            <DialogActions>
              <AWButton
                id="add-forward-recipients"
                variant="contained"
                disabled={
                  !forwardRecipientSelection.length ||
                  forwardLetterLoading ||
                  forwardAttachmentsLoading
                }
                onClick={() => addToForwardList()}
                className={classes.button}
                endIcon={!forwardAttachmentsLoading ?? <ArrowBackIosIcon />}
              >
                {forwardAttachmentsLoading ? (
                  <Loading color="white" />
                ) : (
                  t('forwardLetter.labels.addRecipients')
                )}
              </AWButton>
            </DialogActions>
          </div>
        </Grid>
        <Grid item md={6}>
          <div className={classes.item}>
            <ForwardRecipientList
              forwardedRecipientList={forwardedRecipientList}
              deleteForwardListItem={deleteForwardListItem}
            />
            <DialogActions>
              <Button
                id="multiple-forward-letter"
                variant="contained"
                disabled={!forwardedRecipientList.length || forwardLetterLoading}
                onClick={handleForwardLetter}
                className={classes.button}
              >
                {forwardLetterLoading ? (
                  <Loading color="white" />
                ) : (
                  t('solutionHistory.labels.letter.forward')
                )}
              </Button>
            </DialogActions>
          </div>
        </Grid>
      </Grid>
    </AWDialog>
  );
};
MultipleForwardLetter.propTypes = {
  isMobile: PropTypes.bool,
};

MultipleForwardLetter.defaultProps = {
  isMobile: false,
};
export default MultipleForwardLetter;
