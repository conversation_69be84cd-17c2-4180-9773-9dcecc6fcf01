import React, { memo } from 'react';

import CheckCircleOutline from '@mui/icons-material/CheckCircleOutline';
import CheckCircle from '@mui/icons-material/CheckCircle';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { makeStyles } from 'tss-react/mui';
import PropTypes from 'prop-types';
import Box from '@mui/material/Box';
import { useTranslation } from 'react-i18next';

import { useNotificationsContext } from '../../../../../contexts/notificationContext';
import { momentDateUTC, momentTimeUTC } from '../../../../../utils';
import notificationTemplateConverter from '../../../../Notification/notification-template-converter';

const useStyle = makeStyles()((theme) => ({
  root: {
    padding: 8,
    direction: 'rtl',
    borderBottom: '1px solid #e8e8e8',
  },
  rootIsRead: {
    background: '#f8f8f8',
    opacity: 0.8,
  },
  containerOperations: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'end',
    alignItems: 'center',
  },
  title: {
    fontSize: theme.spacing(1.6),
    fontWeight: 'bold',
    fontStretch: 'normal',
    fontStyle: 'normal',
    lineHeight: 2,
    letterSpacing: 'normal',
    textAlign: 'right',
    color: '#111',
  },
  description: {
    display: '-webkit-box',
    WebkitLineClamp: 3,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  date: {
    direction: 'rtl',
    color: '#505050',
  },
  checkCircle: {
    color: '#3ebf62',
  },
  display_none: {
    display: 'none !important',
    translate: '4s',
  },
}));

const Item = (props) => {
  const { id, createdAt, currentlyRead, display } = props;
  const { readNotification, unReadNotification } = useNotificationsContext();
  const { t } = useTranslation();
  const data = notificationTemplateConverter(props, t);
  const { classes, cx } = useStyle();

  return (
    <Box
      className={cx(
        classes.root,
        currentlyRead && classes.rootIsRead,
        !display && classes.display_none,
      )}
    >
      <b>
        <Typography className={classes.title} dangerouslySetInnerHTML={{ __html: data?.title }} />
      </b>
      <Box>
        <Typography
          variant="subtitle1"
          className={classes.description}
          dangerouslySetInnerHTML={{ __html: data?.description }}
        />
      </Box>
      <Box className={classes.containerOperations}>
        <Typography variant="subtitle2" className={classes.date}>
          {`${momentDateUTC(createdAt)} - ${momentTimeUTC(createdAt)}`}
        </Typography>
        <Box>
          {currentlyRead ? (
            <IconButton onClick={() => unReadNotification(id)} className={classes.checkCircle}>
              <CheckCircle />
            </IconButton>
          ) : (
            <IconButton onClick={() => readNotification(id)} className={classes.checkCircle}>
              <CheckCircleOutline />
            </IconButton>
          )}
        </Box>
      </Box>
    </Box>
  );
};

Item.propTypes = {
  id: PropTypes.string.isRequired,
  createdAt: PropTypes.string.isRequired,
  currentlyRead: PropTypes.bool.isRequired,
  display: PropTypes.bool,
};

Item.defaultProps = {
  display: true,
};

export default memo(
  Item,
  (prevProps, nextProps) =>
    prevProps.display === nextProps.display && prevProps.currentlyRead === nextProps.currentlyRead,
);
