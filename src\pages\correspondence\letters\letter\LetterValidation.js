import { useCallback, useEffect, useState } from 'react';

import Joi from 'joi';
import { withValidation } from 'common/validation';
import { useUser } from 'contexts/userContext';
import { RouterPrompt } from 'components/RouterPrompt';
import snackbarUtils from 'utils/snackbarUtils';
import { LetterContext } from 'contexts/pageContext/letters/letterContext';
import { wrapDir } from 'utils/direction-detection';
import useListener from 'hooks/useListener';
import { t } from 'i18next';
import LetterMedia from './letter-media';

const initialState = {
  attachments: [],
  bcc: [],
  cc: [],
  confidentiality: '',
  date: '',
  description: '',
  id: '',
  incomingNumber: '',
  incomingDate: '',
  indicator: {},
  organization: {},
  priority: '',
  recipient: [],
  referenceDate: '',
  referenceNumber: '',
  referenceType: '',
  references: [],
  secretariat: {},
  sender: [],
  signer: [],
  subject: '',
  type: '',
  body: '',
  additionalAttachments: [],
};

const schema = {
  attachments: Joi.array(),
  bcc: Joi.array(),
  cc: Joi.array(),
  confidentiality: Joi.string().optional().allow(''),
  date: Joi.string().required(),
  description: Joi.string().optional().allow(''),
  id: Joi.string(),
  type: Joi.string(),
  incomingNumber: Joi.string().optional().allow(''),
  incomingDate: Joi.string().optional().allow(''),
  indicator: Joi.required(),
  organization: Joi.object(),
  priority: Joi.string().optional().allow(''),
  recipient: Joi.required(),
  references: Joi.array(),
  secretariat: Joi.required(),
  sender: Joi.required(),
  signer: Joi.array(),
  subject: Joi.string().required(),
  body: Joi.any().optional().allow(''),
  additionalAttachments: Joi.array().optional(),
};

const schemaForIncomingLetter = {
  attachments: Joi.array(),
  bcc: Joi.array(),
  cc: Joi.array(),
  confidentiality: Joi.string().optional().allow(''),
  date: Joi.string().required(),
  description: Joi.string().optional().allow(''),
  id: Joi.string(),
  type: Joi.string(),
  incomingNumber: Joi.string().required(),
  incomingDate: Joi.string().required(),
  indicator: Joi.required(),
  organization: Joi.object(),
  priority: Joi.string().optional().allow(''),
  recipient: Joi.required(),
  references: Joi.array(),
  secretariat: Joi.required(),
  sender: Joi.required(),
  signer: Joi.array(),
  subject: Joi.string().required(),
  body: Joi.any().optional().allow(''),
  additionalAttachments: Joi.array().optional(),
};

const comparerArrays = (targetValue, value) => {
  if (targetValue.length !== value.length) {
    return false;
  }
  if (targetValue[0]?.user?.id || value[0]?.user?.id) {
    const isEqual = targetValue.every((t) =>
      value.find((v) => v.user?.id === t.user?.id && v.position.id === t.position.id),
    );
    if (!isEqual) {
      return false;
    }
    return true;
  }
  const isEqual = targetValue.every((t) => value.find((v) => v._id === t._id));
  if (!isEqual) {
    return false;
  }
  return true;
};

const comparerCc = (targetValue, value) => {
  if (targetValue.length !== value.length) {
    return false;
  }
  if (targetValue[0]?.user?.id || value[0]?.user?.id) {
    const isEqual = targetValue.every((t) =>
      value.find(
        (v) => v.user.id === t.user.id && v.position.id === t.position.id && v.note === t.note,
      ),
    );
    if (!isEqual) {
      return false;
    }
    return true;
  }
  const isEqual = targetValue.every((t) => value.find((v) => v._id === t._id && v.note === t.note));
  if (!isEqual) {
    return false;
  }
  return true;
};

const comparers = {
  sender: (targetValue, value) => {
    if (targetValue?.user?.id || value[0]?.user?.id) {
      return (
        targetValue?.user?.id === value[0]?.user?.id &&
        targetValue?.position?.id === value[0]?.position?.id
      );
    }
    return targetValue?._id === value[0]?._id;
  },
  recipient: (targetValue, value) => comparerArrays(targetValue, value),
  bcc: (targetValue, value) => comparerArrays(targetValue, value),
  cc: (targetValue, value) => comparerCc(targetValue, value),
  signer: (targetValue, value) => comparerArrays(targetValue, value),
  attachments: (targetValue, value) => {
    if (targetValue.length !== value.length) {
      return false;
    }
    const isEqual = targetValue.every((t) => value.find((v) => v.name === t.name));
    if (!isEqual) {
      return false;
    }
    const isEqualSelecteds = targetValue.every((t) =>
      value.find((v) => t.name === v.name && t.type === v.type),
    );
    if (!isEqualSelecteds) {
      return false;
    }
    return true;
  },
  additionalAttachments: (targetValue, value) => {
    if (targetValue?.length !== value?.length) {
      return false;
    }
    const isEqual = targetValue.every((t) => value.find((v) => v.name === t.name));
    if (!isEqual) {
      return false;
    }
    const isEqualSelecteds = targetValue.every((t) =>
      value.find((v) => t.name === v.name && t.type === v.type),
    );
    if (!isEqualSelecteds) {
      return false;
    }
    return true;
  },
};

const LetterValidation = ({
  security,
  letterId,
  onSetLetterNumber,
  letters,
  extraItems,
  loading,
  type,
  typeOfLetter,
  users,
  contacts,
  secretariats,
  state,
  initialState: initState,
  onChange,
  isInvalid,
  getValidationError,
  openBackdrop,
  handleChangeChecked,
  goBack,
  priorities,
  confidentialities,
  referenceTypes,
  showBcc,
  handleShowBcc,
  handleDeleteReferenceItem,
  isBlocking,
  setIsBlocking,
  // isResetTouched,
  resetIndicators,
  referencesList,
  setReferencesList,
  openDetails,
  handleCloseDetails,
  handleOpenDetails,
  selectedLetter,
  submitSave,
  handleReceipt,
  handlePrint,
  handleDownloadAttachment,
  getRequestData,
  touched,
  validationOnChange,
  validationOnSubmit,
  setTouched,
  tab,
  onChangeTab,
  letterTypes,
  openAddReferenceDrawer,
  handleOpenAddReferenceDrawer,
  anchorEl,
  openExtraButton,
  handleOpenExtraButton,
  handleCloseExtraButton,
  senderSelection,
  setSenderSelection,
  recipientSelection,
  setRecipientSelection,
  ccSelection,
  setCcSelection,
  bccSelection,
  setBccSelection,
  signerSelection,
  setSignerSelection,
  secretariatSelection,
  setSecretariatSelection,
  indicatorSelection,
  setIndicatorSelection,
  indicatorsActived,
  prioritySelection,
  setPrioritySelection,
  confidentialitySelection,
  setConfidentialitySelection,
  isLetterEditable,
  isLetterEditableAttachmentAsContent,
  isLetterDeletableAttachmentAsContent,
  isLetterEditableIndicator,
  isLetterEditableNote,
  isAdditionalAttachmentEditable,
  showDialogSaveAndSetLetterNumber,
  handleCloseSaveAndSetLetterNumber,
  acceptSaveAndSetLetterNumber,
  showDialogNumberedLetter,
  handleCloseNumberedLetter,
  loadingLetterNumber,
  additionalAttachmentsRef,
  referenceRef,
  registrationInfoRef,
  attachmentsRef,
  openPositionEmployeeTreeDialogRecipient,
  handleOpenPositionEmployeeTreeDialogRecipient,
  handleClosePositionEmployeeTreeDialogRecipient,
  openPositionEmployeeTreeDialogSender,
  handleOpenPositionEmployeeTreeDialogSender,
  handleClosePositionEmployeeTreeDialogSender,
  onForward,
  openForwardLetter,
  handleCloseForwardLetter,
  handleChangeForwardTypes,
  forwardType,
  paraph,
  openPreParaphList,
  toggleDialogParaphList,
  forwardedRecipientList,
  tabPreParaphValue,
  handleChangeTabPreParaph,
  handleClickPreParaph,
  forwardList,
  deleteForwardListItem,
  organizationId,
  positions,
  employees,
  openPositionEmployeeTreeDialogCC,
  openPositionEmployeeTreeDialogBCC,
  openPositionEmployeeTreeDialogSigner,
  handleOpenPositionEmployeeTreeDialogCC,
  handleClosePositionEmployeeTreeDialogCC,
  handleOpenPositionEmployeeTreeDialogBCC,
  handleClosePositionEmployeeTreeDialogBCC,
  handleOpenPositionEmployeeTreeDialogSigner,
  handleClosePositionEmployeeTreeDialogSigner,
  handleChangeForwardRecipient,
  onSubmitForwardRecipient,
  forwardRecipientSelection,
  addToForwardList,
  handleForwardLetter,
  forwardLetterLoading,
  forwardLetterTag,
  handleChangeForwardLetterTag,
  openForwardDialogRecipient,
  setOpenForwardDialogRecipient,
  operationInfo,
  handleToggleDialogDiscard,
  showDialogTerminate,
  handleToggleDialogTerminate,
  terminateDescriptionVal,
  handleChangeTerminateDescriptionVal,
  terminateLetter,
  isLoadingTerminate,
  showDialogDiscard,
  discardDescriptionVal,
  handleChangeDiscardDescriptionVal,
  discardLetter,
  isLoadingDiscard,
  setOpenPositionEmployeeTreeDialogRecipient,
  openForwardListMobileDialog,
  setOpenForwardListMobileDialog,
  attachments,
  setAttachments,
  anchorElLetterInfoMenu,
  openLetterInfoMenu,
  handleOpenLetterInfoMenu,
  handleCloseLetterInfoMenu,
  isVisibleLetterHistoryDialog,
  toggleVisibleLetterHistoryDialog,
  letterHistory,
  letterHistoryLoading,
  setLetterHistoryLoading,
  isTouched,
  setIsTouched,
  letterNumber,
  getLetterLayouts,
  letterLayoutsLoading,
  openPrintDialog,
  handleClosePrintDialog,
  letterLayoutsList,
  selectedLetterLayout,
  handleChangeSelectedLetterLayout,
  openSendECEDialog,
  handleOpenSendECEDialog,
  handleCloseSendECEDialog,
  eceEmailRecipient,
  onChangeEceEmailRecipient,
  handleSendECE,
  sendECELoading,
  openExportECEDialog,
  handleOpenExportECEDialog,
  handleCloseExportECEDialog,
  handleExportECE,
  exportECELoading,
  ECEEmailRecipientErrorText,
  setECEEmailRecipientErrorText,
  showDialogNumberedErrorForIndicatorStatus,
  setShowDialogNumberedErrorForIndicatorStatus,
  submitSaveAndSign,
  showDialogSignAndSaveOrCancel,
  setShowDialogSignAndSaveOrCancel,
  selectedPosition,
  handleOpenCcDescription,
  handleCloseCcDescription,
  handleChangeCcDescription,
  ccDescriptionPanel,
  handleCloseContactDialog,
  handleOpenContactDialog,
  quicklyAddingContactDialog,
  handleCreatedContact,
  setContacts,
  handleSignLetter,
  checkedSign,
  setCheckedSign,
  checkedStamps,
  setCheckedStamps,
  forwardLetterAttachments,
  setForwardLetterAttachments,
  handleUploadDocument,
  uploadDocumentLoading,
  forwardAttachmentsLoading,
  additionalAttachments,
  setAdditionalAttachments,
  forwardRecipient,
  voidLetterDialog,
  handleCloseVoidLetterDialog,
  handleOpenVoidLetterDialog,
  onChangeValueNote,
  voidLetterNote,
  submitVoidLetter,
  voidLetterLoading,
  errorOfVoidOperationText,
  errorVoidLetterDialog,
  handleCloseErrorVoidLetterDialog,
  openDocumentPreviewerDialog,
  handleCloseDocumentPreviewer,
  handleOpenDocumentPreviewer,
  secretariatListHasAccess,
  indicatorsListHasAccess,
  secretariatListForExportEce,
  indicatorsListForExportEce,
  createLetterSampleState,
  setCreateLetterSampleState,
  handleCreateLetterSample,
  openForCreateLetterSampleDialog,
  handleOpenCreateLetterSampleDialog,
  handleCloseCreateLetterSampleDialog,
  loadingCreateLetterSample,
  letterSamplesListAnchorElAnchorEl,
  handleOpenLetterSamplesListPopover,
  handleCloseLetterSamplesPopover,
  correspondenceSamplesListLoading,
  correspondenceSamplesList,
  deleteCorrespondenceSample,
  setLetterSamplesListAnchorEl,
  deleteCorrespondenceSampleLoading,
  showFieldsReferenceLetter,
  handleShowReferenceLetter,
  getReferenceLettersList,
  searchReferencesLettersLoading,
  setReferenceLetterNumber,
  referenceLetterNumberList,
  newReferencesItems,
  setNewReferencesItems,
  referenceSearchPopoverOpen,
  handleCloseReferenceSearchPopover,
  handleSelectReferenceSearchPopover,
  handleNavigateToReference,
  resetSelectedReferenceLetter,
  sendEceReceipt,
  composeInfoConfig,
  isOpenComposeInfoConfig,
  handleComposeInfoConfigDialog,
  handleSubmitComposeInfoConfig,
  onChangeComposeInfoConfig,
  openLetterAiAnchorElement,
  handleCloseLetterAiPopover,
  handleClickLetterAiPopover,
  letterAiAnchorEl,
  selectedTabCreateLetterByTopic,
  handleToggleChangeTabGenai,
  setTextFieldGenai,
  submitCreateLetterByTopic,
  genaiLoading,
  textFieldGenai,
  isEceLetter,
  isIncomingNumberAndDateDisable,
  checkIncomingNumberRepetitive,
  showDialogNumberRepetitive,
  setShowDialogNumberRepetitive,
  confirmIncomingNumberRepetitive,
  closeDialogNumberRepetitive,
  activeSecretariats,
  currentSecretariat,
  forwardNotesSamples,
  handleDeleteForwardNotesSamples,
  selectedRadio,
  handleChangePreParaphRadio,
  toggleDialogCreateParaph,
  openPreCreateParaph,
  handleCreateForwardNotesSample,
  isSubmittingForwardNoteSample,
  deleteForwardNoteSampleLoading,
  isLoadingForwardNoteSamples,
  setParaph,
  validForwardNotesSample,
  setValidForwardNotesSample,
}) => {
  const { signedInUser } = useUser();

  useEffect(() => {
    if (type === 'create') {
      onChange({
        target: { name: 'date', value: new Date() },
      });
      onChange({
        target: { name: 'priority', value: '' },
      });
      onChange({
        target: { name: 'confidentiality', value: '' },
      });
    }
  }, [type]);

  useEffect(() => {
    if (selectedPosition) {
      if (employees.organizationEmployeeContacts.data?.length && type === 'create') {
        if (typeOfLetter !== 'incoming') {
          const loggedSender = employees.organizationEmployeeContacts?.data?.find(
            (contact) => contact?.id === signedInUser.id,
          );
          const userPosition = loggedSender?.positions.find(
            (position) => position?.id === selectedPosition.position.id,
          );

          setSenderSelection(
            loggedSender?.id
              ? {
                  user: {
                    id: loggedSender?.id,
                    firstName: loggedSender?.firstName || '',
                    lastName: loggedSender?.lastName || '',
                    username: loggedSender?.username || '',
                  },
                  position: {
                    id: userPosition.id,
                    slot: userPosition.slot,
                    title: userPosition.title || '',
                  },
                }
              : [],
          );
          onChange({
            target: {
              name: 'sender',
              value: [
                {
                  user: {
                    id: loggedSender?.id,
                    firstName: loggedSender?.firstName || '',
                    lastName: loggedSender?.lastName || '',
                    username: loggedSender?.username || '',
                  },
                  position: {
                    id: loggedSender?.positions[0].id,
                    slot: loggedSender?.positions[0].slot,
                    title: loggedSender?.positions[0].title || '',
                  },
                },
              ],
            },
          });
        }
      }
    }
  }, [
    employees.organizationEmployeeContacts.data,
    signedInUser.id,
    type,
    selectedPosition.position,
  ]);

  useEffect(() => {
    if (activeSecretariats.length && type === 'create' && !letterId) {
      const defaultSecretariat =
        activeSecretariats.find((secretariat) => secretariat.isDefault === true) ||
        activeSecretariats[0];

      setSecretariatSelection(defaultSecretariat);

      onChange({
        target: { name: 'secretariat', value: { id: defaultSecretariat?._id } },
      });

      if (defaultSecretariat?._id) {
        resetIndicators(defaultSecretariat._id);
      }
    }
  }, [activeSecretariats, type]);

  useEffect(() => {
    if (secretariats.indicatorsList.data?.length && type === 'create') {
      const defaultIndicator =
        indicatorsActived?.find?.((secretariat) => secretariat.isDefault === true) ||
        indicatorsActived[0];
      setIndicatorSelection(defaultIndicator);
      onChange({
        target: { name: 'indicator', value: { id: defaultIndicator?._id } },
      });
      validationOnChange({
        target: {
          name: 'indicator',
          value: { id: defaultIndicator?._id },
        },
      });
      setTouched(touched.filter((item) => item !== 'indicator'));
    }
  }, [indicatorsActived, type]);

  const addReference = () => {
    handleOpenAddReferenceDrawer(false);
    if (
      newReferencesItems.referenceType &&
      newReferencesItems.referenceNumber &&
      newReferencesItems.referenceDate
    ) {
      const value = {
        referenceType: newReferencesItems.referenceType?.value,
        referenceNumber: newReferencesItems.referenceNumber,
        referenceDate: newReferencesItems.referenceDate,
        letter: newReferencesItems.letter ?? null,
      };

      validationOnChange({ target: { name: 'references', value: [...referencesList, value] } });
      setReferencesList((prevState) => [...prevState, value]);

      setNewReferencesItems({
        referenceType: undefined,
        referenceNumber: '',
        referenceDate: null,
        letter: null,
      });
    }
  };

  const handleDeleteReferenceItemWithValidation = (referenceItem) => {
    validationOnChange({
      target: { name: 'references', value: referencesList.filter((a) => a !== referenceItem) },
    });
    handleDeleteReferenceItem(referenceItem);
  };

  const isNewValueInPreStateSigner = (PreValue, newValue) => {
    if (
      newValue &&
      newValue.length &&
      PreValue &&
      PreValue.length &&
      PreValue.some(
        (v) =>
          v?.position.id === newValue[newValue.length - 1]?.position.id &&
          v?.user.id === newValue[newValue.length - 1]?.user.id,
      )
    ) {
      return true;
    }
    return false;
  };

  const isNewValueInPreState = (PreValue, newValue) => {
    if (typeOfLetter !== 'outgoing') {
      if (
        newValue &&
        newValue.length &&
        PreValue &&
        PreValue.length &&
        PreValue.some(
          (v) =>
            v?.position.id === newValue[newValue.length - 1]?.position.id &&
            v?.user.id === newValue[newValue.length - 1]?.user.id,
        )
      ) {
        return true;
      }
      return false;
    }
    if (
      newValue &&
      newValue.length &&
      PreValue &&
      PreValue.length &&
      PreValue.some((v) => v?._id === newValue[newValue.length - 1]?._id)
    ) {
      return true;
    }
    return false;
  };

  const handleChangeSender = (e, newValue) => {
    if (newValue === null) setSenderSelection(typeOfLetter === 'incoming' ? null : []);
    else setSenderSelection(newValue);
    validationOnChange({
      target: { name: 'sender', value: newValue || [] },
    });
    onChange({
      target: { name: 'sender', value: newValue ? [newValue] : [] },
    });
  };

  const handleChangeRecipient = (e, newValue, st) => {
    if (newValue === null) {
      setRecipientSelection([]);
    } else {
      setRecipientSelection(newValue);
    }

    validationOnChange({
      target: { name: 'recipient', value: newValue || [] },
    });
    onChange({
      target: {
        name: 'recipient',
        value: newValue || [],
      },
    });
  };

  const handleChangeCc = (e, newValue, st) => {
    if (newValue === null) setCcSelection([]);
    else {
      if (
        st !== 'removeOption' &&
        st !== 'editDescription' &&
        isNewValueInPreState(state.cc, newValue)
      )
        return;
      setCcSelection(newValue);
    }

    setCcSelection(newValue);
    validationOnChange({
      target: { name: 'cc', value: newValue || [] },
    });
    onChange({
      target: { name: 'cc', value: newValue || [] },
    });
  };

  const handleChangeBcc = (e, newValue, st) => {
    if (newValue === null) setBccSelection([]);
    else {
      if (st !== 'removeOption' && isNewValueInPreState(state.bcc, newValue)) return;
      setBccSelection(newValue);
    }
    validationOnChange({
      target: { name: 'bcc', value: newValue || [] },
    });
    onChange({
      target: { name: 'bcc', value: newValue || [] },
    });
  };

  useListener(() => {
    resetIndicators(secretariatSelection?.id || secretariatSelection?._id);
  }, [secretariatSelection, secretariats.secretariatsList.data, type]);

  const handleChangeSecretariat = (e, newValue) => {
    if (newValue === null) {
      setSecretariatSelection(null);
    } else {
      setSecretariatSelection(newValue);
    }
    setIndicatorSelection(null);
    validationOnChange({
      target: {
        name: 'indicator',
        value: null,
      },
    });
    onChange({
      target: {
        name: 'indicator',
        value: null,
      },
    });

    validationOnChange({
      target: {
        name: 'secretariat',
        value: newValue ? { id: newValue.id || newValue._id } : null,
      },
    });
    onChange({
      target: {
        name: 'secretariat',
        value: newValue ? { id: newValue?.id || newValue?._id } : null,
      },
    });

    if (newValue?.id || newValue?._id) {
    } else {
      validationOnChange({
        target: {
          name: 'indicator',
          value: null,
        },
      });
      onChange({
        target: {
          name: 'indicator',
          value: null,
        },
      });
    }
  };

  const handleChangeIndicator = (e, newValue) => {
    if (newValue === null) setIndicatorSelection(null);
    else setIndicatorSelection(newValue);
    validationOnChange({
      target: {
        name: 'indicator',
        value: newValue ? { id: newValue?.id || newValue?._id } : null,
      },
    });
    onChange({
      target: {
        name: 'indicator',
        value: newValue ? { id: newValue?.id || newValue?._id } : null,
      },
    });
  };

  const handleChangeSigner = (e, newValue, st) => {
    if (newValue === null) setSignerSelection([]);
    else {
      if (st !== 'removeOption' && isNewValueInPreStateSigner(state.signer, newValue)) return;
      setSignerSelection(newValue);
    }
    validationOnChange({
      target: { name: 'signer', value: newValue || [] },
    });
    onChange({
      target: { name: 'signer', value: newValue || [] },
    });
  };

  const handleChangeConfidentiality = (e, newValue) => {
    if (newValue === null) setConfidentialitySelection('');
    else setConfidentialitySelection(newValue);
    validationOnChange({
      target: { name: 'confidentiality', value: newValue?.value || '' },
    });
    onChange({
      target: { name: 'confidentiality', value: newValue?.value || '' },
    });
  };

  const handleChangePriority = (e, newValue) => {
    if (newValue === null) setPrioritySelection('');
    else setPrioritySelection(newValue);
    validationOnChange({
      target: { name: 'priority', value: newValue?.value || '' },
    });
    onChange({
      target: { name: 'priority', value: newValue?.value || '' },
    });
  };

  const handleChangeBody = (data) => {
    validationOnChange({
      target: { name: 'body', value: data ? JSON.stringify(data) : data },
    });
    onChange({
      target: { name: 'body', value: data ? JSON.stringify(data) : data },
    });
  };

  const handleChangeReferenceType = (e, newValue) => {
    setNewReferencesItems((preState) => ({ ...preState, referenceType: newValue }));
  };

  const handleChangeReferenceNumber = (e) => {
    const value = e?.target?.value ?? '';
    setNewReferencesItems((preState) => ({ ...preState, referenceNumber: value, letter: null }));
  };

  const handleSelectedReferenceDate = (event) => {
    const value = event?.target?.value ?? '';
    setNewReferencesItems((preState) => ({ ...preState, referenceDate: value }));
  };

  const handleSelectedDate = (value) => {
    const newValue = {
      target: {
        ...value.target,
        value: value.target.value ? value.target.value.toISOString() : '',
      },
    };

    validationOnChange(newValue);
    onChange(newValue);
  };

  const handleSelectedDateIncomingLetter = (value) => {
    if (isEceLetter) return;

    const newValue = {
      target: {
        ...value.target,
        value: value.target.value ? value.target.value.toISOString() : '',
      },
    };

    validationOnChange(newValue);
    onChange(newValue);
  };

  const handleIncomingLetterNumberChange = (e) => {
    if (isEceLetter) return;

    const value = e?.target?.value;

    validationOnChange({
      target: { name: 'incomingNumber', value },
    });
    onChange({
      target: { name: 'incomingNumber', value },
    });
  };

  const onChange2 = (e) => {
    validationOnChange(e);
    onChange(e);
  };

  useEffect(() => {
    setIsTouched(touched.length > 0);
  }, [touched]);

  useEffect(() => {
    setIsBlocking(isTouched);
    !isTouched && setTouched([]);
  }, [isTouched, setIsBlocking]);

  const onSave = () => {
    if (validationOnSubmit(getRequestData())) {
      if (typeOfLetter === 'incoming') {
        if (!state.incomingDate) {
          snackbarUtils.error(t('common.messages.addDateReceiveLetter'));
          return;
        }
        if (!state.incomingNumber) {
          snackbarUtils.error(t('forwardLetter.labels.addIncomingNumber'));
          return;
        }

        checkIncomingNumberRepetitive();
      } else {
        submitSave();
      }
    } else {
      if (state.secretariat === null || state.indicator === null) {
        snackbarUtils.error(t('common.messages.complete-all-required-fields'));
      }
    }
  };

  const handleLetterNumber = () => {
    if (indicatorSelection?.isDisabled) {
      setShowDialogNumberedErrorForIndicatorStatus(() => true);
    } else if (validationOnSubmit(getRequestData())) {
      onSetLetterNumber();
    } else {
      snackbarUtils.error(`${t('common.messages.MandatoryField')}`);
    }
  };

  const handleLetterSign = () => {
    if (isTouched) {
      setShowDialogSignAndSaveOrCancel(true);
    } else {
      handleSignLetter();
    }
  };

  // -------------- Attachments ---------------
  const handleAddAttachment = (e) => {
    if (e.target?.files[0] && e.target?.files[0].size < 30 * 1024 ** 2) {
      setAttachments((prevAttachments) => {
        const newItems = [...prevAttachments, e.target?.files[0]];
        validationOnChange({
          target: {
            name: 'attachments',
            value: newItems,
          },
        });
        return newItems;
      });
    } else {
      snackbarUtils.error(`${t('common.labels.sizeFile30')}`);
    }
  };

  const handleAddAttachmentAsContent = (e) => {
    if (e.target?.files[0] && e.target?.files[0].size < 30 * 1024 ** 2) {
      setAttachments((prevAttachments) => {
        const prevAttachmentsWithoutNewAttachment = prevAttachments.filter(
          (item) => !item?.mustBeAsContent && !item?.isLetterContent,
        );
        const file = e.target?.files[0];
        file.mustBeAsContent = true;

        const newItems = [...prevAttachmentsWithoutNewAttachment, file];

        validationOnChange({
          target: {
            name: 'attachments',
            value: newItems,
          },
        });
        return newItems;
      });
    } else {
      snackbarUtils.error(`${t('common.labels.sizeFile30')}`);
    }
  };

  const handleDeleteAttachment = (attachment) => {
    setAttachments((prevAttachments) => {
      const newItems = prevAttachments.filter((a) => a !== attachment);
      validationOnChange({
        target: {
          name: 'attachments',
          value: newItems,
        },
      });
      return newItems;
    });
  };

  const handleAddAdditionalAttachment = (additionalAttachmentCallback) => {
    setAdditionalAttachments((prev) => {
      const result = additionalAttachmentCallback(prev);

      validationOnChange({
        target: {
          name: 'additionalAttachments',
          value: result,
        },
      });

      return result;
    });
  };

  const handleSelectedAttachment = (e, index, isLetterContent) => {
    setAttachments((prevAttachments) => {
      const newItems = [...prevAttachments];
      newItems.forEach((item, i) => {
        if (i === index) {
          if (item.type === '' || item.type === 'content') {
            item.type = isLetterContent ? 'content' : '';
          }
          item.isLetterContent = isLetterContent;
        } else {
          if (item.type === '' || item.type === 'content') {
            item.type = '';
          }
          item.isLetterContent = false;
        }
      });
      validationOnChange({
        target: {
          name: 'attachments',
          value: newItems,
        },
      });
      return newItems;
    });
  };

  // --------- Disabled action buttons ----------
  const securityOfComposeNewLetter = () => {
    if (typeOfLetter === 'incoming') return security?.composeIncoming;
    if (typeOfLetter === 'outgoing') return security?.composeOutgoing;
    if (typeOfLetter === 'internal') return security?.composeInternal;
    return null;
  };

  const securityOfUpdateLetter = () => {
    if (typeOfLetter === 'incoming') return security?.updateIncoming;
    if (typeOfLetter === 'outgoing') return security?.updateOutgoing;
    if (typeOfLetter === 'internal') return security?.updateInternal;
    return null;
  };

  const securityOfComposeLetter = () => {
    if (type === 'create') {
      return securityOfComposeNewLetter();
    }
    return securityOfUpdateLetter();
  };

  const handleIsDisabledSave = () => {
    if (type === 'create') {
      return false;
    }
    if (openBackdrop || !isTouched) {
      return true;
    }
    if (!selectedLetter?.isInCartable) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some((f) => f.type === 'void' && f.status === 'success')
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const handleIsDisabledForward = () => {
    if (isTouched) {
      return true;
    }
    if (type === 'create') {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    if (selectedLetter?.isInCartable || selectedLetter?.number) {
      return false;
    }
    // if (!selectedLetter?.isInCartable) {
    //   return true;
    // }
    return true;
  };

  const handleIsDisabledNumber = useCallback(() => {
    if (type === 'create' || !!selectedLetter?.number) {
      return true;
    }
    if (selectedLetter?.signedBy?.length !== selectedLetter?.signer?.length) {
      return true;
    }
    if (
      !selectedLetter?.signer?.every((t) =>
        selectedLetter?.signedBy?.find((s) => s.user._id === t.user._id),
      )
    ) {
      return true;
    }
    if (
      !state?.signer?.every((t) => selectedLetter?.signedBy?.find((s) => s.user._id === t.user._id))
    ) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some((f) => f.type === 'discard' && f.status === 'success')
    ) {
      return true;
    }
    if (!selectedLetter?.isInCartable) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some((f) => f.type === 'void' && f.status === 'success')
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }

    if (
      !secretariatListHasAccess.find((sec) => sec.id === state.secretariat?.id) ||
      !indicatorsListHasAccess.find((sec) => sec.id === state.indicator?.id)
    ) {
      return true;
    }
    return false;
  }, [state, indicatorsListHasAccess, secretariatListHasAccess]);

  const handleIsDisabledEce = () => {
    if (type === 'create' || !selectedLetter?.number) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }

    if (
      !secretariatListForExportEce.find((sec) => sec.id === state.secretariat?.id) ||
      !indicatorsListForExportEce.find((sec) => sec.id === state.indicator?.id)
    ) {
      return true;
    }
    return false;
  };

  const handleIsDisabledReceipt = () => {
    if (type === 'create' || !selectedLetter?.number) return true;
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some((f) => f.type === 'void' && f.status === 'success')
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const handleIsDisabledPrint = () => {
    if (type === 'create') {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const handleIsDisabledSign = () => {
    if (type === 'create') {
      return true;
    }
    if (selectedLetter?.signer?.length === 0) {
      return true;
    }
    if (!selectedLetter?.isInCartable) {
      return true;
    }
    if (
      !selectedLetter?.signer?.some(
        (s) => s.user?._id === signedInUser.id && s.position.id === selectedPosition.position.id,
      )
    ) {
      return true;
    }
    if (
      selectedLetter?.signedBy?.length &&
      selectedLetter?.signedBy?.some(
        (s) =>
          s.position.id === selectedPosition.position.id &&
          JSON.stringify(s.position.slot) === JSON.stringify(selectedPosition.position.slot),
      )
    ) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const handleIsDisabledTerminate = () => {
    if (type === 'create' || !selectedLetter?.number) {
      return true;
    }
    if (selectedLetter?.isTerminated) {
      return true;
    }
    if (!selectedLetter?.isInCartable) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some((f) => f.type === 'void' && f.status === 'success')
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const handleIsDisabledDiscard = () => {
    if (type === 'create') {
      return true;
    }
    if (selectedLetter?.number) {
      return true;
    }
    if (
      selectedLetter?.signedBy?.length &&
      selectedLetter?.signedBy?.every((s) => s.user._id !== signedInUser.id)
    ) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    if (!selectedLetter?.isInCartable) {
      return true;
    }
    return false;
  };

  const handleIsSingedCurrentLetter = () => {
    if (
      selectedLetter?.signedBy?.length &&
      selectedLetter?.signedBy?.some(
        (s) =>
          s.position.id === selectedPosition.position.id &&
          JSON.stringify(s.position.slot) === JSON.stringify(selectedPosition.position.slot),
      )
    ) {
      return true;
    }
    return false;
  };

  const handleIsTerminatedCurrentLetter = () => {
    if (selectedLetter?.isTerminated) {
      return true;
    }
    return false;
  };

  const handleIsDiscardedCurrentLetter = () => {
    if (
      (selectedLetter?.operations?.length &&
        selectedLetter?.operations?.some((f) => f.type === 'discard' && f.status === 'success')) ||
      selectedLetter?.status === 'discard'
    ) {
      return true;
    }
    return false;
  };

  const handleIsDiscardedCurrentLetterByOperations = () => {
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some((f) => f.type === 'discard' && f.status === 'success')
    ) {
      return true;
    }
    return false;
  };

  const handleDisabledVoid = () => {
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some((f) => f.type === 'void' && f.status === 'success')
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    if (!selectedLetter?.number) {
      return true;
    }
    return false;
  };

  const applyCorrespondenceSample = () => {
    setLetterSamplesListAnchorEl(null);
  };

  const validateForwardNotesSampleLength = (text) => {
    if (text.length > 1024) {
      setValidForwardNotesSample({
        valid: false,
        text: t('validation.string_max'),
      });
    } else {
      setValidForwardNotesSample({
        valid: true,
        text: null,
      });
    }
  };

  const handleChangeParaph = (e) => {
    setParaph(e.target.value);
    validateForwardNotesSampleLength(e.target.value);
  };

  const selectedTabKey = tabPreParaphValue;
  const selectedId = selectedRadio[selectedTabKey];
  const isForwardTab = selectedTabKey === 'forwardNotesSamples';
  const isForwardListEmpty = isForwardTab && forwardNotesSamples.length === 0;
  const isSubmitDisabled = isForwardListEmpty || !selectedId;

  return (
    <LetterContext.Provider
      value={{
        security,
        letterId,
        letters,
        extraItems,
        loading,
        type,
        typeOfLetter,
        users,
        contacts,
        secretariats,
        initState,
        state,
        onChange: onChange2,
        isInvalid,
        getValidationError,
        isTouched,
        openBackdrop,
        handleChangeChecked,
        goBack,
        priorities,
        confidentialities,
        referenceTypes,
        showBcc,
        handleShowBcc,
        senderSelection,
        handleChangeSender,
        recipientSelection,
        handleChangeRecipient,
        ccSelection,
        handleChangeCc,
        bccSelection,
        handleChangeBcc,
        handleChangeBody,
        handleSelectedDate,
        signerSelection,
        handleChangeSigner,
        secretariatSelection,
        handleChangeSecretariat,
        indicatorSelection,
        indicatorsActived,
        handleChangeIndicator,
        handleSelectedDateIncomingLetter,
        handleIncomingLetterNumberChange,
        confidentialitySelection,
        handleChangeConfidentiality,
        prioritySelection,
        handleChangePriority,
        handleChangeReferenceType,
        handleChangeReferenceNumber,
        handleSelectedReferenceDate,
        handleDeleteReferenceItem: handleDeleteReferenceItemWithValidation,
        referencesList,
        referencesItems: newReferencesItems,
        addReference,
        openDetails,
        handleCloseDetails,
        handleOpenDetails,
        selectedLetter,
        onSave,
        handleLetterSign,
        handleReceipt,
        handlePrint,
        handleDownloadAttachment,
        handleLetterNumber,
        tab,
        onChangeTab,
        letterTypes,
        openAddReferenceDrawer,
        handleOpenAddReferenceDrawer,
        anchorEl,
        openExtraButton,
        handleOpenExtraButton,
        handleCloseExtraButton,
        isLetterEditable,
        isLetterEditableAttachmentAsContent,
        isLetterDeletableAttachmentAsContent,
        isLetterEditableIndicator,
        isLetterEditableNote,
        isAdditionalAttachmentEditable,
        showDialogSaveAndSetLetterNumber,
        handleCloseSaveAndSetLetterNumber,
        acceptSaveAndSetLetterNumber,
        showDialogNumberedLetter,
        handleCloseNumberedLetter,
        loadingLetterNumber,
        additionalAttachmentsRef,
        referenceRef,
        registrationInfoRef,
        attachmentsRef,
        openPositionEmployeeTreeDialogRecipient,
        handleOpenPositionEmployeeTreeDialogRecipient,
        handleClosePositionEmployeeTreeDialogRecipient,
        openPositionEmployeeTreeDialogSender,
        handleOpenPositionEmployeeTreeDialogSender,
        handleClosePositionEmployeeTreeDialogSender,
        onForward,
        openForwardLetter,
        handleCloseForwardLetter,
        handleChangeForwardTypes,
        forwardType,
        paraph,
        handleChangeParaph,
        openPreParaphList,
        toggleDialogParaphList,
        forwardedRecipientList,
        tabPreParaphValue,
        handleChangeTabPreParaph,
        handleClickPreParaph,
        forwardList,
        deleteForwardListItem,
        organizationId,
        positions,
        employees,
        openPositionEmployeeTreeDialogCC,
        openPositionEmployeeTreeDialogBCC,
        openPositionEmployeeTreeDialogSigner,
        handleOpenPositionEmployeeTreeDialogCC,
        handleClosePositionEmployeeTreeDialogCC,
        handleOpenPositionEmployeeTreeDialogBCC,
        handleClosePositionEmployeeTreeDialogBCC,
        handleOpenPositionEmployeeTreeDialogSigner,
        handleClosePositionEmployeeTreeDialogSigner,
        onSubmitForwardRecipient,
        forwardRecipientSelection,
        handleChangeForwardRecipient,
        addToForwardList,
        handleForwardLetter,
        forwardLetterLoading,
        forwardLetterTag,
        handleChangeForwardLetterTag,
        setOpenPositionEmployeeTreeDialogRecipient,
        openForwardDialogRecipient,
        setOpenForwardDialogRecipient,
        operationInfo,
        handleToggleDialogTerminate,
        handleToggleDialogDiscard,
        showDialogTerminate,
        terminateDescriptionVal,
        handleChangeTerminateDescriptionVal,
        terminateLetter,
        isLoadingTerminate,
        showDialogDiscard,
        discardDescriptionVal,
        handleChangeDiscardDescriptionVal,
        discardLetter,
        isLoadingDiscard,
        openForwardListMobileDialog,
        setOpenForwardListMobileDialog,
        securityOfComposeLetter,
        handleIsDisabledSave,
        handleIsDisabledForward,
        handleIsDisabledNumber,
        handleIsDisabledSign,
        handleIsDisabledEce,
        handleIsDisabledReceipt,
        handleIsDisabledPrint,
        handleIsDisabledDiscard,
        handleIsDisabledTerminate,
        handleIsSingedCurrentLetter,
        handleIsTerminatedCurrentLetter,
        handleIsDiscardedCurrentLetter,
        handleIsDiscardedCurrentLetterByOperations,
        handleAddAttachment,
        handleAddAttachmentAsContent,
        handleDeleteAttachment,
        handleSelectedAttachment,
        setAttachments,
        attachments,
        anchorElLetterInfoMenu,
        openLetterInfoMenu,
        handleOpenLetterInfoMenu,
        handleCloseLetterInfoMenu,
        isVisibleLetterHistoryDialog,
        toggleVisibleLetterHistoryDialog,
        letterHistory,
        letterHistoryLoading,
        setLetterHistoryLoading,
        letterNumber,
        getLetterLayouts,
        letterLayoutsLoading,
        openPrintDialog,
        handleClosePrintDialog,
        letterLayoutsList,
        selectedLetterLayout,
        handleChangeSelectedLetterLayout,
        openSendECEDialog,
        handleOpenSendECEDialog,
        handleCloseSendECEDialog,
        eceEmailRecipient,
        onChangeEceEmailRecipient,
        handleSendECE,
        sendECELoading,
        openExportECEDialog,
        handleOpenExportECEDialog,
        handleCloseExportECEDialog,
        handleExportECE,
        exportECELoading,
        ECEEmailRecipientErrorText,
        setECEEmailRecipientErrorText,
        showDialogNumberedErrorForIndicatorStatus,
        setShowDialogNumberedErrorForIndicatorStatus,
        showDialogSignAndSaveOrCancel,
        setShowDialogSignAndSaveOrCancel,
        handleOpenCcDescription,
        handleCloseCcDescription,
        handleChangeCcDescription,
        ccDescriptionPanel,
        handleCreatedContact,
        handleCloseContactDialog,
        handleOpenContactDialog,
        quicklyAddingContactDialog,
        submitSaveAndSign,
        handleSignLetter,
        checkedSign,
        setCheckedSign,
        checkedStamps,
        setCheckedStamps,
        forwardLetterAttachments,
        setForwardLetterAttachments,
        handleUploadDocument,
        uploadDocumentLoading,
        forwardAttachmentsLoading,
        additionalAttachments,
        setAdditionalAttachments,
        handleAddAdditionalAttachment,
        forwardRecipient,
        voidLetterDialog,
        handleCloseVoidLetterDialog,
        handleOpenVoidLetterDialog,
        onChangeValueNote,
        voidLetterNote,
        submitVoidLetter,
        voidLetterLoading,
        handleDisabledVoid,
        errorOfVoidOperationText,
        errorVoidLetterDialog,
        handleCloseErrorVoidLetterDialog,
        openDocumentPreviewerDialog,
        handleCloseDocumentPreviewer,
        handleOpenDocumentPreviewer,
        secretariatListHasAccess,
        indicatorsListHasAccess,
        createLetterSampleState,
        setCreateLetterSampleState,
        handleCreateLetterSample,
        openForCreateLetterSampleDialog,
        handleOpenCreateLetterSampleDialog,
        handleCloseCreateLetterSampleDialog,
        loadingCreateLetterSample,
        letterSamplesListAnchorElAnchorEl,
        handleOpenLetterSamplesListPopover,
        handleCloseLetterSamplesPopover,
        correspondenceSamplesListLoading,
        correspondenceSamplesList,
        deleteCorrespondenceSample,
        applyCorrespondenceSample,
        deleteCorrespondenceSampleLoading,
        showFieldsReferenceLetter,
        handleShowReferenceLetter,
        getReferenceLettersList,
        searchReferencesLettersLoading,
        setReferenceLetterNumber,
        referenceLetterNumberList,
        referenceSearchPopoverOpen,
        handleCloseReferenceSearchPopover,
        handleSelectReferenceSearchPopover,
        handleNavigateToReference,
        resetSelectedReferenceLetter,
        sendEceReceipt,
        composeInfoConfig,
        isOpenComposeInfoConfig,
        handleComposeInfoConfigDialog,
        handleSubmitComposeInfoConfig,
        onChangeComposeInfoConfig,
        openLetterAiAnchorElement,
        handleCloseLetterAiPopover,
        handleClickLetterAiPopover,
        letterAiAnchorEl,
        selectedTabCreateLetterByTopic,
        handleToggleChangeTabGenai,
        setTextFieldGenai,
        submitCreateLetterByTopic,
        genaiLoading,
        textFieldGenai,
        isEceLetter,
        isIncomingNumberAndDateDisable,
        showDialogNumberRepetitive,
        setShowDialogNumberRepetitive,
        confirmIncomingNumberRepetitive,
        closeDialogNumberRepetitive,
        activeSecretariats,
        currentSecretariat,
        forwardNotesSamples,
        handleDeleteForwardNotesSamples,
        selectedRadio,
        handleChangePreParaphRadio,
        toggleDialogCreateParaph,
        openPreCreateParaph,
        handleCreateForwardNotesSample,
        isSubmittingForwardNoteSample,
        deleteForwardNoteSampleLoading,
        isLoadingForwardNoteSamples,
        validForwardNotesSample,
        isSubmitDisabled,
      }}
    >
      <RouterPrompt isBlocking={isBlocking} />
      <LetterMedia />
    </LetterContext.Provider>
  );
};

const OtherLetterWithValidation = withValidation({ initialState, schema, comparers })(
  LetterValidation,
);
const IncomingLetterWithValidation = withValidation({
  initialState,
  schema: schemaForIncomingLetter,
  comparers,
})(LetterValidation);

const ValidationLetter = (props) => {
  const { typeOfLetter } = props;
  if (typeOfLetter === 'incoming') return <IncomingLetterWithValidation {...props} />;
  return <OtherLetterWithValidation {...props} />;
};

export default ValidationLetter;
