apiVersion: v1
kind: Namespace
metadata:
  name: develop
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: com-chargoon-cloud-front-spa
  namespace: develop
  labels:
    app: com-chargoon-cloud-front-spa
spec:
  replicas: 2
  selector:
    matchLabels:
      app: com-chargoon-cloud-front-spa
  template:
    metadata:
      labels:
        app: com-chargoon-cloud-front-spa
    spec:
      imagePullSecrets:
        - name: dockerregistry
      containers:
        - name: com-chargoon-cloud-front
          image: localhost:32001/com.chargoon.cloud.front.spa:0.0.90
          imagePullPolicy: Always
          resources:
            requests:
              memory: "100Mi"
              cpu: "50m"
            limits:
              memory: "100Mi"
              cpu: "50m"
          ports:
            - containerPort: 80
          volumeMounts:
          - name: nginx-configmap
            mountPath: /etc/nginx/conf.d/default.conf
            subPath: default.conf
      volumes:
      - name: nginx-configmap
        configMap:
          name: nginx-config
      restartPolicy: Always
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: develop
data:
  default.conf: |
    server {
        listen       80;
        listen  [::]:80;
        server_name  localhost;
        client_max_body_size 30M;

        underscores_in_headers on;

        #charset koi8-r;
        #access_log  /var/log/nginx/host.access.log  main;

        location /api/v1/ {
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $http_host;
            proxy_pass http://com-chargoon-cloud-svc-gw:3000/api/v1/;
            proxy_redirect off;
            proxy_buffering off;
        }

        location /api/v1/genai/ {
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header Host $http_host;
            proxy_pass http://com-chargoon-cloud-svc-gw:3000/api/v1/genai/;
            proxy_redirect off;
            proxy_buffering off;

            proxy_connect_timeout 300s;
            proxy_send_timeout 300s;
            proxy_read_timeout 300s;
        }

        location / {
          root   /usr/share/nginx/html;
          index  index.html index.htm;
          try_files $uri $uri/ /index.html;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        #error_page   500 502 503 504  /50x.html;
        #location = /50x.html {
        #    root   /usr/share/nginx/html;
        #}

        # proxy the PHP scripts to Apache listening on 127.0.0.1:80
        #
        #location ~ \.php$ {
        #    proxy_pass   http://127.0.0.1;
        #}

        # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
        #
        #location ~ \.php$ {
        #    root           html;
        #    fastcgi_pass   127.0.0.1:9000;
        #    fastcgi_index  index.php;
        #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
        #    include        fastcgi_params;
        #}

        # deny access to .htaccess files, if Apache's document root
        # concurs with nginx's one
        #
        #location ~ /\.ht {
        #    deny  all;
        #}
    }
---
kind: Service
apiVersion: v1
metadata:
  name: com-chargoon-cloud-front-spa
  namespace: develop
  labels:
    app: com-chargoon-cloud-front-spa
spec:
  type: NodePort
  ports:
    - port: 80
      targetPort: 80
      protocol: TCP
      nodePort: 31002
  selector:
    app: com-chargoon-cloud-front-spa
