import { useEffect, useState } from 'react';

import Grid from '@mui/material/Grid';
import LinearProgress from '@mui/material/LinearProgress';
import { makeStyles } from 'tss-react/mui';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';
import Jo<PERSON> from 'joi';
import { connect } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';
import { useNavigate, useLocation } from 'react-router-dom';

import { convertDigitsToEnglish } from 'utils';
import { RecoverPasswordContext } from 'contexts/pageContext/auth/recoverPasswordContext';
import { ReactComponent as RecoverPasswordIcon } from 'assets/images/forms/recoverPassword.svg';
import { encryptPassword } from 'utils/encryption';
import { isMobileValid } from 'utils/isMobileValid';
import { withValidation, extendedJoi } from 'common/validation';
import {
  otpAsync as otpAsync_,
  userInfoSet as userInfoSet_,
  otpReset as otpReset_,
  recoverPasswordByMobileAsync as recoverPasswordByMobileAsync_,
  recoverPasswordByMobileReset as recoverPasswordByMobileReset_,
} from '../authSlice';
import GetMobile from './GetMobile';
import ConfirmPhone from './ConfirmPhone';
import RecoverPasswordSuccess from './RecoverPasswordSuccess';

const useStyles = makeStyles()({
  root: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    alignItems: 'center',
  },
  lineProgressRoot: {
    height: 8,
    backgroundColor: '#f3f3f3',
  },
  lineProgressBar: {
    borderRadius: 4,
  },
  progressBar: {
    width: '100%',
    position: 'absolute',
    top: 0,
  },
  mainForm: {
    display: 'flex',
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    maxWidth: 1280,
  },
  forms: {
    height: '100%',
  },
  image: {},
});

const RecoverPassword = ({
  auth,
  otpAsync,
  otpReset,
  recoverPasswordByMobileAsync,
  recoverPasswordByMobileReset,
  userInfoSet,
  validationOnChange,
  validationOnSubmit,
  getValidationErrorEx,
  getValidationError,
  isInvalid,
}) => {
  const { classes } = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const [step, setStep] = useState(1);
  const [submitedOtp, setSubmitedOtp] = useState(false);
  const [state, setState] = useState({});
  const [confirmCode, setConfirmCode] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isChangeSuccessFull, setIsChangeSuccessFull] = useState(false);
  const [counter, setCounter] = useState(120);
  const [loadingForgetPassword, setLoadingForgetPassword] = useState(false);
  const [loadingConfirmPhone, setLoadingConfirmPhone] = useState(false);
  const [phonErrorText, setPhonErrorText] = useState('');
  const [passwordError, setPasswordError] = useState(false);

  useEffect(() => {
    setIsChangeSuccessFull(false);
    setSubmitedOtp(false);
    setConfirmCode('');
  }, [location]);

  useEffect(() => {
    let interval;

    if (step === '2') {
      interval = setInterval(() => {
        if (counter > 0) setCounter(counter - 1);
      }, 1000);
    }

    return () => {
      clearInterval(interval);
    };
  }, [counter, step]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const stp = params.get('step');
    setStep(stp);
  }, [location.search]);

  //   ---------------------------get mobile---------------------------------
  const goToLogin = () => {
    setIsChangeSuccessFull(false);
    navigate('/login');
  };

  const onChangeConfirmCode = (e) => {
    setConfirmCode(e.target.value);
  };

  const onChange = (e) => {
    validationOnChange(e);
    const stt = { ...state };
    stt[e.target.name] = e.target.value;
    setState(stt);
  };

  useEffect(() => {
    setLoadingForgetPassword(false);

    if (submitedOtp && auth.otp.data?.success) {
      userInfoSet({ mobile: state.mobile });
      navigate({
        pathname: '/recover-password',
        search: '?step=2',
      });
    }
  }, [auth.otp.data, auth.otp.status, navigate, state.mobile, submitedOtp, userInfoSet]);

  const submitForgetPassword = (e) => {
    e.preventDefault();
    setPasswordError(false);
    setPhonErrorText('');
    if (validationOnSubmit(state) && !isInvalid('mobile') && !isInvalid('password')) {
      const realNumber = state.mobile.split(' ').join('').replace(/_/g, '');

      if (!isMobileValid(realNumber)) {
        setPhonErrorText(t('auth.messages.mobileInvalid'));
      }

      if (realNumber && realNumber.length === 11) {
        setLoadingForgetPassword(true);
        const options = {
          data: {
            id: uuidv4(),
            mobile: state.mobile,
          },
        };
        setSubmitedOtp(true);
        otpAsync(options);
        setCounter(120);
      }
    }
  };

  const onShowPassword = () => {
    setShowPassword(!showPassword);
  };

  //   ---------------------------------confirm code--------------------------
  const editInfo = () => {
    otpReset();
    setState({ mobile: state.mobile, password: '' });
    setConfirmCode('');
    onChange({ target: { name: 'password', value: '' } });
    navigate({
      pathname: '/recover-password',
      search: '?step=1',
    });
  };

  useEffect(() => {
    if (auth.recoverPasswordByMobile.success) {
      setIsChangeSuccessFull(auth.recoverPasswordByMobile.success);
      otpReset();
      setState({ mobile: '', password: '' });
      setConfirmCode('');
    }
  }, [auth.recoverPasswordByMobile.success, otpReset]);

  useEffect(() => {
    setLoadingConfirmPhone(auth.recoverPasswordByMobile.status === 'loading');
  }, [auth.recoverPasswordByMobile.status]);

  useEffect(() => {
    if (isChangeSuccessFull) {
      navigate(
        {
          pathname: '/recover-password',
          search: '?step=3',
        },
        { replace: true },
      );
    }
  }, [isChangeSuccessFull, navigate]);

  useEffect(() => {
    if (!auth.userInfo?.mobile) {
      navigate('/recover-password?step=1');
    }
  }, [auth.userInfo?.mobile, navigate]);

  useEffect(() => {
    if (confirmCode.length === 0) {
      recoverPasswordByMobileReset();
    }
  }, [confirmCode, recoverPasswordByMobileReset]);

  const submitConfirmCode = (e) => {
    e.preventDefault();
    const options = {
      data: {
        mobile: auth.otp.data?.mobile,
        password: encryptPassword(state.password),
      },
      headers: {
        [`otp_${auth.otp.data.mobile}_${auth.otp.data.id}`]: convertDigitsToEnglish(confirmCode),
      },
    };
    recoverPasswordByMobileAsync(options);
  };

  const resendOtp = () => {
    setCounter(120);
    setConfirmCode('');
    if (state.password) {
      const options = {
        data: {
          id: uuidv4(),
          mobile: state.mobile.split(' ').join(''),
        },
      };
      otpAsync(options);
    }
  };

  const linearProgressValue = (_step) => {
    if (_step === '1') return 0;
    if (_step === '2') return 50;
    if (_step === '3') return 100;

    return 0;
  };

  return (
    <RecoverPasswordContext.Provider
      value={{
        auth,
        getValidationErrorEx,
        getValidationError,
        isInvalid,
        state,
        onChange,
        showPassword,
        onShowPassword,
        goToLogin,
        submitForgetPassword,
        setState,
        counter,
        editInfo,
        submitConfirmCode,
        resendOtp,
        phonErrorText,
        passwordError,
        setPasswordError,
        onChangeConfirmCode,
        confirmCode,
        loadingForgetPassword,
        loadingConfirmPhone,
      }}
    >
      <div className={classes.root}>
        <div className={classes.progressBar}>
          <LinearProgress
            classes={{ root: classes.lineProgressRoot, bar: classes.lineProgressBar }}
            variant="determinate"
            value={linearProgressValue(step)}
          />
        </div>
        <Grid container className={classes.mainForm} columns={16}>
          <Grid item lg={9} display={{ xs: 'none', lg: 'block' }}>
            <RecoverPasswordIcon className={classes.image} />
          </Grid>
          <Grid item xs={16} lg={7} className={classes.forms}>
            {step === '1' && <GetMobile />}
            {step === '2' && <ConfirmPhone />}
            {step === '3' && <RecoverPasswordSuccess />}
          </Grid>
        </Grid>
      </div>
    </RecoverPasswordContext.Provider>
  );
};

const mapStateToProps = (state) => {
  const { auth } = state;
  return {
    auth,
  };
};
const initialState = {
  mobile: '',
  password: '',
};

const schema = {
  mobile: Joi.string()
    .required()
    .min(11)
    .max(11)
    .pattern(/^09/)
    .message({
      'string.pattern.base': i18n.t('auth.messages.mobileInvalid'),
    }),
  password: extendedJoi.passwordComplexity().required(),
};

RecoverPassword.propTypes = {
  auth: PropTypes.shape({
    loginByPassword: PropTypes.shape({
      status: PropTypes.string,
    }),
    otp: PropTypes.shape({
      status: PropTypes.string,
      data: PropTypes.shape({
        success: PropTypes.bool,
        mobile: PropTypes.string,
        id: PropTypes.string,
      }),
    }),
    recoverPasswordByMobile: PropTypes.shape({
      correlationId: PropTypes.string,
      status: PropTypes.string,
      success: PropTypes.bool,
      data: PropTypes.shape({
        meta: PropTypes.shape({
          correlationId: PropTypes.string,
        }),
        mobile: PropTypes.string,
        success: PropTypes.bool,
      }),
    }),
    registerUser: PropTypes.shape({
      status: PropTypes.string,
    }),
    userInfo: PropTypes.shape({
      mobile: PropTypes.string,
    }),
    username: PropTypes.shape({
      status: PropTypes.string,
    }),
  }).isRequired,
  getValidationErrorEx: PropTypes.func.isRequired,
  getValidationError: PropTypes.func.isRequired,
  isInvalid: PropTypes.func.isRequired,
  validationOnChange: PropTypes.func.isRequired,
  validationOnSubmit: PropTypes.func.isRequired,
  otpAsync: PropTypes.func.isRequired,
  otpReset: PropTypes.func.isRequired,
  recoverPasswordByMobileAsync: PropTypes.func.isRequired,
  recoverPasswordByMobileReset: PropTypes.func.isRequired,
  userInfoSet: PropTypes.func.isRequired,
};

export default withValidation({ initialState, schema })(
  connect(mapStateToProps, {
    otpAsync: otpAsync_,
    userInfoSet: userInfoSet_,
    otpReset: otpReset_,
    recoverPasswordByMobileAsync: recoverPasswordByMobileAsync_,
    recoverPasswordByMobileReset: recoverPasswordByMobileReset_,
  })(RecoverPassword),
);
