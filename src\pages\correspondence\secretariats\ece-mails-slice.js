import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import Services3 from 'api/v1/services/index';
import { delay } from 'utils/delay-method';

const apiVersion = '/api/v1/';

const initialState = {
  sendECE: {
    status: 'idle',
    data: null,
  },
  incomingEceMailsList: {
    data: null,
    status: 'idle',
    meta: null,
  },
  outgoingEceMailsList: {
    data: null,
    status: 'idle',
    meta: null,
  },
  eceMail: {
    status: 'idle',
    data: null,
  },
  convertMailToLetter: {
    status: 'idle',
    data: null,
  },
  eceReceipt: {
    status: 'idle',
    data: null,
    success: false,
  },
  eceReceiptMailsList: {
    status: 'idle',
    data: null,
    meta: null,
    success: false,
  },
  eceReceiptMail: {
    status: 'idle',
    data: null,
    success: false,
  },
  parsedEceReceipt: {
    status: 'idle',
    data: null,
    success: false,
  },
  retryEceReceipt: {
    status: 'idle',
    data: null,
    success: false,
  },
};

export const sendECEAsync = createAsyncThunk('eceMails/sendECE', async (args) => {
  const { data, checkStatusOveredFunction } = args;
  const options = {
    apiVersion,
    domainRoot: 'ecemails',
    method: 'sendECE',
    checkStatusOveredFunction,
    commandName: 'ecemails.EceConvertedToLetter',
    path: args.path,
  };
  const response = await Services3({ options, data });
  return response;
});

export const incomingEceMailsAsync = createAsyncThunk(
  'eceMails/incomingEceMailsList',
  async (options) => {
    const { secretariatId, limit, offset } = options;
    const opts = {
      apiVersion,
      domainRoot: 'ecemails',
      method: 'incomingEceMailsList',
      path: `secretariat/${secretariatId}`,
      queryObject: { limit, offset, type: 'incoming' },
    };
    const response = await Services3({ options: opts });
    return response;
  },
);

export const outgoingEceMailsAsync = createAsyncThunk(
  'eceMails/outgoingEceMailsList',
  async (options) => {
    const { secretariatId, limit, offset } = options;
    const opts = {
      apiVersion,
      domainRoot: 'ecemails',
      method: 'outgoingEceMailsList',
      path: `secretariat/${secretariatId}`,
      queryObject: { limit, offset, type: 'outgoing' },
    };
    const response = await Services3({ options: opts });
    return response;
  },
);

export const eceMailAsync = createAsyncThunk('eceMails/eceMail', async (options) => {
  const { mailId, secretariatId } = options;
  const opts = {
    apiVersion,
    domainRoot: 'ecemails',
    method: 'eceMail',
    path: `${mailId}`,
    queryObject: { secretariatId },
  };
  const response = await Services3({ options: opts });
  return response;
});

export const convertMailToLetterAsync = createAsyncThunk(
  'eceMails/convertMailToLetter',
  async (options) => {
    const { mailId, showSnackBar } = options;
    const opts = {
      apiVersion,
      domainRoot: 'ecemails',
      method: 'convertMailToLetter',
      showSnackBar: showSnackBar ?? true,
      path: `${mailId}/convert-to-letter`,
    };
    const response = await Services3({ options: opts });
    return response;
  },
);

export const eceReceiptAsync = createAsyncThunk(
  'eceMails/eceReceipt',
  async ({ letterId, requester }) => {
    const options = {
      apiVersion,
      domainRoot: 'ecemails',
      method: 'receipts',
    };
    const response = await Services3({ options, data: { id: letterId, requester } });
    return response;
  },
);

export const getEceReceiptMailsListAsync = createAsyncThunk(
  'eceMails/eceReceiptMails',
  async ({ secretariatId, limit, offset, type }) => {
    const options = {
      apiVersion,
      domainRoot: 'ecemails',
      method: 'eceReceiptMails',
      path: `receipts/secretariat/${secretariatId}`,
      queryObject: { limit, offset, type },
    };
    const response = await Services3({ options });
    return response;
  },
);

export const getEceReceiptMailAsync = createAsyncThunk(
  'eceMails/eceReceiptMail',
  async ({ receiptId }) => {
    const options = {
      apiVersion,
      domainRoot: 'ecemails',
      method: 'eceReceiptMail',
      path: `receipts/${receiptId}`,
    };
    const response = await Services3({ options });
    return response;
  },
);

export const retryEceReceiptAsync = createAsyncThunk('eceMails/retryEceReceipt', async (data) => {
  const { receipt } = data;
  const options = {
    apiVersion,
    domainRoot: 'ecemails',
    method: 'retryEceReceipt',
    path: `receipts/${receipt.id}/retry`,
  };
  const response = await Services3({ options, data });
  return response;
});

export const eceMailsSlice = createSlice({
  name: 'eceMails',
  initialState,
  reducers: {
    sendECEReset: (state) => {
      const st = state;
      st.sendECE = initialState.sendECE;
    },
    incomingEceMailsReset: (state) => {
      const st = state;
      st.incomingEceMailsList = initialState.incomingEceMailsList;
    },
    outgoingEceMailsReset: (state) => {
      const st = state;
      st.outgoingEceMailsList = initialState.outgoingEceMailsList;
    },
    eceMailReset: (state) => {
      const st = state;
      st.eceMail = initialState.eceMail;
    },
    convertMailToLetterReset: (state) => {
      const st = state;
      st.eceMail = initialState.convertMailToLetter;
    },
    getEceReceiptMailsListReset: (state) => {
      const st = state;
      st.eceReceiptMailsList = initialState.eceReceiptMailsList;
    },
    sentEceReceiptMailReset: (state) => {
      const st = state;
      st.eceReceiptMail = initialState.eceReceiptMail;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(sendECEAsync.pending, (state) => {
        const st = state;
        st.sendECE.status = 'loading';
      })
      .addCase(sendECEAsync.fulfilled, (state, action) => {
        const st = state;
        st.sendECE.status = 'idle';
        st.sendECE.data = action.payload;
      })
      .addCase(sendECEAsync.rejected, (state, action) => {
        const st = state;
        st.sendECE.status = 'idle';
        st.sendECE.data = { success: false };
      })
      .addCase(incomingEceMailsAsync.pending, (state) => {
        const st = state;
        st.incomingEceMailsList.status = 'loading';
      })
      .addCase(incomingEceMailsAsync.fulfilled, (state, action) => {
        const st = state;
        st.incomingEceMailsList.status = 'idle';

        if (action.payload?.status) {
          st.incomingEceMailsList.data = action.payload.data;
        } else {
          st.incomingEceMailsList.data = null;
        }
        st.incomingEceMailsList.meta = action.payload.meta;
      })
      .addCase(incomingEceMailsAsync.rejected, (state) => {
        const st = state;
        st.incomingEceMailsList.status = 'idle';
        st.incomingEceMailsList = {
          ...initialState.incomingEceMailsList,
          success: false,
        };
      })
      .addCase(outgoingEceMailsAsync.pending, (state) => {
        const st = state;
        st.outgoingEceMailsList.status = 'loading';
      })
      .addCase(outgoingEceMailsAsync.fulfilled, (state, action) => {
        const st = state;
        st.outgoingEceMailsList.status = 'idle';

        if (action.payload?.status) {
          st.outgoingEceMailsList.data = action.payload.data;
        } else {
          st.outgoingEceMailsList.data = null;
        }
        st.outgoingEceMailsList.meta = action.payload.meta;
      })
      .addCase(outgoingEceMailsAsync.rejected, (state) => {
        const st = state;
        st.outgoingEceMailsList.status = 'idle';
        st.outgoingEceMailsList = {
          ...initialState.outgoingEceMailsList,
          success: false,
        };
      })
      .addCase(eceMailAsync.pending, (state) => {
        const st = state;
        st.eceMail.status = 'loading';
      })
      .addCase(eceMailAsync.fulfilled, (state, action) => {
        const st = state;
        st.eceMail.status = 'idle';
        st.eceMail.data = action.payload.data;
        st.eceMail.meta = action.payload.meta;
      })
      .addCase(eceMailAsync.rejected, (state) => {
        const st = state;
        st.eceMail.status = 'idle';
        st.eceMail = { success: false };
      })
      .addCase(convertMailToLetterAsync.pending, (state) => {
        const st = state;
        st.convertMailToLetter.status = 'loading';
      })
      .addCase(convertMailToLetterAsync.fulfilled, (state, action) => {
        const st = state;
        st.convertMailToLetter.status = 'idle';
        st.convertMailToLetter.data = action.payload.data;
        st.convertMailToLetter.meta = action.payload.meta;
      })
      .addCase(convertMailToLetterAsync.rejected, (state) => {
        const st = state;
        st.convertMailToLetter.status = 'idle';
        st.convertMailToLetter = { success: false };
      })
      .addCase(eceReceiptAsync.pending, (state) => {
        const st = state;
        st.eceReceipt.status = 'pending';
      })
      .addCase(eceReceiptAsync.fulfilled, (state, action) => {
        const st = state;
        st.eceReceipt.status = 'idle';
        st.eceReceipt.data = action.payload.data;
      })
      .addCase(getEceReceiptMailsListAsync.pending, (state) => {
        const st = state;
        st.eceReceiptMailsList.status = 'pending';
      })
      .addCase(getEceReceiptMailsListAsync.fulfilled, (state, action) => {
        const st = state;
        st.eceReceiptMailsList.status = 'idle';
        st.eceReceiptMailsList.data = action.payload.data;
        st.eceReceiptMailsList.meta = action.payload.meta;
        st.eceReceiptMailsList.success = action.payload.success;
      })
      .addCase(getEceReceiptMailAsync.pending, (state) => {
        const st = state;
        st.eceReceiptMail.status = 'pending';
      })
      .addCase(getEceReceiptMailAsync.fulfilled, (state, action) => {
        const st = state;
        st.eceReceiptMail.status = 'idle';
        st.eceReceiptMail.data = action.payload.data;
        st.eceReceiptMail.meta = action.payload.meta;
        st.eceReceiptMail.success = action.payload.success;
      })
      .addCase(retryEceReceiptAsync.pending, (state) => {
        const st = state;
        st.retryEceReceipt.status = 'pending';
      })
      .addCase(retryEceReceiptAsync.fulfilled, (state, action) => {
        const st = state;
        st.retryEceReceipt.status = 'idle';
        st.retryEceReceipt.data = action.payload.data;
        st.retryEceReceipt.success = action.payload.success;
      });
  },
});

export const {
  sendECEReset,
  incomingEceMailsReset,
  outgoingEceMailsReset,
  eceMailReset,
  convertMailToLetterReset,
  getEceReceiptMailsListReset,
  sentEceReceiptMailReset,
} = eceMailsSlice.actions;

export default eceMailsSlice.reducer;
