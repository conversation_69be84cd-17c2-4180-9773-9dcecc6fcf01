import React from 'react';

import FormControl from '@mui/material/FormControl';
import InputLabel from '@mui/material/InputLabel';
import MenuItem from '@mui/material/MenuItem';
import Select from '@mui/material/Select';
import { t } from 'i18next';
import { useLayoutEditorPageContext } from '../../../../../../contexts/pageContext/secretariat/layout-editor-page/layout-editor-page-context';

const FontFaceSelect = () => {
  const { fontFamilySelect, handleChangeFontFamily } = useLayoutEditorPageContext();

  const fonts = [
    'vazir',
    'lotus',
    'sahel',
    'myriad-pro',
    'yaghot',
    'yaghotBold',
    'mitra',
    'nazanin',
    'yekan',
    'b-traffic',
    'b-titr',
    'b-zar',
  ];

  return (
    <FormControl fullWidth>
      <InputLabel id="demo-simple-select-label">
        {t('behaviorToken.labels.fontFamilySelect')}
      </InputLabel>
      <Select
        labelId="demo-simple-select-label"
        id="demo-simple-select"
        value={fontFamilySelect || ''}
        label="Age"
        onChange={handleChangeFontFamily}
      >
        {fonts.map((font) => (
          <MenuItem value={font} key={font}>
            {t(`fonts.labels.${font}`)}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default FontFaceSelect;
