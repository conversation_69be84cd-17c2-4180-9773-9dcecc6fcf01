import React from 'react';

import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';

import { useLetter } from '../../../../../../../contexts/pageContext/letters/letterContext';

const useStyles = makeStyles()((theme) => ({
  flex: {
    display: 'flex',
    alignItems: 'center',
  },
  formLabel: {
    fontSize: '14px',
    fontWeight: 700,
    color: `${theme.palette.text.dark} !important`,
  },
  formControlLabel: {
    fontSize: '14px',
    fontWeigth: 400,
  },
  defalutLetterLayout: {
    fontSize: '12px',
    fontWeight: 700,
    color: theme.palette.background.lightGreen,
    padding: theme.spacing(0.5),
    borderRadius: '6px',
    background: '#F3FFF7',
  },
}));

const LetterLayoutListFormControl = ({ label }: { label?: string }) => {
  const { letterLayoutsList, selectedLetterLayout, handleChangeSelectedLetterLayout } = useLetter();
  const { classes } = useStyles();
  const { t } = useTranslation();

  return (
    <FormControl>
      <FormLabel id="letter-layout-form-label" classes={{ root: classes.formLabel }}>
        {label || t('correspondence.letters.labels.choose-letter-layout')}
      </FormLabel>
      <RadioGroup
        value={selectedLetterLayout}
        aria-labelledby="radio-buttons-group-label"
        name="radio-buttons-group"
        onChange={handleChangeSelectedLetterLayout}
      >
        <FormControlLabel
          value="no-layout"
          classes={{ label: classes.formControlLabel }}
          control={<Radio />}
          label={t('correspondence.letters.labels.no-letter-layout')}
        />
        {letterLayoutsList.length > 0 &&
          letterLayoutsList.map((letterLayout: any) => (
            <Box className={classes.flex}>
              <FormControlLabel
                value={letterLayout.layoutId}
                classes={{ label: classes.formControlLabel }}
                control={<Radio />}
                label={`${letterLayout.name} - ${letterLayout.size}`}
              />
              {letterLayout?.isDefault && (
                <Typography variant="body1" className={classes.defalutLetterLayout}>
                  {t('correspondence.letters.labels.default-letter-layout')}
                </Typography>
              )}
            </Box>
          ))}
      </RadioGroup>
    </FormControl>
  );
};

export default LetterLayoutListFormControl;
