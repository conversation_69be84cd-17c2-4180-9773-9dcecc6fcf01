import { makeStyles } from 'tss-react/mui';
import Typography from '@mui/material/Typography';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import IconButton from '@mui/material/IconButton';
import TabContext from '@mui/lab/TabContext';
import TabPanel from '@mui/lab/TabPanel';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import RateReviewOutlinedIcon from '@mui/icons-material/RateReviewOutlined';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import KeyboardReturn from '@mui/icons-material/KeyboardReturn';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import CommentOutlinedIcon from '@mui/icons-material/CommentOutlined';

import { t } from 'i18next';
import DocumentViewer from 'components/document-previewer';
import MainInfo from './components/main-info';
import AttachMents from './components/attachments-mobile';
import RegistrationInfo from './components/registration-info';
import Reference from './components/reference';
import AdditionalAttachments from './components/additional-attachments';
import ExtraButtons from './components/extra-buttons';
import BackdropCom from '../../../../components/backdrop';
import DialogDetails from './components/dialog-details';
import DialogSaveAndSetLetterNumber from './components/dialog-save-set-letter-number';
import DialogNumberedLetter from './components/dialog-numbered-letter';
import { useLetter } from '../../../../contexts/pageContext/letters/letterContext';
import DialogForwardLetter from './components/dialog-forward-letter';
import DialogPreParaphList from './components/forward-letter/components/dialog-pre-paraph-list';
import Save from './components/extraButtons/save';
import LetterHistoryDialog from './components/dialog/letter-history';
import LetterInfoMenu from './components/letter-info-menu';
import useStyles from './components/extraButtons/styles';
import PrintLetterDialog from './components/dialog/print-letter/print-letter-dialog';
import SendECEDialog from './components/dialog/send-ece/send-ece-dialog';
import ExportECEDialog from './components/dialog/export-ece/export-ece-dialog';
import AWBox from '../../../../components/AWComponents/AWBox';
import WarningDialogForNumberForIndicatorStatus from './components/dialog/number-error/number-error-dialog';
import { formatLetterNumber } from '../../../../utils/letterNumberUtils';
import DialogSignAndSaveOrCancel from './components/dialog-sign-and-save-or-cancel';
import DialogTerminateLetter from './components/dialog-terminate-letter';
import DialogDiscardLetter from './components/dialog-discard-letter';
import ContactsDialog from '../../../management-center/manageContacts/components/contactDialog';
import ConfirmationVoidLetterDialog from './components/dialog/void-letter/confirmation-void-letter-dialog';
import ErrorOfVoidOperationDialog from './components/dialog/void-letter/error-of-void-operation-dialog';
import Loading from '../../../../components/Loading';
import WarningDialogNumberRepetitive from './components/dialog/numberRepetitive';
import DialogParaphCreator from './components/forward-letter/components/dialog-paraph-creator';

const useStyles2 = makeStyles()((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    maxHeight: '100%',
    paddingBottom: '78px',
  },
  tabsRoot: {
    height: 54,
    borderBottom: 'solid 1px #d1d1d1',
  },
  tabsFlexContainer: {
    height: 54,
    padding: '0 16px',
  },
  tabRoot: {
    minWidth: 45,
    flex: 1,
  },
  tabWrapper: {
    flexDirection: 'row',
    fontWeight: 'bold',
    '& *:first-child': {
      margin: '0 2px 0 0 !important',
    },
  },
  indicator: {
    flex: '1 1 30%',
    background: '#f8f8f8',
    color: theme.palette.background.secondColor,
    minHeight: 54,
    fontSize: 12,
    flexDirection: 'row',
    justifyContent: 'center',
    '& svg': {
      marginLeft: 4,
    },
  },
  actionBox: {
    position: 'fixed',
    bottom: 0,
    width: '100%',
    height: 85,
  },
  actionBoxContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 8,
    borderRadius: 12,
    boxShadow: '0 0 3px 0 rgb(0 0 0 / 16%)',
    backgroundColor: '#fff',
    margin: '0 16px',
    '& button': {
      padding: '0 12px',
    },
  },
  extraButtons: {
    display: 'flex',
  },

  tabItem: {
    overflowY: 'auto',
    flex: 1,
    padding: theme.spacing(0, 1),
  },
  letterStatusBtn: {
    display: 'flex',
    justifyContent: 'space-between',
    height: 50,
    borderBottom: '1px solid #d1d1d1',
    borderRadius: 0,
    padding: '0 16px',
    color: '#111111',
  },
  letterStatusText: {
    display: 'flex',
    fontSize: 'unset!important',
    '& svg': {
      color: theme.palette.primary.main,
      marginLeft: theme.spacing(1),
    },
  },
  padding: {
    padding: '16px 24px',
  },
  infoBox: {
    display: 'flex',
    justifyContent: 'space-between',
    padding: theme.spacing(0.5, 1),
  },
  letterNumberArea: {
    display: 'flex',
    alignItems: 'center',
  },
  letterInfoButton: {
    // padding: [theme.spacing(0.5), '!important'],
    margin: theme.spacing(1, 0),
  },
  label: {
    fontSize: '14px',
    color: theme.palette.text.gray,
    marginLeft: theme.spacing(0.5),
  },
  letterNumber: {
    fontSize: '14px',
    fontWeight: 500,
  },
}));

const MobileLetter = () => {
  const {
    tab,
    openForwardLetter,
    onChangeTab,
    handleOpenExtraButton,
    openBackdrop,
    openDetails,
    showDialogSaveAndSetLetterNumber,
    showDialogNumberedLetter,
    openPreParaphList,
    onForward,
    type,
    selectedLetter,
    handleOpenLetterInfoMenu,
    handleIsDisabledForward,
    letterNumber,
    showDialogNumberedErrorForIndicatorStatus,
    setShowDialogNumberedErrorForIndicatorStatus,
    showDialogSignAndSaveOrCancel,
    setShowDialogSignAndSaveOrCancel,
    showDialogTerminate,
    showDialogDiscard,
    handleCreatedContact,
    handleCloseContactDialog,
    quicklyAddingContactDialog,
    handleCloseVoidLetterDialog,
    voidLetterDialog,
    voidLetterNote,
    onChangeValueNote,
    submitVoidLetter,
    voidLetterLoading,
    errorOfVoidOperationText,
    handleCloseErrorVoidLetterDialog,
    errorVoidLetterDialog,
    openDocumentPreviewerDialog,
    handleCloseDocumentPreviewer,
    showDialogNumberRepetitive,
    loading,
    openPreCreateParaph,
  } = useLetter();
  const { classes, cx } = useStyles2();
  const { classes: classes2 } = useStyles();

  return loading ? (
    <Loading />
  ) : (
    <>
      <BackdropCom {...{ openBackdrop }} />
      <AWBox className={classes.root}>
        {selectedLetter && (
          <AWBox className={classes.infoBox}>
            <AWBox className={classes.letterNumberArea}>
              <Typography variant="subtitle2" className={classes.label}>
                {t('commands.letters.numberLetter')}:
              </Typography>
              <Typography variant="subtitle2" className={classes.letterNumber}>
                {formatLetterNumber(letterNumber) ?? t('commands.letters.notNumberLetter')}
              </Typography>
            </AWBox>
            <>
              <IconButton
                onClick={handleOpenLetterInfoMenu}
                id="more-info-button"
                className={classes.letterInfoButton}
              >
                <MoreVertIcon />
              </IconButton>
              <LetterInfoMenu isMobile />
            </>
          </AWBox>
        )}
        <TabContext value={tab}>
          <Tabs
            variant="fullWidth"
            value={tab}
            onChange={onChangeTab}
            classes={{ root: classes.tabsRoot, flexContainer: classes.tabsFlexContainer }}
          >
            <Tab
              value="main"
              label={tab === 'main' && t(`solutionHistory.labels.letter.basicInformation`)}
              icon={<RateReviewOutlinedIcon />}
              classes={{
                root: classes.tabRoot,
                wrapper: classes.tabWrapper,
                selected: classes.indicator,
              }}
            />
            <Tab
              value="attachment"
              label={tab === 'attachment' && t('secretariats.labels.attachment')}
              icon={<AttachFileIcon />}
              classes={{
                root: classes.tabRoot,
                wrapper: classes.tabWrapper,
                selected: classes.indicator,
              }}
            />
            <Tab
              value="registration-info"
              label={tab === 'registration-info' && t('secretariats.labels.registerInformation')}
              icon={<EventAvailableIcon />}
              classes={{
                root: classes.tabRoot,
                wrapper: classes.tabWrapper,
                selected: classes.indicator,
              }}
            />
            <Tab
              value="reference"
              label={tab === 'reference' && 'مراجع'}
              icon={<ArrowForwardIcon />}
              classes={{
                root: classes.tabRoot,
                wrapper: classes.tabWrapper,
                selected: classes.indicator,
              }}
            />
            <Tab
              value="additionalAttachments"
              label={
                tab === 'additionalAttachments' && t('secretariats.labels.additionalAttachments')
              }
              icon={<CommentOutlinedIcon />}
              classes={{
                root: classes.tabRoot,
                wrapper: classes.tabWrapper,
                selected: classes.indicator,
              }}
            />
          </Tabs>
          <TabPanel className={classes.tabItem} value="main">
            <MainInfo isMobile />
          </TabPanel>
          <TabPanel className={classes.tabItem} value="attachment">
            <AttachMents />
          </TabPanel>
          <TabPanel className={cx(classes.tabItem, classes.padding)} value="registration-info">
            <RegistrationInfo isMobile />
          </TabPanel>
          <TabPanel className={classes.tabItem} value="reference">
            <Reference />
          </TabPanel>
          <TabPanel className={cx(classes.tabItem, classes.padding)} value="additionalAttachments">
            <AdditionalAttachments />
          </TabPanel>
        </TabContext>
      </AWBox>
      <AWBox className={classes.actionBox}>
        <AWBox className={classes.actionBoxContainer}>
          <AWBox>
            <ExtraButtons {...{ isMobile: true }} />
          </AWBox>
          <AWBox className={classes.extraButtons}>
            <Save />
            <IconButton
              className={classes2.extraIconButtons}
              onClick={onForward}
              color="primary"
              disabled={handleIsDisabledForward()}
            >
              <AWBox className={classes2.extraIconBox}>
                <KeyboardReturn />
              </AWBox>
              <Typography variant="caption">
                {t('solutionHistory.labels.letter.forward')}
              </Typography>
            </IconButton>
          </AWBox>
        </AWBox>
      </AWBox>
      <DialogSignAndSaveOrCancel
        open={showDialogSignAndSaveOrCancel}
        onClose={() => setShowDialogSignAndSaveOrCancel(false)}
      />
      {openDetails && <DialogDetails {...{ isMobile: true }} />}
      <LetterHistoryDialog {...{ isMobile: true }} />
      {showDialogSaveAndSetLetterNumber && <DialogSaveAndSetLetterNumber />}
      {openForwardLetter && <DialogForwardLetter isMobile />}
      {openPreParaphList && <DialogPreParaphList isMobile />}
      {openPreCreateParaph && <DialogParaphCreator />}
      {showDialogNumberedLetter && <DialogNumberedLetter />}
      {showDialogTerminate && <DialogTerminateLetter />}
      {showDialogDiscard && <DialogDiscardLetter />}
      {showDialogNumberRepetitive && <WarningDialogNumberRepetitive />}
      <WarningDialogForNumberForIndicatorStatus
        open={showDialogNumberedErrorForIndicatorStatus}
        onClose={() => setShowDialogNumberedErrorForIndicatorStatus(() => false)}
      />
      <PrintLetterDialog />
      <SendECEDialog />
      <ExportECEDialog />
      <ContactsDialog
        onSave={handleCreatedContact}
        onClose={handleCloseContactDialog}
        open={quicklyAddingContactDialog.open}
      />
      <ConfirmationVoidLetterDialog
        onClose={handleCloseVoidLetterDialog}
        open={voidLetterDialog}
        valueNote={voidLetterNote}
        onChangeValueNote={onChangeValueNote}
        submit={submitVoidLetter}
        loading={voidLetterLoading}
      />
      <ErrorOfVoidOperationDialog
        title={errorOfVoidOperationText}
        open={errorVoidLetterDialog}
        onClose={handleCloseErrorVoidLetterDialog}
      />

      {openDocumentPreviewerDialog?.open && (
        <DocumentViewer
          currentIndex={openDocumentPreviewerDialog.currentIndex}
          documents={openDocumentPreviewerDialog.documents}
          open={openDocumentPreviewerDialog.open}
          onClose={handleCloseDocumentPreviewer}
        />
      )}
    </>
  );
};

export default MobileLetter;
