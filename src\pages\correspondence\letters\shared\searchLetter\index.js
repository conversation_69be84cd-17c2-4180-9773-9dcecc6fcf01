import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { useLocation, useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { t } from 'i18next';
import useSecretariatHasAccess from 'hooks/useSecretariatsHasAccess';
import useIndicatorsHasAccess from 'hooks/useIndicatorsHasAccess';
import { useSecurity } from 'common/security/security-context';
import useListener from 'hooks/useListener';
import { employeePositionsUtils } from 'utils/employee-position/makeEmployeePositions';
import { contactInfo } from 'utils/contact-info';
import { userInfo } from 'utils';
import SearchLetterDesktop from './search-letter-desktop';
import SearchLetterMobile from './search-letter-mobile';
import { SearchLetterContext } from '../../../../../contexts/pageContext/letters/searchContext';
import { letterTypes, typeOfLetterList } from '../../letter-utility';
import { organizationEmployeeContactsAsync as organizationEmployeeContactsAsync_ } from '../../../../management-center/employees/employeesSlice';
import {
  contactsOrganizationAsync as contactsOrganizationAsync_,
  resultsSearchReset as resultsSearchReset_,
} from '../../lettersSlice';
import { getOrganizationPositionsAsync as getOrganizationPositionsAsync_ } from '../../../../management-center/positions/positionsSlice';
import { useIsMount } from '../../../../../hooks/useIsMount';
import withSearchLetterSecurity from './with-search-letter-security';
import { decorateSecretariatAndIndicatorParam } from './utils';

const SearchLetter = (props) => {
  const {
    typeOfComponent,
    letters,
    employees,
    positions,
    secretariats,
    correspondenceAuthz,
    organization,
    mobile,
    organizationEmployeeContactsAsync,
    contactsOrganizationAsync,
    getOrganizationPositionsAsync,
    setState,
    state,
    getResultsSearch,
    isCollapsed1,
    resultsSearchReset,
    securityLevelDictionary,
    loading,
  } = props;

  const params = useParams();
  const { organizationId } = params;
  const location = useLocation();
  const navigate = useNavigate();
  const { pathname } = location;
  const [searchParams] = useSearchParams();
  const isMount = useIsMount();
  const { accessibility } = useSecurity();
  const ownerId = organization.selectedOrganization?.owner?.id;
  const additionalItem = { name: t('common.labels.all-secretariats'), id: 'all' };
  const additionalItemForIndicator = { name: t('common.labels.all-indicator'), id: 'all' };
  const [selectedSecretariat, setSelectedSecretariat] = useState();

  //* Secretariats List
  const secretariatListHasAccess = useMemo(() => {
    const organizationSecretariats = secretariats.secretariatsList.data;
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;

    const secretariatList = useSecretariatHasAccess({
      accessibility,
      organizationId,
      organizationSecretariats,
      ownerId,
      secretariatsListSecurityDictionary:
        securityLevelDictionary.secretariatsListSecurityDictionary,
      solutionOrganizationRoles,
      userPermissions,
    });

    const initData = [];

    if (secretariatList?.length === secretariats.secretariatsList?.data?.length) {
      initData.push(additionalItem);
    }

    return [...initData, ...secretariatList];
  }, [
    secretariats.secretariatsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
  ]);

  //* Indicators List
  const indicatorsListHasAccess = useMemo(() => {
    const indicators =
      secretariatListHasAccess.find((item) => item.id === selectedSecretariat?.id)?.indicators ??
      [];
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;

    const indicatorList = useIndicatorsHasAccess({
      accessibility,
      organizationId,
      secretariat: selectedSecretariat,
      indicators,
      ownerId,
      indicatorsListSecurityDictionary: securityLevelDictionary.indicatorsListSecurityDictionary,
      solutionOrganizationRoles,
      userPermissions,
    });

    const initData = [];

    if (Boolean(selectedSecretariat) && selectedSecretariat.id === 'all') {
      initData.push(additionalItemForIndicator);
    }

    return [...initData, ...indicatorList];
  }, [
    secretariatListHasAccess,
    selectedSecretariat,
    secretariats.indicatorsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
  ]);

  const currentListDetector = () => {
    if (pathname.split('/').pop() === 'correspondence') {
      return 'my-cartable';
    }
    return pathname.split('/').pop();
  };

  const currentList =
    typeOfComponent === 'results' ? (searchParams.get('searchIn') ?? 'all') : currentListDetector();
  const currentListLabel = typeOfLetterList.find((t) => t.value === currentList)?.label;

  // searchInList
  const searchInList = useMemo(() => {
    const result =
      typeOfComponent === 'results'
        ? [{ value: 'all', label: t('common.labels.lists') }, ...typeOfLetterList]
        : [
            { value: 'this', label: ` ${t('common.labels.currentList')} ( ${currentListLabel})` },
            { value: 'all', label: t('common.labels.lists') },
          ];

    if (secretariatListHasAccess.length) {
      result.push({
        value: 'secretariats',
        label: t('common.labels.secretariats'),
      });
    }

    return result;
  }, [typeOfComponent, secretariatListHasAccess]);

  const searchInListObj = useMemo(
    () => searchInList.reduce((result, current) => ({ ...result, [current.value]: current }), {}),
    [searchInList],
  );

  const [isFocusSubject, setIsFocusSubject] = useState(false);
  const [searchInValue, setSearchInValue] = useState(searchInList[0]);
  const [typeOfLetterValue, setTypeOfLetterValue] = useState(letterTypes[0]);
  const [subjectValue, setSubjectValue] = useState('');
  const [numberValue, setNumberValue] = useState('');
  const [incomingNumberValue, setIncomingNumberValue] = useState('');
  const [bodyValue, setBodyValue] = useState('');
  const [senderSelection, setSenderSelection] = useState(null);
  const [recipientSelection, setRecipientSelection] = useState(null);
  const [isCollapsed, setIsCollapsed] = useState(isCollapsed1 || false);
  const [openPositionEmployeeTreeDialogSender, setOpenPositionEmployeeTreeDialogSender] =
    useState(false);
  const [openPositionEmployeeTreeDialogRecipient, setOpenPositionEmployeeTreeDialogRecipient] =
    useState(false);
  const [openSearchDrawer, setOpenSearchDrawer] = useState(false);
  const [fromValue, setFromValue] = useState(null);
  const [toValue, setToValue] = useState(null);

  const [selectIndicator, setSelectIndicator] = useState();
  const [isDisabledIndicator, setIsDisabledIndicator] = useState(true);
  const handleFocusSubject = () => {
    if (pathname.split('/').pop() === 'correspondence') {
      navigate(`/organizations/${organizationId}/correspondence`);
    }
    setIsFocusSubject(true);
  };

  const handleChangeSender = (e, newValue) => {
    setSenderSelection(newValue);
  };

  const handleChangeRecipient = (e, newValue) => {
    setRecipientSelection(newValue);
  };

  // --------------get info sender and recipient -------------------------------
  const getOrganizationEmployeeContacts = () => {
    const opts = {
      path: `${organizationId}/employee-contacts`,
      queryObject: { limit: 1000, offset: 0 },
    };
    organizationEmployeeContactsAsync(opts);
  };

  const getContactsOrganization = () => {
    contactsOrganizationAsync({
      path: `${organizationId}`,
      queryObject: {
        limit: 1000,
        offset: 0,
      },
    });
  };

  const getOrganizationPositions = () => {
    getOrganizationPositionsAsync({
      path: `organization/${organizationId}`,
      queryObject: { limit: 1000, offset: 0 },
    });
  };

  useEffect(() => {
    getOrganizationEmployeeContacts();
    getOrganizationPositions();
    setSenderSelection(JSON.parse(searchParams.get('sender')));
    setRecipientSelection(JSON.parse(searchParams.get('recipient')));
    if (typeOfLetterValue.value !== 'internal') {
      getContactsOrganization();
    }
  }, [typeOfLetterValue]);
  // ---------------------------------------------------------------------------

  useEffect(() => {
    if (mobile && pathname.split('/').pop() === 'correspondence') {
      navigate(`/organizations/${organizationId}/correspondence`);
    }

    if (typeOfComponent === 'results' && indicatorsListHasAccess && secretariatListHasAccess) {
      setIsFocusSubject(true);
      setSubjectValue(searchParams.get('subject'));
      setTypeOfLetterValue(
        letterTypes.find((l) => (searchParams.get('typeOfLetter') ?? 'internal') === l.value),
      );

      setSearchInValue(searchInListObj[currentList]);
      // setSearchInValue(searchInList.find((searchItem) => searchItem.value === 'secretariats'));
      setNumberValue(searchParams.get('number'));
      setIncomingNumberValue(searchParams.get('incomingNumber'));
      setFromValue(searchParams.get('from') === 'null' ? null : searchParams.get('from'));
      setToValue(searchParams.get('to') === 'null' ? null : searchParams.get('to'));
      setBodyValue(searchParams.get('body'));

      const currentSecretariat = secretariatListHasAccess.find(
        (item) => item.id === searchParams.get('secretariats'),
      );

      if (currentSecretariat) {
        setSelectedSecretariat(currentSecretariat);
      } else if (secretariatListHasAccess?.length && secretariatListHasAccess[0].id === 'all') {
        setSelectedSecretariat(secretariatListHasAccess[0]);
      }

      const currentIndicator = indicatorsListHasAccess.find(
        (item) => item.id === searchParams.get('indicators'),
      );

      if (currentIndicator) {
        setSelectIndicator(currentIndicator);
      }
    }
  }, []);

  const isSelectedForSearchSecretariats = useMemo(
    () => searchInValue?.value === 'secretariats',
    [searchInValue],
  );

  useEffect(() => {
    let listOfSecretariat;
    let listOfIndicator;

    if (selectedSecretariat && selectedSecretariat.id !== 'all') {
      listOfSecretariat = [{ id: selectedSecretariat.id }];
    }
    if (selectedSecretariat && selectedSecretariat.id !== 'all' && Boolean(selectIndicator)) {
      listOfIndicator = [{ id: selectIndicator.id }];
    }

    if (!isMount) {
      if (typeOfComponent === 'results') {
        setState((prevState) => ({
          ...prevState,
          subject: subjectValue,
          typeOfLetter: typeOfLetterValue,
          searchIn: searchInValue,
          number: numberValue,
          incomingNumber: incomingNumberValue,
          fromValue,
          toValue,
          body: bodyValue,
          sender: senderSelection,
          recipient: recipientSelection,
          secretariatId: selectedSecretariat ? listOfSecretariat : [],
          indicators: selectIndicator ? listOfIndicator : [],
        }));
      }
    }
  }, [
    isMount,
    typeOfComponent,
    setState,
    subjectValue,
    typeOfLetterValue,
    searchInValue,
    numberValue,
    fromValue,
    toValue,
    incomingNumberValue,
    bodyValue,
    senderSelection,
    recipientSelection,
    selectIndicator,
  ]);

  useListener(() => {
    if (searchInValue === 'secretariats') {
      searchParams.delete('secretariats');
      searchParams.delete('indicators');
      setSelectedSecretariat(null);
      setSelectIndicator(null);
      setState((prevState) => ({
        ...prevState,
        secretariats: [],
        indicator: [],
      }));
    }
  }, [searchInValue]);
  // }, [searchInValue, secretariatListHasAccess]);

  useEffect(() => {
    let secretariatId;

    if (selectedSecretariat && selectedSecretariat.id !== 'all') {
      secretariatId = [{ id: selectedSecretariat.id }];
    }

    if (!isMount) {
      if (typeOfComponent === 'results') {
        setState((prevState) => ({
          ...prevState,
          secretariats: selectedSecretariat ? secretariatId : [],
        }));
      }
      setSelectIndicator(null);
    }
  }, [selectedSecretariat]);

  useListener(() => {
    const currentIndicator = indicatorsListHasAccess.find(
      (item) => item.id === searchParams.get('indicators'),
    );

    if (currentIndicator) {
      setSelectIndicator(currentIndicator);
    }
  }, [indicatorsListHasAccess]);

  const onChangeSelectedSecretariat = (value) => {
    searchParams.delete('indicators');
    setSelectedSecretariat(value);
    setSelectIndicator(null);
  };

  useEffect(() => {
    if (!isMount) {
      if (typeOfComponent === 'results') {
        setRecipientSelection(null);
        setSenderSelection(null);
        searchParams.delete('sender');
        searchParams.delete('recipient');
        searchParams.delete('secretariats');
        searchParams.delete('indicators');
        const newParams = {};
        for (const [key, value] of searchParams) {
          newParams[key] = value;
        }
        navigate({
          pathname: `/organizations/${organizationId}/correspondence/correspondence-search-results`,
          search: `?${new URLSearchParams(newParams).toString()}`,
        });
      }
    }
  }, [typeOfLetterValue]);

  useListener(() => {
    if (!selectedSecretariat || selectedSecretariat?.id === 'all') {
      setIsDisabledIndicator(true);
      setSelectIndicator(additionalItemForIndicator);
    } else {
      setIsDisabledIndicator(false);
      setSelectIndicator(null);
    }
  }, [selectedSecretariat]);

  const handleSearch = (event) => {
    if (event) {
      event.preventDefault();
    }

    if (typeOfComponent !== 'results') {
      // Calculate the result of decorateSecretariatAndIndicatorParam
      const secretariatAndIndicatorParams = decorateSecretariatAndIndicatorParam({
        selectedSecretariat,
        selectIndicator,
      });

      const correctSearchInValue =
        searchInValue?.value === 'this'
          ? currentListDetector()
          : (searchInValue?.value ?? currentList);

      const searchParamsAll = [
        { key: 'subject', value: subjectValue },
        { key: 'typeOfLetter', value: typeOfLetterValue?.value },
        { key: 'searchIn', value: correctSearchInValue },
        { key: 'number', value: numberValue },
        { key: 'incomingNumber', value: incomingNumberValue },
        { key: 'body', value: bodyValue },
        { key: 'sender', value: JSON.stringify(senderSelection) },
        { key: 'recipient', value: JSON.stringify(recipientSelection) },
        { key: 'from', value: fromValue },
        { key: 'to', value: toValue },
        ...secretariatAndIndicatorParams,
      ];

      const searchParamsListExist = searchParamsAll
        .filter((item) => item.value)
        .map((item) => `${item.key}=${item.value}`);

      navigate(
        `/organizations/${organizationId}/correspondence/correspondence-search-results?${searchParamsListExist.join('&')}`,
        { state: { isCollapsed } },
      );
    } else {
      resultsSearchReset();
      getResultsSearch();
    }
  };

  const toggleOpenSearchDrawer = (isOpen) => {
    setOpenSearchDrawer(isOpen);
  };

  //* handle sender and receiver
  const [showingMode, setShowingMode] = useState('both');

  const showingEmployeeIsSelected = useMemo(
    () => showingMode === 'both' || showingMode === 'employee',
    [showingMode],
  );

  const showingContactIsSelected = useMemo(
    () => showingMode === 'both' || showingMode === 'contact',
    [showingMode],
  );

  const contactsAndEmployees = useMemo(() => {
    const result = [];

    // set employee position
    if (
      showingEmployeeIsSelected &&
      positions.organizationPositions.data &&
      employees.organizationEmployeeContacts.data
    ) {
      const organizationPosition = positions.organizationPositions.data?.filter((item) =>
        Boolean(item.employees?.length > 0),
      );

      const converted = employeePositionsUtils.makeTreeAndListData(
        organizationPosition,
        employees.organizationEmployeeContacts.data,
      );

      const employeeList = converted.list.map?.((item) => ({
        position: item,
        user: item.user,
        isEmployee: true,
      }));

      result.push(...employeeList);
    }

    // set contact
    if (showingContactIsSelected && letters.contactsOrganization?.data) {
      const contacts =
        letters.contactsOrganization.data?.map?.((item) => ({
          ...item,
          isContact: true,
        })) ?? [];

      result.push(...contacts);
    }

    return result;
  }, [
    positions.organizationPositions.data,
    employees.organizationEmployeeContacts.data,
    letters.contactsOrganization.data,
    showingMode,
  ]);

  const toggleShowEmployees = () => {
    setShowingMode((preState) => {
      if (preState === 'employee') {
        return 'both';
      }
      return 'employee';
    });
  };

  const toggleShowContacts = () => {
    setShowingMode((preState) => {
      if (preState === 'contact') {
        return 'both';
      }
      return 'contact';
    });
  };

  const getLabelEmployeeOrContact = (value) => {
    if (value.isContact) {
      return contactInfo(value);
    }

    if (value.isEmployee) {
      return userInfo(value.user, value.position);
    }

    return '';
  };

  return (
    <SearchLetterContext.Provider
      value={{
        mobile,
        isFocusSubject,
        handleFocusSubject,
        setIsFocusSubject,
        searchInList,
        searchInValue,
        setSearchInValue,
        typeOfLetterValue,
        setTypeOfLetterValue,
        subjectValue,
        setSubjectValue,
        numberValue,
        setNumberValue,
        incomingNumberValue,
        setIncomingNumberValue,
        bodyValue,
        setBodyValue,
        senderSelection,
        handleChangeSender,
        openPositionEmployeeTreeDialogSender,
        setOpenPositionEmployeeTreeDialogSender,
        openPositionEmployeeTreeDialogRecipient,
        setOpenPositionEmployeeTreeDialogRecipient,
        organizationId,
        isCollapsed,
        setIsCollapsed,
        letters,
        employees,
        recipientSelection,
        handleChangeRecipient,
        handleSearch,
        openSearchDrawer,
        setOpenSearchDrawer,
        toggleOpenSearchDrawer,
        positions,
        fromValue,
        setFromValue,
        toValue,
        setToValue,
        isSelectedForSearchSecretariats,
        secretariatListHasAccess,
        indicatorsListHasAccess,
        selectedSecretariat,
        setSelectedSecretariat,
        onChangeSelectedSecretariat,
        selectIndicator,
        setSelectIndicator,
        isDisabledIndicator,
        contactsAndEmployees,
        toggleShowEmployees,
        toggleShowContacts,
        getLabelEmployeeOrContact,
        showingEmployeeIsSelected,
        showingContactIsSelected,
        showingMode,
        loading,
      }}
    >
      {mobile ? <SearchLetterMobile /> : <SearchLetterDesktop />}
    </SearchLetterContext.Provider>
  );
};

SearchLetter.propTypes = {
  typeOfComponent: PropTypes.string,
  mobile: PropTypes.bool,
  organizationEmployeeContactsAsync: PropTypes.func.isRequired,
  contactsOrganizationAsync: PropTypes.func.isRequired,
  getOrganizationPositionsAsync: PropTypes.func.isRequired,
  resultsSearchReset: PropTypes.func.isRequired,
  employees: PropTypes.shape({}).isRequired,
  positions: PropTypes.shape({}).isRequired,
  letters: PropTypes.shape({}).isRequired,
  setState: PropTypes.func,
  getResultsSearch: PropTypes.func,
  isCollapsed1: PropTypes.bool,
};

SearchLetter.defaultProps = {
  mobile: false,
  typeOfComponent: '',
  isCollapsed1: false,
  setState: () => {},
  getResultsSearch: () => {},
};

const mapStateToProps = (state) => {
  const { letters, positions, employees, secretariats, correspondenceAuthz, organization } = state;
  return { letters, positions, employees, secretariats, correspondenceAuthz, organization };
};

export default withSearchLetterSecurity(
  connect(mapStateToProps, {
    organizationEmployeeContactsAsync: organizationEmployeeContactsAsync_,
    contactsOrganizationAsync: contactsOrganizationAsync_,
    getOrganizationPositionsAsync: getOrganizationPositionsAsync_,
    resultsSearchReset: resultsSearchReset_,
  })(SearchLetter),
);
