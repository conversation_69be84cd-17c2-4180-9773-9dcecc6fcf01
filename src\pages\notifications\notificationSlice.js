import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import Services3 from 'api/v1/services/index';

const apiVersion = '/api/v1/';

const initialState = {
  notifications: {
    unReadCount: 0,
    status: 'idle',
    data: [],
    meta: null,
  },
  newNotifications: {
    state: 'idle',
    data: [],
    recievedIds: [],
    count: 0,
    lastRequest: Date.now(),
  },
  readNotification: {
    state: 'idle',
    data: [],
  },
  unReadNotification: {
    state: 'idle',
    data: null,
  },
  readAllNotifications: {
    state: 'idle',
    data: null,
    unReadCount: 0,
  },
};

export const notificationsAsync = createAsyncThunk(
  'notification/notifications',
  async (options) => {
    const { path, queryObject } = options;
    const opts = {
      apiVersion,
      domainRoot: 'notifications',
      method: 'notifications',
      path,
      queryObject: { limit: queryObject.limit, offset: queryObject.offset },
    };
    const response = await Services3({ options: opts });
    return response;
  },
  {
    condition: (arg, api) => {
      if (
        (api.getState().notification.notifications.status === 'loading' ||
          api.getState().notification.notifications.data) &&
        api.getState().notification.notifications?.data?.length &&
        arg.offset1 < api.getState().notification.notifications?.data?.length
      ) {
        return false;
      }
      return true;
    },
  },
);

export const getUnReadNotificationsAsync = createAsyncThunk(
  'notification/unReadNotifications',
  async (options) => {
    const { path, queryObject } = options;
    const opts = {
      apiVersion,
      domainRoot: 'notifications',
      method: 'unReadNotifications',
      path,
      queryObject,
    };
    const response = await Services3({ options: opts });
    return response;
  },
  {
    condition: (arg, api) => {
      if (
        (api.getState().notification.notifications.status === 'loading' ||
          api.getState().notification.notifications.data) &&
        api.getState().notification.notifications?.data?.length &&
        arg.offset1 < api.getState().notification.notifications?.data?.length
      ) {
        return false;
      }
      return true;
    },
  },
);

export const getUnReadNotificationsCountAsync = createAsyncThunk(
  'notification/getUnReadNotificationsCount',
  async (options) => {
    const { path, queryObject } = options;
    const opts = {
      apiVersion,
      domainRoot: 'notifications',
      method: 'unReadNotifications',
      path,
      queryObject,
    };
    const response = await Services3({ options: opts });
    return response;
  },
);

export const newNotificationsAsync = createAsyncThunk(
  'notification/newNotifications',
  async (options, thunkApi) => {
    const lastRequest = thunkApi.getState().notification?.newNotifications?.lastRequest;
    const lastRequestIso = new Date(lastRequest).toISOString();

    const opts = {
      apiVersion,
      domainRoot: 'notifications',
      method: 'newNotifications',
      path: `${options?.path}&createdAfter=${lastRequestIso}`,
    };
    const response = await Services3({ options: opts });
    return response;
  },
);

export const readNotificationAsync = createAsyncThunk(
  'notification/readNotification',
  async (options) => {
    const { path, data, id } = options;
    const opts = {
      apiVersion,
      domainRoot: 'notifications',
      method: 'read',
      path,
    };
    const response = await Services3({ options: opts, data });
    return { response, id };
  },
);

export const unReadNotificationAsync = createAsyncThunk(
  'notification/unReadNotification',
  async (options) => {
    const { path, data, id } = options;
    const opts = {
      apiVersion,
      domainRoot: 'notifications',
      method: 'unRead',
      path,
    };
    const response = await Services3({ options: opts, data });
    return { response, id };
  },
);

export const readAllNotificationsAsync = createAsyncThunk(
  'notification/readAllNotifications',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'notifications',
      method: 'readAll',
      path: 'read-all',
    };
    const response = await Services3({ options: opts, data: options });
    return { response };
  },
);

export const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    shiftOldestNotification: (state) => {
      state.newNotifications.data.shift();
    },
    notificationsReset: (state) => {
      const st = state;
      const { status, data, meta } = initialState.notifications;
      st.notifications = { unReadCount: st.notifications.unReadCount, status, data, meta };
    },
    filterNotificationToUnread: (state) => {
      const st = state;
      st.notifications.data = state?.notifications.data.map((v) =>
        !v.currentlyRead ? { ...v, display: true } : { ...v, display: false },
      );
    },
  },
  extraReducers: (builder) => {
    builder
      // get notifications
      .addCase(notificationsAsync.pending, (state, action) => {
        const st = state;
        st.notifications.status = 'loading';
      })
      .addCase(notificationsAsync.fulfilled, (state, action) => {
        const st = state;
        st.notifications.status = 'idle';
        st.notifications.meta = action.payload?.meta ?? st.notifications.meta;
        if (action?.payload?.data?.notifications?.length) {
          st.notifications.data = state.notifications?.data
            ? state.notifications?.data?.concat(action?.payload?.data?.notifications)
            : action.payload.data?.notifications;
        }
        st.notifications.unReadCount =
          action.payload?.data?.unreadNotificationsCount ?? st.notifications.unReadCount;
      })
      .addCase(notificationsAsync.rejected, (state, action) => {
        const st = state;
        st.notifications.status = 'failed';
      })

      // get UnRead Notifications
      .addCase(getUnReadNotificationsAsync.fulfilled, (state, action) => {
        const st = state;
        st.notifications.status = 'idle';
        st.notifications.meta = action.payload?.meta ?? st.notifications.meta;
        if (action?.payload?.data?.notifications?.length) {
          st.notifications.data = state.notifications?.data
            ? state.notifications?.data?.concat(action?.payload?.data?.notifications)
            : action.payload.data?.notifications;
        }
        st.notifications.unReadCount =
          action.payload?.data?.unreadNotificationsCount ?? st.notifications.unReadCount;
      })
      .addCase(getUnReadNotificationsAsync.pending, (state, action) => {
        const st = state;
        st.notifications.status = 'loading';
      })
      .addCase(getUnReadNotificationsAsync.rejected, (state, action) => {
        const st = state;
        st.notifications.status = 'failed';
      })

      // unread count
      .addCase(getUnReadNotificationsCountAsync.fulfilled, (state, action) => {
        const st = state;
        st.notifications.unReadCount =
          action.payload.data?.unreadNotificationsCount ?? st.notifications.unReadCount;
      })

      // newNoification
      .addCase(newNotificationsAsync.fulfilled, (state, action) => {
        const st = state;
        st.newNotifications.status = 'idle';
        st.notifications.unReadCount =
          action?.payload?.data?.unreadNotificationsCount ?? st.notifications?.unReadCount;

        if (!action?.payload?.data?.newNotifications?.length) return;

        const newNotif = action.payload.data.newNotifications?.filter(
          (v) => !st.newNotifications.recievedIds.includes(v._id),
        );

        if (!newNotif?.length) return;

        st.newNotifications.recievedIds = st.newNotifications.recievedIds?.concat(
          newNotif.map((v) => v?._id),
        );
        st.newNotifications.data = st.newNotifications?.data?.concat(newNotif);
        st.newNotifications.lastRequest += 1000 * 30;
      })
      .addCase(newNotificationsAsync.pending, (state, action) => {
        const st = state;
        st.newNotifications.status = 'loading';
      })
      .addCase(newNotificationsAsync.rejected, (state, action) => {
        const st = state;
        st.newNotifications.status = 'failed';
      })

      // read notification
      .addCase(readNotificationAsync.fulfilled, (state, action) => {
        const st = state;
        st.readNotification.status = 'idle';

        st.notifications.data = st.notifications?.data?.map((v) =>
          v?.id === action?.payload?.data?.data?.id ? { ...v, currentlyRead: true } : v,
        );

        st.readNotification.data = action.payload;
        // st.notifications.unReadCount -= 1;
      })
      .addCase(readNotificationAsync.pending, (state, action) => {
        const st = state;
        st.readNotification.status = 'loading';
        st.notifications.data = st.notifications?.data?.map((v) =>
          v?.id === action?.meta?.arg?.id ? { ...v, currentlyRead: true } : v,
        );
        st.notifications.unReadCount -= 1;
      })
      .addCase(readNotificationAsync.rejected, (state, action) => {
        const st = state;
        st.readNotification.status = 'failed';

        st.notifications.data = st.notifications?.data?.map((v) =>
          v?.id === action?.meta?.arg?.id ? { ...v, currentlyRead: false } : v,
        );
        st.notifications.unReadCount += 1;
      })

      // unread notification
      .addCase(unReadNotificationAsync.fulfilled, (state, action) => {
        const st = state;
        st.unReadNotification.status = 'idle';
        st.notifications.data = st.notifications?.data?.map((v) =>
          v?.id === action?.payload?.data?.data?.id ? { ...v, currentlyRead: false } : v,
        );

        st.unReadNotification.data = action.payload;
      })
      .addCase(unReadNotificationAsync.pending, (state, action) => {
        const st = state;
        st.unReadNotification.status = 'loading';
        st.notifications.data = st.notifications?.data?.map((v) =>
          v?.id === action?.meta?.arg?.id ? { ...v, currentlyRead: false } : v,
        );
        st.notifications.unReadCount += 1;
      })
      .addCase(unReadNotificationAsync.rejected, (state, action) => {
        const st = state;
        st.unReadNotification.status = 'failed';
        st.notifications.data = st.notifications?.data?.map((v) =>
          v?.id === action?.meta?.arg?.id ? { ...v, currentlyRead: true } : v,
        );
        st.notifications.unReadCount -= 1;
      })

      // read all notifications
      .addCase(readAllNotificationsAsync.fulfilled, (state, action) => {
        const st = state;
        st.readAllNotifications.status = 'idle';
        // st.readAllNotifications.data = action.payload;
        // st.notifications.unReadCount = 0;
      })
      .addCase(readAllNotificationsAsync.pending, (state, action) => {
        const st = state;
        st.readAllNotifications.status = 'loading';
        st.readAllNotifications.data = st.notifications.data;
        st.readAllNotifications.unReadCount = st.notifications.unReadCount;
        st.notifications.unReadCount = 0;
        st.notifications.data = [];
      })
      .addCase(readAllNotificationsAsync.rejected, (state, action) => {
        const st = state;
        st.readAllNotifications.status = 'failed';
        st.notifications.data = st.readAllNotifications.data;
        st.notifications.unReadCount += st.readAllNotifications.unReadCount;
        // st.notifications.unReadCount = 0;
      });
  },
});

export const selectNotificationsResponse = (state) => state.notifications.data;
export const selectNewNotificationsResponse = (state) => state.newNotifications.data;

export const { shiftOldestNotification, notificationsReset, filterNotificationToUnread } =
  notificationSlice.actions;

export default notificationSlice.reducer;
