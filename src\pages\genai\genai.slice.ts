import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';

import Services3 from 'api/v1/services/index';

const apiVersion = '/api/v1/';

const initialState = {
  letterContentByTopic: {
    data: null,
    status: 'idle',
    success: false,
  },
};

export const createLetterContentByTopicAsync = createAsyncThunk(
  'genai/createLetterContentByTopic',
  async (data: any) => {
    const { organizationId } = data;
    const options = {
      apiVersion,
      domainRoot: 'genai',
      method: 'createLetterContentByTopic',
      path: `organization/${organizationId}/letter/content`,
    };
    const response = await Services3({ options, data });
    return response;
  },
);

export const genaiSlice = createSlice({
  name: 'genai',
  initialState,
  reducers: {
    createLetterContentByTopicReset: (state) => {
      const st = state;
      st.letterContentByTopic = initialState.letterContentByTopic;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(createLetterContentByTopicAsync.pending, (state) => {
        const st = state;
        st.letterContentByTopic.status = 'pending';
      })
      .addCase(createLetterContentByTopicAsync.fulfilled, (state, action) => {
        const st = state;
        st.letterContentByTopic.status = 'idle';
        st.letterContentByTopic.data = action.payload.data;
        st.letterContentByTopic.success = action.payload.success;
      }),
});

export default genaiSlice.reducer;

export const { createLetterContentByTopicReset } = genaiSlice.actions;
