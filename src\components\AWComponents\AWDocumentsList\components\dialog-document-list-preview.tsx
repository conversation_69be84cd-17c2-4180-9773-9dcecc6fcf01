import { FC } from 'react';
import CancelIcon from '@mui/icons-material/Cancel';
import Download from '@mui/icons-material/Download';
import { makeStyles } from 'tss-react/mui';
import { IconButton } from '@mui/material';

import { useTranslation } from 'react-i18next';
import AWDialog from 'components/AWComponents/AWDialog';
import AWBox from 'components/AWComponents/AWBox';
import AWIconButton from 'components/AWComponents/AWIconButton';

import { downloadDocument } from 'utils/download-document-utils';
import { IDocumentListPreviewDialog } from '../type/AWDocumentList.type';

const useStylesAttachments = makeStyles()((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(1),
  },
  row: {
    textAlign: 'left',
    backgroundColor: '#EFEFEF',
    padding: `${theme.spacing(0.5)} ${theme.spacing(1)}`,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderRadius: theme.spacing(0.5),
    fontWeight: '400',
    gap: theme.spacing(1),
  },
  fileName: {
    wordBreak: 'break-word',
  },
}));

const DocumentListPreviewDialog: FC<IDocumentListPreviewDialog> = ({
  open,
  onClose,
  data,
  isEditable,
  isDisabled,
  handleDeleteDocument,
}) => {
  const { t } = useTranslation();
  const { classes } = useStylesAttachments();

  return (
    <AWDialog open={open} onClose={onClose} title="پیوست ها" isTitleShow fullWidth maxWidth="md">
      <AWBox sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        {data?.map(
          (
            attachment: {
              id: number;
              name: string;
              isLetterContent: boolean;
              mustBeAsContent: boolean;
            },
            index: number,
          ) => (
            <AWBox
              tabIndex={0}
              role={`box-attachment-${attachment?.id}`}
              key={attachment?.id}
              className={classes.row}
            >
              <AWBox style={{ marginInlineEnd: 'auto' }}>
                {isEditable && handleDeleteDocument && (
                  <IconButton
                    disabled={isDisabled}
                    aria-label="delete"
                    role={`delete-button-${attachment?.id}`}
                    size="small"
                    color="inherit"
                    onClick={(e) => {
                      if (attachment && handleDeleteDocument) {
                        handleDeleteDocument({ id: attachment.id });
                      }
                      e.stopPropagation();
                    }}
                  >
                    <CancelIcon fontSize="inherit" />
                  </IconButton>
                )}
                {attachment?.id && (
                  <AWIconButton
                    aria-label="download"
                    role={`download-button-${attachment?.id}`}
                    size="small"
                    color="inherit"
                    onClick={(e) => {
                      downloadDocument(String(attachment?.id), attachment?.name);
                      e.stopPropagation();
                    }}
                  >
                    <Download fontSize="small" />
                  </AWIconButton>
                )}
              </AWBox>
              <AWBox>{attachment?.name}</AWBox>
            </AWBox>
          ),
        )}
      </AWBox>
    </AWDialog>
  );
};

export default DocumentListPreviewDialog;
