import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import Services3 from 'api/v1/services';
import { ApiQueryObject } from 'api/v1/services/types/api-query-object.type';
import { RootState } from 'store';

const apiVersion = '/api/v1/';
const domainRoot = 'corr-samples';

const initialState = {
  createCorrespondenceSample: {
    status: 'idle',
    data: null,
  },
  correspondenceSamplesList: {
    status: 'idle',
    data: null,
  },
  correspondenceSample: {
    status: 'idle',
    data: null,
  },
  deleteCorrespondenceSample: {
    status: 'idle',
    data: null,
  },
  createForwardNotesSample: {
    status: 'idle',
    data: null,
    success: false,
  },
  forwardNotesSamples: {
    status: 'idle',
    data: null,
    success: false,
  },
  deleteForwardNotesSamples: {
    status: 'idle',
    data: null,
    success: false,
  },
};

export type CorrespondenceSamplesListOptions = {
  organizationId: string;
  queryObject: ApiQueryObject;
};

export const correspondenceSamplesListAsync = createAsyncThunk(
  `corr-samples/correspondenceSamplesList`,
  async (options: CorrespondenceSamplesListOptions) => {
    const { organizationId, queryObject } = options;
    const opts = {
      apiVersion,
      domainRoot,
      method: 'correspondenceSamplesList',
      path: `organization/${organizationId}`,
      queryObject,
    };
    const response = await Services3({ options: opts });
    return response;
  },
  {
    condition: (arg, api) => {
      if (
        (api.getState() as RootState).correspondenceSamples.correspondenceSamplesList.status ===
          'pending' ||
        (api.getState() as RootState).correspondenceSamples.correspondenceSamplesList.data
      )
        return false;

      return true;
    },
  },
);

export type CreateCorrespondenceSampleOptions = {
  organizationId: string;
  data: {
    id: string;
    organization: {
      id: string;
    };
    title: string;
    content: string;
  };
};

export const createCorrespondenceSampleAsync = createAsyncThunk(
  `corr-samples/createCorrespondenceSample`,
  async (args: CreateCorrespondenceSampleOptions) => {
    const { data } = args;
    const options = {
      apiVersion,
      domainRoot,
      method: 'createCorrespondenceSample',
    };
    const response = await Services3({ options, data });
    return response;
  },
);

export type DeleteCorrespondenceSampleOptions = {
  id: string;
  organizationId: string;
};

export const deleteCorrespondenceSampleAsync = createAsyncThunk(
  `corr-samples/deleteCorrespondenceSample`,
  async (args: any) => {
    const { id } = args;

    const data = {
      id,
    };

    const options = {
      apiVersion,
      domainRoot,
      method: 'deleteCorrespondenceSample',
      path: id,
    };

    const response = await Services3({ options, data });
    return response;
  },
);

export type CorrespondenceSampleOptions = {
  id: string;
  organizationId: string;
};

export const correspondenceSampleAsync = createAsyncThunk(
  `corr-samples/correspondenceSample`,
  async (args: CorrespondenceSampleOptions) => {
    const { organizationId, id } = args;
    const options = {
      apiVersion,
      domainRoot,
      method: 'correspondenceSample',
      path: `organization/${organizationId}/${id}`,
    };
    const response = await Services3({ options });
    return response;
  },
);

type CreateForwardNoteSampleDto = {
  id: string;
  note: string;
  requester: {
    user: { id: string };
    position: {
      id: string;
      slot: (string | number)[];
    };
  };
};

export const createForwardNotesSampleAsync = createAsyncThunk(
  'corr-samples/createForwardNotesSample',
  async (data: CreateForwardNoteSampleDto) => {
    const { requester, ...rest } = data;
    const requesterString = `${requester.position.id}_${requester.position.slot?.join('-')}`;

    const options = {
      apiVersion,
      domainRoot,
      method: 'createForwardNotesSample',
      path: 'forward-notes',
      queryObject: {
        requester: requesterString,
      },
    };

    const response = await Services3({ options, data: rest });
    return response;
  },
);

type RequesterDto = {
  user: { id: string };
  position: {
    id: string;
    slot: (string | number)[];
  };
};

type GetForwardNotesSamplesDto = {
  queryObject: {
    requester: RequesterDto;
    limit?: number;
    offset?: number;
  };
};

export const getForwardNotesSamplesAsync = createAsyncThunk(
  'corr-samples/forwardNotesSamples',
  async ({ queryObject }: GetForwardNotesSamplesDto) => {
    const { requester, ...rest } = queryObject;
    const requesterString = `${requester.position.id}_${requester.position.slot?.join('-')}`;

    const query: Record<string, string | number | null> = {
      requester: requesterString,
      limit: rest.limit ?? null,
      offset: rest.offset ?? null,
    };

    const options = {
      apiVersion,
      domainRoot,
      method: 'forwardNotesSamples',
      path: 'forward-notes',
      queryObject: query,
    };

    const response = await Services3({ options });
    return response;
  },
);

type DeleteForwardNotesSampleDto = {
  id: string;
  requester: RequesterDto;
};

export const deleteForwardNotesSamplesAsync = createAsyncThunk(
  'corr-samples/deleteForwardNotesSamples',
  async (data: DeleteForwardNotesSampleDto) => {
    const { id, requester } = data;
    const requesterString = `${requester.position.id}_${requester.position.slot?.join('-')}`;

    const options = {
      apiVersion,
      domainRoot,
      method: 'deleteForwardNotesSamples',
      path: `forward-notes/${id}`,
      queryObject: {
        requester: requesterString,
      },
    };

    const response = await Services3({ options });
    return response;
  },
);

const correspondenceSamplesSlice = createSlice({
  name: `corr-samples`,
  initialState,
  reducers: {
    correspondenceSamplesListReset: (state) => {
      const st = state;
      st.correspondenceSamplesList = initialState.correspondenceSamplesList;
    },
    createCorrespondenceSampleReset: (state) => {
      const st = state;
      st.createCorrespondenceSample = initialState.createCorrespondenceSample;
    },
    deleteCorrespondenceSampleReset: (state) => {
      const st = state;
      st.deleteCorrespondenceSample = initialState.deleteCorrespondenceSample;
    },
    correspondenceSampleReset: (state) => {
      const st = state;
      st.correspondenceSample = initialState.correspondenceSample;
    },
    forwardNotesSamplesReset: (state) => {
      const st = state;
      st.forwardNotesSamples = initialState.forwardNotesSamples;
    },
  },

  extraReducers: (builder) => {
    builder
      .addCase(correspondenceSamplesListAsync.pending, (state) => {
        const st = state;
        st.correspondenceSamplesList.status = 'pending';
      })
      .addCase(correspondenceSamplesListAsync.fulfilled, (state, action) => {
        const st = state;
        st.correspondenceSamplesList.status = 'success';
        st.correspondenceSamplesList.data = action.payload.data ?? [];
      })
      .addCase(correspondenceSamplesListAsync.rejected, (state) => {
        const st = state;
        st.correspondenceSamplesList.status = 'failed';
        st.correspondenceSamplesList.data = null;
      })
      .addCase(correspondenceSampleAsync.pending, (state) => {
        const st = state;
        st.correspondenceSample.status = 'pending';
      })
      .addCase(correspondenceSampleAsync.fulfilled, (state, action) => {
        const st = state;
        st.correspondenceSample.status = 'success';
        st.correspondenceSample.data = action.payload.data;
      })
      .addCase(correspondenceSampleAsync.rejected, (state) => {
        const st = state;
        st.correspondenceSample.status = 'failed';
        st.correspondenceSample.data = null;
      })
      .addCase(createCorrespondenceSampleAsync.pending, (state) => {
        const st = state;
        st.createCorrespondenceSample.status = 'pending';
      })
      .addCase(createCorrespondenceSampleAsync.fulfilled, (state, action) => {
        const st = state;

        if (action.payload?.success) {
          st.createCorrespondenceSample.status = 'success';
          st.createCorrespondenceSample.data = action.payload?.data;
        } else {
          st.createCorrespondenceSample.status = 'failed';
          st.createCorrespondenceSample.data = null;
        }
      })
      .addCase(createCorrespondenceSampleAsync.rejected, (state) => {
        const st = state;
        st.createCorrespondenceSample.status = 'failed';
        st.createCorrespondenceSample.data = null;
      })
      .addCase(deleteCorrespondenceSampleAsync.pending, (state) => {
        const st = state;
        st.deleteCorrespondenceSample.status = 'pending';
      })
      .addCase(deleteCorrespondenceSampleAsync.fulfilled, (state, action) => {
        const st = state;
        st.deleteCorrespondenceSample.status = 'success';
        st.deleteCorrespondenceSample.data = action.payload.data;
      })
      .addCase(deleteCorrespondenceSampleAsync.rejected, (state) => {
        const st = state;
        st.deleteCorrespondenceSample.status = 'failed';
        st.deleteCorrespondenceSample.data = null;
      })
      .addCase(getForwardNotesSamplesAsync.pending, (state) => {
        const st = state;
        st.forwardNotesSamples.status = 'pending';
      })
      .addCase(getForwardNotesSamplesAsync.fulfilled, (state, action) => {
        const st = state;
        st.forwardNotesSamples.status = 'idle';
        st.forwardNotesSamples.data = action.payload.data;
        st.forwardNotesSamples.success = action.payload.success;
      })
      .addCase(createForwardNotesSampleAsync.pending, (state) => {
        const st = state;
        st.createForwardNotesSample.status = 'pending';
      })
      .addCase(createForwardNotesSampleAsync.fulfilled, (state, action) => {
        const st = state;
        st.createForwardNotesSample.status = 'idle';
        st.createForwardNotesSample.data = action.payload.data;
        st.createForwardNotesSample.success = action.payload.success;
      })
      .addCase(deleteForwardNotesSamplesAsync.pending, (state) => {
        const st = state;
        st.deleteForwardNotesSamples.status = 'pending';
      })
      .addCase(deleteForwardNotesSamplesAsync.fulfilled, (state, action) => {
        const st = state;
        st.deleteForwardNotesSamples.status = 'idle';
        st.deleteForwardNotesSamples.data = action.payload.data;
        st.deleteForwardNotesSamples.success = action.payload.success;
      });
  },
});

export const {
  createCorrespondenceSampleReset,
  deleteCorrespondenceSampleReset,
  correspondenceSampleReset,
  correspondenceSamplesListReset,
  forwardNotesSamplesReset,
} = correspondenceSamplesSlice.actions;

export default correspondenceSamplesSlice.reducer;
