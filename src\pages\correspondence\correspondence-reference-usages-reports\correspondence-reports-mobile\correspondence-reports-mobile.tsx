import Box from '@mui/material/Box';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import AccountTreeOutlinedIcon from '@mui/icons-material/AccountTreeOutlined';
import SchemaOutlinedIcon from '@mui/icons-material/SchemaOutlined';
import ListItemText from '@mui/material/ListItemText';
import Divider from '@mui/material/Divider';
import { makeStyles } from 'tss-react/mui';
import ArrowBackIosNewOutlinedIcon from '@mui/icons-material/ArrowBackIosNewOutlined';
import { ListItemButton, ListItemIcon } from '@mui/material';

import { useTranslation } from 'react-i18next';
import { IWithReportsSecurity } from './withReportsSecurity';
import { FC } from 'react';
import AWListItem from 'components/AWComponents/AWListItem';

const useStyles = makeStyles()((theme) => ({
  list: {
    '& li': {
      paddingRight: theme.spacing(1),
      paddingLeft: theme.spacing(2),
    },
    '& .MuiListItemText-root': {
      '& span': {
        fontWeight: 500,
      },
    },
    '& .MuiListItemIcon-root': {
      minWidth: 'unset',
      marginLeft: theme.spacing(1),
    },
  },
  arrow: {
    width: '0.7em',
    height: '0.7em',
  },
}));

type CorrespondenceReportsMobileProps = {
  security: IWithReportsSecurity;
  handleNavigate: (type: string) => void;
};

const CorrespondenceReportsMobile: FC<CorrespondenceReportsMobileProps> = ({
  handleNavigate,
  security,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslation();

  const correspondenceManagement = [
    {
      label: t('correspondence.management.all-forwarded-letter'),
      icon: AccountTreeOutlinedIcon,
      type: 'allForwardedLetters',
      security: security.manageAllForwardedLetters,
    },
    {
      label: t('correspondence.management.reports'),
      icon: SchemaOutlinedIcon,
      type: 'reports',
      security: security.sidebarCorrespondenceReferenceUsagesReports,
    },
  ];

  return (
    <Box>
      <List className={classes.list}>
        {correspondenceManagement.map((item) => (
          <>
            <AWListItem
              security={item.security}
              disableGutters
              onClick={() => handleNavigate(item.type)}
            >
              <ListItemButton>
                <ListItemIcon>
                  <item.icon />
                </ListItemIcon>
                <ListItemText primary={item.label} />
              </ListItemButton>
              <ArrowBackIosNewOutlinedIcon className={classes.arrow} />
            </AWListItem>
            <Divider />
          </>
        ))}
      </List>
    </Box>
  );
};

export default CorrespondenceReportsMobile;
