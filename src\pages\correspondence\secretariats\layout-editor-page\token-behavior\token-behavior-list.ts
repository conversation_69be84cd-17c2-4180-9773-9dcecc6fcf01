import { BehaviorList } from './behavior-list.type';

export const tokenBehaviorList: { [key: string]: BehaviorList[] } = {
  paper: [],
  date: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
  ],
  letterNumber: [
    'change-label',
    'font-size',
    'font-weight',
    'use-english-digits',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
  ],
  attachment: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
  ],
  sender: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
    'information-setting',
  ],
  recipient: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
    'information-setting',
  ],
  subject: [
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
  ],
  signature: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'max-size',
    'signer-name',
    'signer-name-and-line',
    'signer-name-position',
    'format-dock',
    'format-align-and-direction',
    'information-setting',
  ],
  logo: ['max-size', 'format-dock'],
  priority: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
  ],
  confidentiality: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
  ],
  pageNumber: ['format-lang', 'font-size', 'font-weight'],
  cc: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
    'information-setting',
  ],
  staticText: [
    'format-lang',
    'change-label',
    'font-size',
    'font-weight',
    'delete-token',
    'format-dock',
    'name-field-order',
    'format-align-and-direction',
  ],
  content: [],
  stamp: ['max-size', 'format-dock'],
};

// export const tokenBehaviorList = {
//   date: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//   },
//   letterNumber: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//   },
//   attachment: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//   },
//   subject: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//   },
//   priority: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//   },
//   confidentiality: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//   },
//   pageNumber: {
//     'font-size': { order: 0 },
//   },
//   sender: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//     'single-line': { order: 2 },
//     'fullName-only': { order: 3 },
//     'title-fullName-first': { order: 4 },
//   },
//   recipient: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//     'single-line': { order: 2 },
//     'fullName-only': { order: 3 },
//     'title-fullName-first': { order: 4 },
//   },
//   cc: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//     'single-line': { order: 2 },
//     'fullName-only': { order: 3 },
//     'title-fullName-first': { order: 4 },
//   },
//   signature: {
//     'change-label': { order: 0 },
//     'font-size': { order: 1 },
//     'signer-name': { order: 2 },
//     'single-line': { order: 3 },
//     'fullName-only': { order: 4 },
//     'title-fullName-first': { order: 5 },
//   },
//   logo: 0,
//   content: 0,
// };
