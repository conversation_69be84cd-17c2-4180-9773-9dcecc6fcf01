import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import Box from '@mui/material/Box';
import DialogActions from '@mui/material/DialogActions';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import SwipeableDrawer from '@mui/material/SwipeableDrawer';
import Grid from '@mui/material/Grid';
import CloseIcon from '@mui/icons-material/Close';
import { t } from 'i18next';
import { useLetter } from 'contexts/pageContext/letters/letterContext';
import Loading from 'components/Loading';
import AWDialog from 'components/AWComponents/AWDialog';
import AWBox from 'components/AWComponents/AWBox';
import PositionEmployeeInput from './components/position-employee-input';
import ForwardLetterTypes from './components/forward-letter-types';
import Paraph from './components/paraph';
import ForwardLetterTags from './components/forward-letter-tags';
import ForwardLetterAttachments from './components/forward-letter-attachments';

const useStyles = makeStyles()((theme) => ({
  swipeableDrawer: {
    height: '100vh',
    // height: 'calc(100vh - 64px)',
    overflowY: 'scroll',
  },
  topBar: {
    padding: '0 !important',
    display: 'flex',
    alignItems: 'center',
    background: theme.palette.background.innerPage,
    height: 64,
    textAlign: 'center',
    position: 'relative',
    '& svg': {
      height: 64,
      width: 64,
      position: 'absolute',
      padding: 16,
      color: theme.palette.text.gray,
      top: 0,
    },
    '& h6': {
      margin: ' 0 auto',
      height: 64,
      display: 'flex',
      alignItems: 'center',
      fontSize: '1rem',
    },
  },
  fixed: {
    position: 'fixed',
    zIndex: 10,
    width: '100%',
  },
  paperAnchorBottom: {
    // overflowY: 'scroll',
    margin: '64px 0 25px',
    padding: theme.spacing(3, 2),
  },
  dialogPaper: {
    padding: 24,
  },
  appBar: {
    background: '#fff',
    color: '#000',
    display: 'flex',
    position: 'relative',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: 'unset',
    '& button': {
      position: 'absolute',
      right: 0,
    },
  },
  dialogContent: {
    padding: 0,
  },
  dialogActions: {
    justifyContent: 'end',
  },
  box: {
    padding: 24,
    marginTop: 18,
    // borderRadius: 8,
    // backgroundSize: '20px 1px, 20px 1px, 1px 20px, 1px 20px',
    // backgroundImage:
    //   'linear-gradient(to right, #d1d1d1 60%, transparent 50%), linear-gradient(to right, #d1d1d1 60%, transparent 60%), linear-gradient(to bottom, #d1d1d1 60%, transparent 60%), linear-gradient(to bottom, #d1d1d1 60%, transparent 60%)',
    // backgroundRepeat: 'repeat-x, repeat-x, repeat-y, repeat-y',
    // backgroundPosition: 'left top, left bottom, left top, right top',
  },
  fieldset: {
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    margin: theme.spacing(2, 0),
  },
  radioGroup: {
    flexDirection: 'row',
    marginRight: 8,
  },
  addRecipientDiv: {
    display: 'flex',
    justifyContent: 'left',
  },
  addRecipientBtn: {
    float: 'left',
    marginTop: theme.spacing(2),
  },
  box2: {
    display: 'flex',
    minHeight: 90,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing(2),
    maxHeight: 200,
    overflowY: 'auto',
  },
  paraphField: {
    '& input': {
      textAlign: 'right',
    },
  },
  listItemRoot: {
    margin: '0 0 8px 0',
    borderRadius: 6,
    boxShadow: '0 1px 5px 0 rgb(0 0 0 / 8%)',
  },
  textArea: {
    height: 'unset',
  },
  inputLabelRoot: {
    lineHeight: 'unset',
    transform: 'translate(-14px, 16px) scale(1)!important',
  },
  forwardTypeBox: {
    display: 'flex',
    alignItems: 'center',
  },
  forwardType: {
    background: '#eeeeee',
    padding: 8,
    borderRadius: 6,
    color: '#6c6c6c',
  },
  addToForwardListMobile: {
    textAlign: 'center',
    // justifyContent: 'center',
    padding: theme.spacing(0, 2),
  },
  addButton: {
    // position: 'fixed',
    // width: '90%',
    // bottom: theme.spacing(6),
  },
}));

const SingleForwardLetter = ({ isMobile }) => {
  const { classes, cx } = useStyles();
  const {
    handleCloseForwardLetter,
    handleChangeForwardTypes,
    forwardType,
    paraph,
    handleChangeParaph,
    toggleDialogParaphList,
    organizationId,
    openPositionEmployeeTreeDialogRecipient,
    handleOpenPositionEmployeeTreeDialogRecipient,
    handleClosePositionEmployeeTreeDialogRecipient,
    forwardRecipientSelection,
    handleChangeForwardRecipient,
    forwardLetterLoading,
    forwardLetterTag,
    handleChangeForwardLetterTag,
    addToForwardList,
    forwardLetterAttachments,
    setForwardLetterAttachments,
    uploadDocumentLoading,
    forwardAttachmentsLoading,
    toggleDialogCreateParaph,
    security,
    validForwardNotesSample,
  } = useLetter();

  return isMobile ? (
    <SwipeableDrawer anchor="bottom" open onClose={handleCloseForwardLetter} disableSwipeToOpen>
      <div className={classes.swipeableDrawer}>
        <div className={cx(classes.fixed, classes.topBar)}>
          <CloseIcon onClick={handleCloseForwardLetter} />
          <Typography variant="h6">{t('commands.letters.LetterForwarded')}</Typography>
        </div>
        <Grid xs={12} className={classes.paperAnchorBottom}>
          <PositionEmployeeInput
            organizationId={organizationId}
            label={t('forwardLetter.labels.recipients')}
            name="forwardRecipient"
            isSingleSelection
            selectedPositionEmployee={forwardRecipientSelection}
            handleChangeAutocomplete={handleChangeForwardRecipient}
            openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogRecipient}
            handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogRecipient}
            handleClosePositionEmployeeViewerDialog={handleClosePositionEmployeeTreeDialogRecipient}
            onSubmitPositionEmployee={handleChangeForwardRecipient}
            isMobile={isMobile}
            isForwardLetter
            isLetterEditable={() => false}
          />
          <ForwardLetterTypes
            forwardType={forwardType}
            handleChangeForwardTypes={handleChangeForwardTypes}
          />
          <Paraph
            paraph={paraph}
            handleChangeParaph={handleChangeParaph}
            toggleDialogParaphList={toggleDialogParaphList}
            toggleDialogCreateParaph={toggleDialogCreateParaph}
            forwardLetterLoading={forwardLetterLoading}
            security={security}
            validForwardNotesSample={validForwardNotesSample}
          />
          <ForwardLetterAttachments
            setForwardLetterAttachments={setForwardLetterAttachments}
            forwardLetterAttachments={forwardLetterAttachments}
            isDisabled={forwardLetterLoading}
          />
          <ForwardLetterTags
            forwardLetterTag={forwardLetterTag}
            handleChangeForwardLetterTag={handleChangeForwardLetterTag}
          />
          <DialogActions className={classes.addToForwardListMobile}>
            <Button
              id="single-letter-button"
              variant="contained"
              disabled={
                !Object.keys(forwardRecipientSelection || {})?.length ||
                forwardLetterLoading ||
                uploadDocumentLoading
              }
              onClick={() => addToForwardList()}
              className={classes.addButton}
              // endIcon={<ArrowBackIosIcon />}
            >
              {forwardLetterLoading ? (
                <Loading color="white" />
              ) : (
                t('solutionHistory.labels.letter.forward')
              )}
            </Button>
          </DialogActions>
        </Grid>
      </div>
    </SwipeableDrawer>
  ) : (
    <AWDialog
      open
      onClose={handleCloseForwardLetter}
      classes={{ paper: classes.dialogPaper }}
      fullWidth
      maxWidth="md"
      isTitleShow
      title={t('commands.letters.LetterForwarded')}
    >
      <Box component="div" className={classes.box}>
        <div>
          <PositionEmployeeInput
            organizationId={organizationId}
            label={t('forwardLetter.labels.recipients')}
            name="forwardRecipient"
            isSingleSelection
            selectedPositionEmployee={forwardRecipientSelection}
            handleChangeAutocomplete={handleChangeForwardRecipient}
            openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogRecipient}
            handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogRecipient}
            handleClosePositionEmployeeViewerDialog={handleClosePositionEmployeeTreeDialogRecipient}
            isForwardLetter
            onSubmitPositionEmployee={handleChangeForwardRecipient}
            isLetterEditable={() => false}
          />
        </div>
        <ForwardLetterTypes
          forwardType={forwardType}
          handleChangeForwardTypes={handleChangeForwardTypes}
        />
        <Paraph
          paraph={paraph}
          handleChangeParaph={handleChangeParaph}
          toggleDialogParaphList={toggleDialogParaphList}
          toggleDialogCreateParaph={toggleDialogCreateParaph}
          forwardLetterLoading={forwardLetterLoading}
          security={security}
          validForwardNotesSample={validForwardNotesSample}
        />
        <ForwardLetterAttachments
          setForwardLetterAttachments={setForwardLetterAttachments}
          forwardLetterAttachments={forwardLetterAttachments}
          isDisabled={forwardLetterLoading}
        />
        <ForwardLetterTags
          forwardLetterTag={forwardLetterTag}
          handleChangeForwardLetterTag={handleChangeForwardLetterTag}
        />
      </Box>
      <AWBox sx={{ display: ' flex', justifyContent: 'end', padding: ' 0px 24px' }}>
        <Button
          sx={{ display: 'flex', justifyContent: 'end' }}
          id="forward-letter-button"
          variant="contained"
          disabled={
            !Object.keys(forwardRecipientSelection || {})?.length ||
            forwardLetterLoading ||
            forwardAttachmentsLoading ||
            uploadDocumentLoading
          }
          onClick={() => addToForwardList()}
        >
          {forwardLetterLoading || uploadDocumentLoading ? (
            <Loading color="white" />
          ) : (
            t('solutionHistory.labels.letter.forward')
          )}
        </Button>
      </AWBox>
    </AWDialog>
  );
};

SingleForwardLetter.propTypes = {
  isMobile: PropTypes.bool,
};

SingleForwardLetter.defaultProps = {
  isMobile: false,
};
export default SingleForwardLetter;
