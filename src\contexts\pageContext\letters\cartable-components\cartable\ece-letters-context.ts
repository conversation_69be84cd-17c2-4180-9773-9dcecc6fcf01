import { createContext, useContext } from 'react';

const defaultValue = {
  dataList: [],
  handleNextPage: () => {},
  handlePrevPage: () => {},
  changePage: (page: number) => {},
  handleRowClick: (row: any) => {},
  meta: {
    limit: 0,
    offset: 0,
    total: 0,
  },
  mobile: false,
  limit: 10,
  offset: 0,
  loading: false,
  tableLoading: false,
  getMyCartableLetters: () => {},
  infinityLoading: false,
  organizationSecretariats: [],
  selectedSecretariat: { id: '', name: '' },
  handleChangeSecretariat: (e: any) => {},
  setOffset: () => {},
  handleChangePage: () => {},
  pageLoading: false,
  onRowClick: () => {},
  positions: { positionsUser: { data: [] } },
  profile: { userProfile: { data: {} } },
  params: {},
  openSecretariatsDrawer: false,
  setOpenSecretariatsDrawer: (value: boolean) => {},
  showDialogRefreshEce: false,
  setShowDialogRefreshEce: (value: boolean) => {},
  handleEceLettersRefresh: () => {},
  handleResetEceLettersRefresh: () => {},
  showRefreshEceLetter: false,
  selectedTab: 'incoming',
  handleChangeTab: (e: any, newValue: string) => {},
  type: 'incoming',
  remainingCalculate: () => 0,
  handleFailedSentReceiptsEce: () => {},
  handleChangeLimit: () => {},
};

export const ECELettersContext = createContext(defaultValue);

export const useECELettersContext = () => useContext(ECELettersContext);
