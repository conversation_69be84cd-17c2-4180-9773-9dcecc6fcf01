import React from 'react';
import PropTypes from 'prop-types';
import { useNavigate, useParams } from 'react-router-dom';
import ListItemText from '@mui/material/ListItemText';
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import Collapse from '@mui/material/Collapse';
import { makeStyles } from 'tss-react/mui';
import ListItemIcon from '@mui/material/ListItemIcon/ListItemIcon';
import ExpandLess from '@mui/icons-material/ExpandLess';
import ExpandMore from '@mui/icons-material/ExpandMore';
import { connect } from 'react-redux';
import ListItemButton from '@mui/material/ListItemButton';
import { ReactComponent as Apps } from '../../assets/images/apps.svg';
import { useMainLayoutStyles } from '../../contexts/mainLayoutStylesContext';
import { userOrganizationsAsync } from '../../pages/management-center/organization/organizationSlice';
import AWMenuItem from '../AWComponents/AWMenuItem';

const SideBarAppsAll = ({ open }) => {
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes, cx } = makeStyles()(stylesGenerator)();
  const navigate = useNavigate();
  const params = useParams();
  const [expand, setExpand] = React.useState(true);
  const handleClickExpand = () => {
    setExpand(!expand);
  };
  const goToLetterIncoming = () => {
    navigate(
      `/organizations/${params.organizationId}/correspondence/active?tab=correspondence-in-progress`,
    );
  };

  return (
    <>
      <AWMenuItem
        button
        onClick={handleClickExpand}
        className={cx({
          [classes.ListItem]: open,
          [classes.miniListItem]: !open,
        })}
      >
        {!open && (
          <Box className={classes.miniSidebarItem}>
            <Apps className="itemIcon" />
            {/* <Typography variant="body2">راهکارها</Typography> */}
          </Box>
        )}
        {open && (
          <>
            <ListItemIcon>
              <Apps className="itemIcon" />
            </ListItemIcon>
            <ListItemText primary="راهکارها" />
            {expand ? (
              <ExpandLess onClick={handleClickExpand} className="arrowIcon" />
            ) : (
              <ExpandMore onClick={handleClickExpand} className="arrowIcon" />
            )}
          </>
        )}
      </AWMenuItem>
      <Collapse
        in={open ? expand : open}
        timeout="auto"
        unmountOnExit
        className={classes.CollapseItem}
      >
        <List component="div" disablePadding>
          <ListItemButton className={classes.nested} onClick={goToLetterIncoming}>
            <ListItemText primary="مکاتبات" className={classes.ListItemText2} />
          </ListItemButton>
          <ListItemButton className={classes.nested}>
            <ListItemText primary="جلسات" className={classes.ListItemText2} />
          </ListItemButton>
          <ListItemButton className={classes.nested}>
            <ListItemText primary="کار ها" className={classes.ListItemText2} />
          </ListItemButton>
        </List>
      </Collapse>
    </>
  );
};
const mapStateToProps = (state) => {
  const { organization, auth } = state;
  return {
    organization,
    auth,
  };
};

SideBarAppsAll.propTypes = {
  open: PropTypes.bool.isRequired,
};

export default connect(mapStateToProps, { userOrganizationsAsync })(SideBarAppsAll);
