import { FC } from 'react';
import Link from '@mui/material/Link';
import Grid from '@mui/material/Grid';
import Container from '@mui/material/Container';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import FormControl from '@mui/material/FormControl';
import OutlinedInput from '@mui/material/OutlinedInput';
import FormHelperText from '@mui/material/FormHelperText';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import { Link as RouterLink } from 'react-router-dom';
import logo from 'assets/images/logo/Logo.svg';
import SocialMedia from '../../../../components/SocialMedia';
import Loading from '../../../../components/Loading';
import { useMainLayoutStyles } from '../../../../contexts/mainLayoutStylesContext';
import { useLoginContext } from '../../../../contexts/pageContext/auth/loginContext';
import { ReactComponent as LoginIcon } from '../../../../assets/images/forms/login.svg';
import AWBox from '../../../../components/AWComponents/AWBox';
import AWTypography from '../../../../components/AWComponents/AWTypography';
import AWTextField from '../../../../components/AWComponents/AWTextField';
import AWIconButton from '../../../../components/AWComponents/AWIconButton';
import AWButton from '../../../../components/AWComponents/AWButton';

const useStyles = makeStyles()((theme) => ({
  mainForm: {
    display: 'flex',
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    maxWidth: 1280,
  },
  forms: {
    height: '100%',
  },
  root: {
    width: '100%',
    height: '100vh',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    minHeight: 650,
  },
  mobileRoot: {
    position: 'relative',
    textAlign: 'center',
    flexGrow: 1,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  mobileMiddlePaper: {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '24px 19px 44.2px 19px',
    margin: 'auto',
    marginTop: 0,
    flex: 1,
    '& .marginTopMobile': {
      marginTop: theme.spacing(3),
    },
  },
  logo: {
    display: 'flex',
    margin: theme.spacing(2, 0),
  },
  title: {
    margin: theme.spacing(2, 0),
  },
  form: {
    height: '100%',
    width: '100%',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    flex: 1,
  },
  input: {
    marginTop: theme.spacing(2),
    marginBottom: theme.spacing(2),
  },
  textField: {
    flexBasis: 200,
  },
  marginTop1: {
    marginTop: theme.spacing(1),
  },
  forgetPassword: {
    marginTop: theme.spacing(1),
    textAlign: 'right',
    '& a': {
      color: theme.palette.text.link,
    },
  },
  marginTop: {
    marginTop: theme.spacing(3),
  },
  ltrInput: {
    '& input': {
      direction: 'ltr',
    },
  },
  errorText: {
    color: theme.palette.text.error,
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
    '& svg': {
      fontSize: '1rem',
      marginLeft: '5px',
    },
  },
  margin: {
    margin: '20px 0',
  },
  outlinedInput: {
    flexDirection: 'revert',
  },
  loginLink: {
    textDecoration: 'underline!important',
    textUnderlineOffset: '5px',
    fontSize: '16px',
    fontWeight: 500,
    color: theme.palette.text.link,
    padding: theme.spacing(0, 1)
  },
  goRegister: {
    display: 'flex',
    justifyContent: 'center',
    fontSize: '16px',
    fontWeight: 500,
    [theme.breakpoints.down('sm')]: {
      marginBottom: theme.spacing(2),
      fontSize: '16px',
      fontWeight: 500,
    },
  },
  mobileMode: {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'end',
    flex: 1,
  },
}));

interface IDetails {
  isMobile?: boolean;
}

const Details: FC<IDetails> = ({ isMobile = false }) => {
  const { classes: customClasses, cx } = useStyles();
  const { t } = useTranslation();
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes } = makeStyles()(stylesGenerator)();

  const {
    auth,
    isInvalid,
    getValidationError,
    submitLogin,
    state,
    onChange,
    usernameErrorText,
    passwordErrorText,
    showPassword,
    handleClickShowPassword,
    loadingInButton,
  } = useLoginContext();

  return (
    <Grid container className={customClasses.mainForm} columns={16}>
      <Grid item lg={9} display={{ xs: 'none', lg: 'block' }}>
        <LoginIcon />
      </Grid>
      <Grid item xs={16} lg={7} className={customClasses.forms}>
        <Container
          component="div"
          maxWidth="xs"
          className={isMobile ? customClasses.mobileRoot : customClasses.root}
        >
          <AWBox className={isMobile ? customClasses.mobileMiddlePaper : classes.middlePaper}>
            <img src={logo} alt="Logo" className={customClasses.logo} />
            <AWTypography variant="body1" className={cx(classes.bold, classes.title)}>
              {t('auth.labels.loginTitle')}
            </AWTypography>
            {auth.loginByPassword.status === 'idle' && auth.loginByPassword.success === false && (
              <AWTypography variant="subtitle1" className={cx(customClasses.errorText)}>
                <HighlightOffIcon />
                {t('auth.messages.userNotFound')}
              </AWTypography>
            )}
            <form className={customClasses.form} onSubmit={submitLogin}>
              <AWTextField
                className={cx(customClasses.input, customClasses.ltrInput)}
                label={`${t('common.labels.usernameOrMobile')} *`}
                variant="outlined"
                fullWidth
                autoComplete="off"
                name="username"
                value={state?.username}
                onChange={onChange}
                error={!!usernameErrorText || isInvalid('username')}
                helperText={
                  getValidationError('username')
                    ? getValidationError('username')
                    : usernameErrorText || ''
                }
              />
              <FormControl
                variant="outlined"
                className={cx(customClasses.input, customClasses.ltrInput)}
                fullWidth
              >
                <InputLabel
                  htmlFor="outlined-adornment-password"
                  error={!!passwordErrorText || isInvalid('password')}
                >
                  {t('common.labels.password')} *
                </InputLabel>
                <OutlinedInput
                  className={customClasses.outlinedInput}
                  id="outlined-adornment-password"
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={state?.password}
                  onChange={onChange}
                  error={!!passwordErrorText || isInvalid('password')}
                  autoComplete="off"
                  endAdornment={
                    <InputAdornment position="end">
                      <AWIconButton
                        role="show-password-button"
                        aria-label="toggle password visibility"
                        onClick={handleClickShowPassword}
                        edge="end"
                        size="large"
                      >
                        {showPassword ? <Visibility /> : <VisibilityOff />}
                      </AWIconButton>
                    </InputAdornment>
                  }
                />
                <FormHelperText
                  id="my-helper-text"
                  component="div"
                  error={!!passwordErrorText || isInvalid('password')}
                >
                  {getValidationError('password')
                    ? getValidationError('password')
                    : passwordErrorText || ''}
                </FormHelperText>
              </FormControl>
              <AWBox>
                <AWButton
                  type="submit"
                  fullWidth
                  className={customClasses.marginTop}
                  variant="contained"
                  disabled={loadingInButton}
                >
                  {loadingInButton ? <Loading color="white" /> : t('common.labels.enter')}
                </AWButton>
                <AWTypography variant="subtitle1" className={cx(customClasses.forgetPassword)}>
                  <Link component={RouterLink} to="/recover-password?step=1" role="forget-password">
                    {t('auth.messages.forgetPassword')}
                  </Link>
                </AWTypography>
              </AWBox>
            </form>
            {!isMobile && <LinkToRegister />}
          </AWBox>
          <AWBox className={isMobile ? customClasses.mobileMode : ''}>
            {isMobile && <LinkToRegister />}
            <SocialMedia />
          </AWBox>
        </Container>
      </Grid>
    </Grid>
  );
};

const LinkToRegister = () => {
  const { classes: customClasses } = useStyles();
  const { t } = useTranslation();
  return (
    <AWTypography variant="subtitle1" className={customClasses.goRegister}>
      {t('auth.strings.doNotHaveAccount')}{' '}
      <Link
        component={RouterLink}
        to="/register?step=1"
        variant="subtitle1"
        className={customClasses.loginLink}
      >
        {t('auth.labels.register')}
      </Link>
    </AWTypography>
  );
};

Details.propTypes = {
  isMobile: PropTypes.bool,
};

export default Details;
