import axios, { AxiosError, AxiosResponse } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import snackbarUtils from 'utils/snackbarUtils';
import { apiClient } from './api-client';
import { ApiMethods } from '../types/api-methods.type';
import servicesUtils from './utils';
import { ApiCallQueueItem } from './types/api-call-queue';
import { ApiCallResponse } from './types/api-call-response.type';
import { ApiCallError } from './types/api-call-error.type';
import { RefreshToken } from './types/refresh-token.type';
import { ServicesParameters } from './types/services-parameters.type';
import { CheckStatusApiResponse } from '../types/check-status-api-response.type';
import { CheckStatusResponse } from '../types/check-status-response.type';
import { refreshTokenAsync } from './refresh-token';
import { checkStatusAsync } from './check-status';
import i18n from 'i18n';

export const apiCallQueue: Map<string, ApiCallQueueItem> = new Map();

let refreshTokenPromise: Promise<RefreshToken | null> | null = null;

type Resolve = ApiCallResponse<any> | CheckStatusResponse;
type Reject = ApiCallError | CheckStatusResponse;

const Services3 = (servicesOptions: ServicesParameters) =>
  new Promise(async (resolve: (value: Resolve) => void, reject: (reason: Reject) => void) => {
    const { options, data, headers, id } = servicesOptions;
    const {
      apiVersion,
      domainRoot,
      method,
      path,
      queryObject,
      responseType,
      commandName,
      checkStatusOveredFunction,
    } = options;
    const apiMethods = await import(`../api-methods/${domainRoot}/index`);

    const {
      authorized = true,
      checkStatus = false,
      defaultMethod,
      domainCheckStatus,
      httpMethod,
      description,
      params,
      showSnackbar = true,
      triesForCheckStatus,
      delayForCheckStatus,
      successfulMessage,
      unsuccessfulMessage,
    }: ApiMethods = apiMethods.default[method ?? domainRoot];

    const { baseURL } = apiClient.defaults;
    const apiCallId = id ?? uuidv4();
    const cancelToken = axios.CancelToken;
    const source = cancelToken.source();

    const checkStatusDelay = delayForCheckStatus;
    const checkStatusTries = triesForCheckStatus;

    const user = localStorage.getItem('user');
    const tokens = localStorage.getItem('tokens');

    const url = servicesUtils.generateURL({
      apiVersion,
      domainRoot,
      defaultMethod,
      path,
      queryObject,
    });

    const axiosClientInfo = {
      method: httpMethod,
      url,
      data: data ?? null,
      headers,
      responseType: responseType ?? 'json',
      withCredentials: true,
    };

    const apiCallQueueItem: ApiCallQueueItem = {
      options,
      data,
      headers,
      id: apiCallId,
      cancelToken,
      source,
      type: checkStatus ? 'command' : 'query',
    };

    const logOut = () => {
      if (window.location.pathname !== '/login' && window.location.pathname !== '/') {
        window.location.href = '/login';
      }
    };

    try {
      if (authorized === false || (tokens && user)) {
        apiCallQueue.set(apiCallQueueItem.id, apiCallQueueItem);
        const axiosResponse: AxiosResponse = await apiClient({
          ...axiosClientInfo,
          cancelToken: source.token,
        });

        let response: ApiCallResponse<any> | CheckStatusResponse =
          servicesUtils.axiosResponseConverter(axiosResponse as AxiosResponse);

        switch (response.statusCode) {
          case 200:
          case 201:
            if (response.success) {
              //TODO: response.success --> If Error had not happened
              //TODO: Handle Validation Error --> Remove That and handle in catch
              if (checkStatus) {
                const checkStatusApiResponse: CheckStatusApiResponse = (await checkStatusAsync({
                  url: `${baseURL}/api/v1/${domainCheckStatus}/status/${response.config.headers['X-Correlation-ID']}`,
                  params: { tries: checkStatusTries, delay: checkStatusDelay },
                  checkStatusOveredFunction,
                  commandName,
                  unsuccessfulMessage,
                  apiCallId,
                  apiCallQueue,
                })) as CheckStatusApiResponse;

                if (checkStatusApiResponse.success) {
                  response = servicesUtils.checkStatusResponseConverter(
                    axiosResponse as AxiosResponse,
                    checkStatusApiResponse,
                  );
                  if (showSnackbar) {
                    snackbarUtils.success(
                      successfulMessage || i18n.t('common.messages.successfulMessage'),
                    );
                  }
                }
              } else if (showSnackbar && ['POST', 'PATCH', 'PUT', 'DELETE'].includes(httpMethod)) {
                snackbarUtils.success(i18n.t('common.messages.successfulMessage'));
              }
            } else if (response.success === false) {
              if (response.meta?.message) {
                snackbarUtils.error(response.meta?.message);
              }
              //TODO: response.success === false
              //TODO: Handle Validation Error --> Remove That and handle in catch
              if (response?.message) {
                snackbarUtils.error(response?.message);
              }
            }
            apiCallQueue.delete(apiCallId);
            break;
          default:
            break;
        }
        resolve(response);
      }
    } catch (axiosError) {
      const error = axiosError as AxiosError | AxiosResponse;

      let errorResponse: ApiCallError | CheckStatusResponse;

      //* 200 -> For Check Status Response
      if (error.status === 200) {
        errorResponse = servicesUtils.checkStatusErrorConverter(
          error as AxiosResponse<CheckStatusApiResponse>,
        );
      } else {
        errorResponse = servicesUtils.axiosErrorConverter(error as AxiosError);
      }

      switch (errorResponse.statusCode) {
        case 200:
          if (errorResponse.success === false && showSnackbar) {
            servicesUtils
              .meaningfulMessage(errorResponse as CheckStatusResponse, unsuccessfulMessage)
              .map((message) => {
                snackbarUtils.error(message);
              });
          }
          apiCallQueue.delete(apiCallId);
          break;
        case 500:
          if (showSnackbar) snackbarUtils.error(i18n.t('common.errors.500ErrorMessage'));
          apiCallQueue.delete(apiCallId);
          break;

        case 404:
          if (showSnackbar) snackbarUtils.error(i18n.t('common.errors.404ErrorMessage'));
          apiCallQueue.delete(apiCallId);
          break;

        case 429:
          if (showSnackbar) snackbarUtils.error(i18n.t('common.errors.429ErrorMessage'));
          apiCallQueue.delete(apiCallId);
          break;

        case 403:
          if (showSnackbar) snackbarUtils.error(i18n.t('common.errors.403ErrorMessage'));
          apiCallQueue.delete(apiCallId);
          break;

        case 401:
          if (tokens && user) {
            //* Cancel all API requests when a 401 occurs
            apiCallQueue.forEach((apiCallItem) => {
              apiCallItem.source.cancel('Request canceled due to 401 Unauthorized error');
            });

            if (!refreshTokenPromise) {
              //* Only create a new refresh token request if none is in progress
              refreshTokenPromise = refreshTokenAsync().finally(() => {
                //* Reset the promise after completion
                refreshTokenPromise = null;
              });
            }

            try {
              const refreshToken = await refreshTokenPromise;

              if (refreshToken && refreshToken.status === 201) {
                localStorage.setItem('tokens', JSON.stringify(refreshToken.data));

                //* Retry all API calls in the queue after successful token refresh
                apiCallQueue.forEach((apiCallItem) => {
                  Services3({
                    options: apiCallItem.options,
                    data: apiCallItem.data,
                    headers: apiCallItem.headers,
                    id: apiCallItem.id,
                  });
                });
              } else {
                localStorage.removeItem('tokens');
                localStorage.removeItem('user');
                apiCallQueue.clear();
                logOut();
              }
            } catch (refreshError) {
              logOut();
            }
          } else {
            //* Should not push failed login into api call queue
            apiCallQueue.clear();
          }
          break;
        default:
          break;
      }

      //TODO: Handle rejection in slice
      resolve(errorResponse as any);
      // reject(errorResponse);
    }
  });

export default Services3;
