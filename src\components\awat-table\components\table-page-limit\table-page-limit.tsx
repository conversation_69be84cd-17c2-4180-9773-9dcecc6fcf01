import { useTranslation } from 'react-i18next';
import { makeStyles } from 'tss-react/mui';

import AWBox from '../../../AWComponents/AWBox';
import AWTypography from '../../../AWComponents/AWTypography';
import { useAWTableContext } from '../../context/table-context';
import { MenuItem, Select } from '@mui/material';

const useStyles = makeStyles()((theme) => ({
  root: {
    display: 'flex',
    alignItems: 'center',
    margin: theme.spacing(0, 1, 0, 0.5),
  },
  label: {
    fontSize: 12,
    color: theme.palette.text.gray,
    margin: theme.spacing(0, 0, 0, 0.5),
  },
  textfield: {
    height: 30,
    background: '#fff',
    borderRadius: 2,
  },
}));

const AWTablePageLimit = () => {
  const { t } = useTranslation();
  const { pagination, loading } = useAWTableContext();
  const { pageLimit, onChangePageLimit, pageLimitOptions, showLimit } = pagination;
  const { classes } = useStyles();

  return (
    <>
      {showLimit && (
        <AWBox className={classes.root}>
          <AWTypography className={classes.label}>{t('common.labels.pageLimit')}:</AWTypography>
          <Select
            value={String(pageLimit)}
            onChange={onChangePageLimit}
            disabled={loading}
            type="number"
            size="small"
            className={classes.textfield}
          >
            {pageLimitOptions.map((item: { value: number; label: string }) => (
              <MenuItem key={`PAGE_LIMIT_OPTION_${item.value}`} value={item.value}>
                {item.label}
              </MenuItem>
            ))}
          </Select>
        </AWBox>
      )}
    </>
  );
};

export default AWTablePageLimit;
