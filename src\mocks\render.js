import React from 'react';

import PropTypes from 'prop-types';
import { render } from '@testing-library/react';
import { MemoryRouter, Route, Router, Routes, useParams } from 'react-router-dom';
import { createMemoryHistory } from 'history';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { Provider } from 'react-redux';

import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import { TssCacheProvider } from 'tss-react';
import rtlPlugin from 'stylis-plugin-rtl';
import mediaQuery from 'css-mediaquery';
import '../i18n';
import { setupStore } from 'store';
import { AuthContext } from '../contexts/authContext';
import { UserContext } from '../contexts/userContext';
import defaultTheme from '../themes/default';
import { LayoutContext } from '../contexts/layoutContext';
import { MainLayoutStylesContext } from '../contexts/mainLayoutStylesContext';

const cacheRtl = createCache({
  key: 'muirtl',
  stylisPlugins: [rtlPlugin],
  prepend: true,
});

const tssCache = createCache({
  key: 'tss',
});

const createMatchMedia = (width) => (query) => ({
  matches: mediaQuery.match(query, { width }),
  addListener: () => {},
  removeListener: () => {},
});

const customRender = (
  ui,
  options,
  {
    path = '',
    initialEntries = ['/'],
    preloadedState = {},
    theme = createTheme(),
    mainLayoutStyles = { stylesGenerator: {} },
    ...renderOptions
  },
) => {
  const history = createMemoryHistory({ initialEntries });
  const innerWidth = defaultTheme.breakpoints.values[options.size] - 1;

  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: innerWidth,
  });
  window.dispatchEvent(new Event('resize'));
  window.matchMedia = createMatchMedia(innerWidth);

  const store = setupStore();

  const Wrapper = ({ children }) => (
    <Provider store={store}>
      <AuthContext.Provider
        value={{
          auth: null,
          setAuth: () => {},
        }}
      >
        <UserContext.Provider
          value={{
            signedInUser: null,
            setSignedInUser: () => {},
          }}
        >
          <CacheProvider value={cacheRtl}>
            <TssCacheProvider value={tssCache}>
              <ThemeProvider theme={theme}>
                <LayoutContext.Provider
                  value={{
                    showSideBar: false,
                    setShowSideBar: () => {},
                    showAppBar: false,
                    setShowAppBar: () => {},
                    title: 'test',
                    setTitle: () => {},
                    iconTopBar: false,
                    setIconTopBar: () => {},
                    showBottomBar: false,
                    setShowBottomBar: () => {},
                    showBreadcrumbs: false,
                    setShowBreadcrumbs: () => {},
                    breadcrumbsInfo: null,
                    setBreadcrumbsInfo: () => {},
                    sideMenus: [],
                    setSideMenus: () => {},
                    bottomBarMenus: [],
                    setBottomBarMenus: () => {},
                  }}
                >
                  <MainLayoutStylesContext.Provider value={mainLayoutStyles}>
                    <MemoryRouter initialEntries={initialEntries}>
                      <Routes>
                        <Route path={path} element={children} />
                      </Routes>
                    </MemoryRouter>
                  </MainLayoutStylesContext.Provider>
                </LayoutContext.Provider>
              </ThemeProvider>
            </TssCacheProvider>
          </CacheProvider>
        </UserContext.Provider>
      </AuthContext.Provider>
    </Provider>
  );

  Wrapper.propTypes = {
    children: PropTypes.node.isRequired,
  };

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    history,
    store,
    initialEntries,
  };
};

export { customRender };
