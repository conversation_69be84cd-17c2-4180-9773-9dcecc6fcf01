import React, { useEffect, useState } from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import Jo<PERSON> from 'joi';
import { useNavigate } from 'react-router-dom';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import {
  loginByPasswordAsync as loginByPasswordAsync_,
  loginByPasswordReset as loginByPasswordReset_,
  getLastLoginDataAsync as getLastLoginDataAsync_,
} from '../authSlice';
import { useAuth } from '../../../contexts/authContext';
import { useUser } from '../../../contexts/userContext';
import { withValidation } from '../../../common/validation';
import DesktopLogin from './desktop';
import MobileLogin from './mobile';
import { LoginContext } from '../../../contexts/pageContext/auth/loginContext';
import snackbarUtils from '../../../utils/snackbarUtils';
import { momentUTC } from '../../../utils';
import { encryptPassword } from '../../../utils/encryption';

const Login = (props) => {
  const {
    auth,
    isInvalid,
    getValidationError,
    loginByPasswordAsync,
    getLastLoginDataAsync,
    loginByPasswordReset,
    validationOnChange,
    validationOnSubmit,
  } = props;
  const navigate = useNavigate();
  const { setAuth } = useAuth();
  const { setSignedInUser } = useUser();
  const [showPassword, setShowPassword] = useState(false);
  const [usernameErrorText, setUsernameErrorText] = useState('');
  const [passwordErrorText, setPasswordErrorText] = useState('');
  const [loadingInButton, setLoadingInButton] = useState(false);
  const [submitingLogin, setSubmitingLogin] = useState(false);
  const [state, setState] = useState({});

  useEffect(() => () => loginByPasswordReset(), [loginByPasswordReset]);

  useEffect(() => {
    setState({
      username: process.env.REACT_APP_USERNAME || '',
      password: process.env.REACT_APP_PASSWORD || '',
    });
    // setAuth();
    // setSignedInUser();
  }, []);

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const submitLogin = async (event) => {
    setUsernameErrorText('');
    setPasswordErrorText('');
    event.preventDefault();
    if (validationOnSubmit(state) && !isInvalid('username') && !isInvalid('password')) {
      const options = {
        data: {
          password: encryptPassword(state.password),
          username: state.username,
        },
      };
      setLoadingInButton(true);
      setSubmitingLogin(true);
      loginByPasswordAsync(options);
    }
  };

  useEffect(() => {
    if (submitingLogin) {
      if (auth.loginByPassword.status === 'idle') {
        setLoadingInButton(false);
      }
      if (auth.loginByPassword.status === 'idle' && auth.loginByPassword.data?.refresh_token) {
        setSubmitingLogin(false);
        setAuth(auth.loginByPassword.data);
        setSignedInUser(auth.loginByPassword.data.user);
        navigate('/organizations', { replace: true });
        setTimeout(() => {
          getLastLoginDataAsync(auth.loginByPassword.data.user?.id).then((data) => {
            if (data?.payload?.data !== undefined && typeof data?.payload?.data === 'string') {
              snackbarUtils.info(`آخرین ورود شما: ${momentUTC(new Date(data.payload.data))}`);
            }
          });
        }, 5000);
      }
    }
  }, [
    submitingLogin,
    auth.loginByPassword.status,
    auth.loginByPassword.data,
    setAuth,
    setSignedInUser,
    navigate,
    getLastLoginDataAsync,
  ]);

  const onChange = (e) => {
    validationOnChange(e);
    const stt = { ...state };
    stt[e.target.name] = e.target.value;
    setState(stt);
  };

  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));

  const handleRenderLayout = () => {
    if (mobile) return <MobileLogin isMobile />;
    if (tablet) return <DesktopLogin />;
    if (desktop) return <DesktopLogin />;

    return null;
  };

  return (
    <LoginContext.Provider
      value={{
        auth,
        isInvalid,
        getValidationError,
        validationOnChange,
        submitLogin,
        state,
        onChange,
        usernameErrorText,
        passwordErrorText,
        showPassword,
        handleClickShowPassword,
        loadingInButton,
      }}
    >
      {handleRenderLayout()}
    </LoginContext.Provider>
  );
};

const mapStateToProps = (state) => {
  const { auth } = state;
  return { auth };
};

const initialState = {
  username: '',
  password: '',
};

const schema = {
  username: Joi.string()
    .min(3)
    .max(32)
    .required()
    .regex(/^[A-Za-z\d_]+$/),
  password: Joi.string().required().min(8).max(32),
};

Login.propTypes = {
  auth: PropTypes.shape({
    loginByPassword: PropTypes.shape({
      status: PropTypes.string,
      data: PropTypes.shape({
        user: PropTypes.shape({
          id: PropTypes.string,
          username: PropTypes.string,
        }),
        refresh_token: PropTypes.string,
      }),
    }),
  }).isRequired,
  isInvalid: PropTypes.func.isRequired,
  getValidationError: PropTypes.func.isRequired,
  getLastLoginDataAsync: PropTypes.func.isRequired,
  loginByPasswordAsync: PropTypes.func.isRequired,
  loginByPasswordReset: PropTypes.func.isRequired,
  validationOnChange: PropTypes.func.isRequired,
  validationOnSubmit: PropTypes.func.isRequired,
};

export default withValidation({ initialState, schema })(
  connect(mapStateToProps, {
    loginByPasswordAsync: loginByPasswordAsync_,
    loginByPasswordReset: loginByPasswordReset_,
    getLastLoginDataAsync: getLastLoginDataAsync_,
  })(Login),
);
