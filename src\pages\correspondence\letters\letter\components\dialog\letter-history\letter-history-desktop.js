import { makeStyles } from 'tss-react/mui';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

import { t } from 'i18next';
import { useLetter } from 'contexts/pageContext/letters/letterContext';
import { momentDateUTC } from 'utils';
import Loading from 'components/Loading';
import { useLetterHistoryContext } from 'contexts/pageContext/letters/letter-history/letter-history-context';
import AWBox from 'components/AWComponents/AWBox';
import AWDialog from 'components/AWComponents/AWDialog';
import LocalPrintshopOutlinedIcon from '@mui/icons-material/LocalPrintshopOutlined';
import LetterHistoryCard from './components/letter-history-card';
import DocumentsListSidebar from './components/document-list-sidebar';
import LetterForwardsFilter from './components/letter-forwards-filter';
import PrintableTable from './components/table-history-print-mode';

const useStyles = makeStyles()((theme) => ({
  appBar: {
    background: '#fff',
    color: '#000',
    height: 64,
    display: 'flex',
    position: 'relative',
    textAlign: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: 'unset',
    [theme.breakpoints.down('sm')]: {
      background: '#f5f5f5',
    },
    '& button': {
      padding: 13,
      position: 'absolute',
      right: 8,
    },
  },
  dialogContainer: {
    [theme.breakpoints.down('sm')]: {
      position: 'relative',
      top: 0,
    },
  },
  label: {},
  value: {
    color: '#000',
    fontWeight: 500,
    margin: '5px 0',
    fontSize: '0.875rem',
  },
  gridContainer: {
    padding: 'unset',
  },
  dialogPaper: {
    minHeight: 600,
    height: 600,
    overflow: 'auto',
    [theme.breakpoints.down('sm')]: {
      minWidth: 'unset',
      minHeight: 'unset',
      height: 'unset',
    },
  },
  dialogContentRoot: {
    display: 'flex',
    direction: 'ltr',
    position: 'relative',
  },
  dialogContentBox: {
    direction: 'rtl',
    display: 'flex',
    flexDirection: 'row-reverse',
  },
  daysBox: {
    minWidth: 250,
    maxWidth: 250,
    textAlign: 'center',
  },
  date: {
    color: '#6c6c6c',
    borderBottom: `3px solid ${theme.palette.primary.main}`,
    position: 'relative',
    padding: '16px 0 6px',
    '&:after': {
      content: '""',
      width: 12,
      height: 12,
      background: theme.palette.primary.main,
      borderRadius: 50,
      position: 'absolute',
      margin: 'auto',
      left: 0,
      right: 0,
      bottom: -7,
    },
  },
  cardBox: {
    padding: theme.spacing(1, 2),
  },
  line: {
    height: 3,
    background: '#655aaf',
    display: 'block',
    position: 'absolute',
    top: 46,
    width: '100%',
  },
  actionsContainer: {
    color: theme.palette.primary.main,
  },
  printableTable: {
    display: 'none',
    '@media print': {
      display: 'block',
    },
  },
}));

const DesktopLetterHistory = () => {
  const {
    letterHistoryRecords,
    openDocListSidebar,
    handleCloseDocumentListSidebar,
    checkedLetterForwardsFilter,
    setCheckedLetterForwardsFilter,
    handlePrint,
  } = useLetterHistoryContext();

  const {
    isVisibleLetterHistoryDialog,
    toggleVisibleLetterHistoryDialog,
    letterHistoryLoading,
    letters,
  } = useLetter();

  const { subject, number } = letters?.selectedLetter?.data ?? {};

  const { classes } = useStyles();

  return (
    <>
      <AWDialog
        open={isVisibleLetterHistoryDialog}
        onClose={() => toggleVisibleLetterHistoryDialog(false)}
        maxWidth="lg"
        fullWidth
        title={t('solutionHistory.labels.letter.history')}
        isTitleShow
        dialogClasses={{ container: classes.dialogContainer, paper: classes.dialogPaper }}
        contentDialogClasses={classes.dialogContentRoot}
        id="desktop-letter-history-dialog"
        showTopBorderInActions
        dialogHeaderActionButtonClassName={classes.actionsContainer}
        dialogHeaderActions={[
          {
            icon: <LocalPrintshopOutlinedIcon />,
            tooltip: t('tooltip.letter-history-print'),
            placementTooltip: 'bottom-end',
            onClick: handlePrint,
            disabled: letterHistoryLoading,
          },
        ]}
      >
        <AWBox sx={{ position: 'relative', flex: 1, overflow: 'auto', direction: 'rtl' }}>
          {/* <span className={classes.line} /> */}
          {!letterHistoryLoading && (
            <LetterForwardsFilter
              checkedLetterForwardsFilter={checkedLetterForwardsFilter}
              setCheckedLetterForwardsFilter={setCheckedLetterForwardsFilter}
            />
          )}
          <Box className={classes.dialogContentBox}>
            {letterHistoryLoading ? (
              <Loading />
            ) : (
              letterHistoryRecords?.map((item, index) => (
                <Box className={classes.daysBox}>
                  <Typography
                    key={momentDateUTC(Object.keys(item)[0])}
                    classes={{ root: classes.date }}
                  >
                    {momentDateUTC(Object.keys(item)[0])}
                  </Typography>
                  <Box className={classes.cardBox}>
                    {item[Object.keys(item)]?.map((card) => (
                      <LetterHistoryCard key={card.id} item={card} date={Object.keys(item)[0]} />
                    ))}
                  </Box>
                </Box>
              ))
            )}
          </Box>
        </AWBox>
        <DocumentsListSidebar
          open={openDocListSidebar.open}
          onClose={handleCloseDocumentListSidebar}
          data={openDocListSidebar.attachments}
        />
      </AWDialog>
      <div id="printableDiv" className={classes.printableTable}>
        <PrintableTable
          data={letterHistoryRecords}
          subject={subject}
          number={number}
          historyType="letter"
        />
      </div>
    </>
  );
};

export default DesktopLetterHistory;
