import React, { useEffect, useState } from 'react';

import { marked } from 'marked';
import { useNavigate } from 'react-router';
import useMediaQuery from '@mui/material/useMediaQuery';

import BaseChangeLogs from './change-log-base';
import { ChangeLogsContext } from '../../../contexts/pageContext/change-logs/change-logs-context';

const file = require('../change-logs.md');

const ChangeLogs = () => {
  const navigate = useNavigate();
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [markdown, setMarkdown] = useState(null);

  useEffect(() => {
    fetch(file)
      .then((res) => res.text())
      .then((text) => {
        const splittedChanges = marked.parse(text).split(/<hr>/g);
        const renderHTML = splittedChanges.reduce((result, currentElement) => {
          if (currentElement.trim()) {
            const div = document.createElement('div');
            div.setAttribute('id', 'logs');
            div.innerHTML = currentElement;
            return `${result}${div.outerHTML}`;
          }
          return result;
        }, '');

        setMarkdown(renderHTML);
      });
  }, []);

  const goBack = () => {
    navigate(-1);
  };

  return (
    <ChangeLogsContext.Provider value={{ mobile, markdown, goBack }}>
      <BaseChangeLogs />;
    </ChangeLogsContext.Provider>
  );
};

export default ChangeLogs;
