import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

export const baseUrl =
  process.env.NODE_ENV === 'production' ? process.env.REACT_APP_BACKEND_URL : '';

export const setAxiosInterceptors = () => {
  axios.defaults.baseURL = baseUrl;
  axios.interceptors.request.use((config) => {
    const { headers } = config;

    headers['X-Correlation-ID'] = config.headers['X-Correlation-ID'] || uuidv4();
    headers['X-Request-ID'] = uuidv4();
    headers['X-Causation-ID'] = uuidv4();
    return config;
  });
};

setAxiosInterceptors();

export const apiClient = axios;
