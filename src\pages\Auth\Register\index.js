import { useState, useEffect } from 'react';

import <PERSON><PERSON> from 'joi';
import { useNavigate, useLocation } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import { connect } from 'react-redux';
import Grid from '@mui/material/Grid';
import LinearProgress from '@mui/material/LinearProgress';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

import i18n from 'i18n';
import { convertDigitsToEnglish } from 'utils';
import { useCheckUserExisted } from 'hooks/useCheckUserExisted';
import { withValidation, extendedJoi } from 'common/validation';
import { useUser } from 'contexts/userContext';
import { useAuth } from 'contexts/authContext';
import { RegisterContext } from 'contexts/pageContext/auth/registerContext';
import { ReactComponent as RegisterIcon } from 'assets/images/forms/register.svg';
import { encryptPassword } from 'utils/encryption';
import { isMobileValid } from 'utils/isMobileValid';
import { baseUrl } from 'api/v1/services/api-client';
import {
  registerUserAsync as registerUserAsync_,
  usernameAsync as usernameAsync_,
  otpAsync as otpAsync_,
  otpReset as otpReset_,
  registerUserAsyncReset as registerUserAsyncReset_,
  userInfoSet as userInfoSet_,
  loginByPasswordAsync as loginByPasswordAsync_,
} from '../authSlice';
import SignUp from './SignUp';
import SignUpSuccess from './SignUpSuccess';
import ConfirmPhone from './ConfirmPhone';

const useStyles = makeStyles()({
  root: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    height: '100%',
    alignItems: 'center',
  },
  lineProgressRoot: {
    height: 8,
    backgroundColor: '#f3f3f3',
  },
  lineProgressBar: {
    borderRadius: 4,
  },
  progressBar: {
    width: '100%',
    position: 'absolute',
    top: 0,
    zIndex: 9,
  },
  mainForm: {
    display: 'flex',
    flexDirection: 'row-reverse',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100vh',
    maxWidth: 1280,
  },
  forms: {
    height: '100%',
  },
  image: {},
});

const Register = ({
  auth,
  getValidationErrorEx,
  validationOnChange,
  validationOnSubmit,
  userInfoSet,
  registerUserAsync,
  isInvalid,
  getValidationError,
  registerUserAsyncReset,
  otpReset,
  loginByPasswordAsync,
  otpAsync,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { checkUserExistAsync } = useCheckUserExisted();
  const { setAuth } = useAuth();
  const { setSignedInUser } = useUser();
  const [step, setStep] = useState(1);
  const [showFirstPassword, setShowFirstPassword] = useState(false);
  const [phonErrorText, setPhonErrorText] = useState('');
  const [usernameErrorText, setUsernameErrorText] = useState('');
  const [passwordError, setPasswordError] = useState(false);
  const [state, setState] = useState({
    mobile: '',
    username: '',
    password: '',
    checkPermission: false,
  });
  const [submitedOtp, setSubmitedOtp] = useState(false);
  const [userExisted, setUserExisted] = useState('');
  const [confirmValue, setConfirmValue] = useState('');
  const [counter, setCounter] = useState('');
  const [openDialogUserExist, setOpenDialogUserExist] = useState(false);
  const [loadingSignUp, setLoadingSignUp] = useState(false);
  const [loadingConfirmCode, setLoadingConfirmCode] = useState(false);
  const [openTermsModal, setOpenTermsModal] = useState(false);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const step1 = params.get('step');
    setStep(step1);
  }, [location.search]);

  // ----------SIGNUP---------------------------------------------
  useEffect(() => {
    if (auth.otp.data?.success === false) {
      setLoadingSignUp(false);
    }

    if (submitedOtp && auth.otp.status === 'idle' && auth.otp.data?.success) {
      setLoadingSignUp(false);
      userInfoSet({
        username: state.username,
        password: state.password,
        mobile: state.mobile,
      });
      navigate({
        pathname: '/register',
        search: '?step=2',
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [submitedOtp, auth.otp.data, auth.otp.status]);

  useEffect(() => {
    setUserExisted('');
  }, [state.username]);

  useEffect(() => {
    if (userExisted === false) {
      const options = {
        data: {
          id: uuidv4(),
          mobile: state.mobile.split(' ').join(''),
        },
      };
      setCounter(120);
      otpAsync(options);
      setUserExisted('');
    }
  }, [otpAsync, state.mobile, userExisted]);

  useEffect(() => {
    setSubmitedOtp(auth.otp.status === 'idle' && auth.otp.data?.success);
  }, [auth.otp.data, auth.otp.status]);

  const submitSignUp = async (e) => {
    e.preventDefault();
    setPhonErrorText('');
    setUsernameErrorText('');
    setPasswordError(false);
    if (
      validationOnSubmit(state) &&
      !isInvalid('mobile') &&
      !isInvalid('username') &&
      !isInvalid('password') &&
      !isInvalid('checkPermission')
    ) {
      const realNumber = state.mobile.split(' ').join('').replace(/_/g, '');

      if (!isMobileValid(realNumber)) {
        setPhonErrorText(t('auth.messages.mobileInvalid'));
      }

      if (phonErrorText === '') {
        setLoadingSignUp(true);
        try {
          const path = state.username;
          await checkUserExistAsync(`${baseUrl}/api/v1/users/username/${path}`, null, {
            id: '',
          });
          setUserExisted(false);
        } catch (e2) {
          if (e2.message === 'existed') {
            setUserExisted(true);
            setLoadingSignUp(false);
          }
        }
      }
    }
  };

  const onChange = (e) => {
    validationOnChange(e);
    const stt = { ...state };
    stt[e.target.name] = e.target.value;
    setState(stt);
  };

  const onChangeCheckPermission = (_, newValue) => {
    validationOnChange({ target: { value: newValue || '', name: 'checkPermission' } });
    const stt = { ...state };
    stt.checkPermission = newValue;
    setState(stt);
  };

  // --------Confirm Phone-----------------------------------------
  useEffect(() => {
    let interval;

    if (step === '2') {
      interval = setInterval(() => {
        if (counter > 0) setCounter(counter - 1);
      }, 1000);
    }

    return () => {
      clearInterval(interval);
    };
  });

  const changeConfirm = (e) => {
    setConfirmValue(e.target.value);
  };

  useEffect(() => {
    if (auth?.registerUser?.data?.success) {
      navigate(
        {
          pathname: '/register',
          search: '?step=3',
        },
        { replace: true },
      );
    }
  }, [auth?.registerUser?.data, navigate]);

  useEffect(() => {
    if (!auth.userInfo?.mobile) {
      navigate('/register?step=1');
    }
  }, [auth.userInfo?.mobile, navigate]);

  useEffect(() => {
    setLoadingConfirmCode(auth.registerUser.status === 'loading');
  }, [auth.registerUser.status]);

  useEffect(() => {
    setConfirmValue('');
  }, [step]);

  useEffect(() => {
    if (confirmValue.length === 0) {
      registerUserAsyncReset();
    }
  }, [confirmValue, registerUserAsyncReset]);

  const submitConfirm = (e) => {
    e.preventDefault();

    if (auth.userInfo?.mobile && auth.userInfo?.password && auth.userInfo?.username) {
      const options = {
        data: {
          id: uuidv4(),
          mobile: auth.otp.data.mobile,
          password: encryptPassword(auth.userInfo?.password),
          username: auth.userInfo?.username,
        },
        headers: {
          [`otp_${auth.otp.data.mobile}_${auth.otp.data.id}`]: convertDigitsToEnglish(confirmValue),
        },
      };
      registerUserAsync(options);
    }
  };

  const resendOtp = () => {
    setCounter(120);
    setConfirmValue('');
    const options = {
      data: {
        id: uuidv4(),
        mobile: auth.userInfo.mobile?.split(' ').join(''),
      },
    };
    otpAsync(options);
  };

  const handleEditInfo = () => {
    registerUserAsyncReset();
    otpReset();
    navigate({
      pathname: '/register',
      search: '?step=1',
    });
  };

  const handleToLogin = () => {
    registerUserAsyncReset();
    otpReset();
    navigate('/login');
  };

  const closeUserExistedDialog = (_, reason) => {
    if (reason !== 'backdropClick') {
      setOpenDialogUserExist();
    }
  };

  // -------------------------success signup--------------------
  useEffect(() => {
    if (auth.loginByPassword.data?.refresh_token) {
      setAuth(auth.loginByPassword.data);
      setSignedInUser(auth.loginByPassword.data.user);
      navigate('/organizations', { replace: true });
    }
  }, [auth.loginByPassword.data, navigate, setAuth, setSignedInUser]);

  const submitLogin = () => {
    const options = {
      data: {
        password: encryptPassword(auth.userInfo?.password),
        username: auth.userInfo?.username,
      },
    };
    loginByPasswordAsync(options);
  };

  const linearProgressValue = (_step) => {
    if (_step === '1') return 0;
    if (_step === '2') return 50;
    if (_step === '3') return 100;

    return 0;
  };

  const handleOpenTermsModal = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setOpenTermsModal(true);
  };

  const handleCloseTermsModal = () => {
    setOpenTermsModal(false);
  };

  return (
    <RegisterContext.Provider
      value={{
        auth,
        getValidationErrorEx,
        isInvalid,
        getValidationError,
        otpAsync,
        submitSignUp,
        state,
        onChange,
        onChangeCheckPermission,
        phonErrorText,
        userExisted,
        usernameErrorText,
        passwordError,
        setPasswordError,
        showFirstPassword,
        setShowFirstPassword,
        loadingSignUp,
        counter,
        changeConfirm,
        confirmValue,
        resendOtp,
        submitConfirm,
        handleEditInfo,
        openDialogUserExist,
        closeUserExistedDialog,
        handleToLogin,
        loadingConfirmCode,
        submitLogin,
        openTermsModal,
        handleOpenTermsModal,
        handleCloseTermsModal,
      }}
    >
      <div className={classes.root}>
        <div className={classes.progressBar}>
          <LinearProgress
            variant="determinate"
            value={linearProgressValue(step)}
            classes={{ root: classes.lineProgressRoot, bar: classes.lineProgressBar }}
          />
        </div>
        <Grid container className={classes.mainForm} columns={16}>
          <Grid item lg={9} display={{ xs: 'none', lg: 'block' }}>
            <RegisterIcon className={classes.image} />
          </Grid>
          <Grid item xs={16} lg={7} className={classes.forms}>
            {step === '1' && <SignUp />}
            {step === '2' && <ConfirmPhone />}
            {step === '3' && <SignUpSuccess />}
          </Grid>
        </Grid>
      </div>
    </RegisterContext.Provider>
  );
};

const mapStateToProps = (state) => {
  const { auth } = state;
  return {
    auth,
  };
};
const initialState = {
  mobile: '',
  username: '',
  password: '',
};

const schema = {
  mobile: Joi.string()
    .required()
    .min(11)
    .max(11)
    .pattern(/^09/)
    .message({
      'string.pattern.base': i18n.t('auth.messages.mobileInvalid'),
    }),
  username: Joi.string()
    .required()
    .min(3)
    .max(32)
    .regex(/^[A-Za-z\d_-]+$/),
  password: extendedJoi.passwordComplexity().required(),
  checkPermission: Joi.bool().required(),
};

Register.propTypes = {
  auth: PropTypes.shape({
    loginByPassword: PropTypes.shape({
      data: PropTypes.shape({
        user: PropTypes.shape({
          id: PropTypes.string,
          username: PropTypes.string,
        }),
        refresh_token: PropTypes.string,
      }),
    }),
    otp: PropTypes.shape({
      status: PropTypes.string,
      data: PropTypes.shape({
        success: PropTypes.bool,
        mobile: PropTypes.string,
        id: PropTypes.string,
      }),
    }),
    registerUser: PropTypes.shape({
      status: PropTypes.string,
      data: PropTypes.shape({
        id: PropTypes.string,
        username: PropTypes.string,
        success: PropTypes.bool,
      }),
    }),
    userInfo: PropTypes.shape({
      username: PropTypes.string,
      password: PropTypes.string,
      mobile: PropTypes.string,
    }),
  }).isRequired,
  getValidationErrorEx: PropTypes.func.isRequired,
  validationOnChange: PropTypes.func.isRequired,
  validationOnSubmit: PropTypes.func.isRequired,
  userInfoSet: PropTypes.func.isRequired,
  isInvalid: PropTypes.func.isRequired,
  getValidationError: PropTypes.func.isRequired,
  registerUserAsync: PropTypes.func.isRequired,
  registerUserAsyncReset: PropTypes.func.isRequired,
  otpReset: PropTypes.func.isRequired,
  loginByPasswordAsync: PropTypes.func.isRequired,
  otpAsync: PropTypes.func.isRequired,
};

export default withValidation({ initialState, schema })(
  connect(mapStateToProps, {
    userInfoSet: userInfoSet_,
    registerUserAsync: registerUserAsync_,
    usernameAsync: usernameAsync_,
    otpAsync: otpAsync_,
    otpReset: otpReset_,
    registerUserAsyncReset: registerUserAsyncReset_,
    loginByPasswordAsync: loginByPasswordAsync_,
  })(Register),
);
