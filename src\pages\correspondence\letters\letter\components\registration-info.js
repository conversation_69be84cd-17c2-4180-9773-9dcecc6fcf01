import PropTypes from 'prop-types';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Autocomplete from '@mui/material/Autocomplete';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import { useLetter } from '../../../../../contexts/pageContext/letters/letterContext';
import PositionEmployeeInput from './position-employee-input';
import AWCustomTextField from '../../../../../components/AWComponents/AWLetterNumberTextField';
import { formatLetterNumber } from '../../../../../utils/letterNumberUtils';
import AWDatePicker from 'components/AWComponents/AWDateComponent/AWDatePicker';

const useStyles = makeStyles()((theme) => ({
  dataPicker: {
    display: 'inherit !important',
    zIndex: 99,
    width: '100%',
    '& .MuiInputBase-root': {
      paddingLeft: 0,
    },
  },
  numberLetter: {
    display: 'flex',
    '& .label': {
      color: theme.palette.text.gray,
    },
  },
  grid: {
    zIndex: 100,
  },
  autoComplete: {
    '& .MuiInputBase-root': {
      height: 'unset',
      minHeight: '42px!important',
    },
  },
}));

const RegistrationInfo = ({ isMobile }) => {
  const { classes } = useStyles();
  const {
    type,
    isInvalid,
    getValidationError,
    typeOfLetter,
    state,
    onChange,
    priorities,
    confidentialities,
    handleSelectedDate,
    signerSelection,
    handleChangeSigner,
    handleSelectedDateIncomingLetter,
    handleIncomingLetterNumberChange,
    secretariatSelection,
    handleChangeSecretariat,
    indicatorSelection,
    indicatorsActived,
    handleChangeIndicator,
    confidentialitySelection,
    handleChangeConfidentiality,
    prioritySelection,
    handleChangePriority,
    isLetterEditable,
    isLetterEditableIndicator,
    isIncomingNumberAndDateDisable,
    selectedLetter,
    organizationId,
    openPositionEmployeeTreeDialogSigner,
    handleOpenPositionEmployeeTreeDialogSigner,
    handleClosePositionEmployeeTreeDialogSigner,
    activeSecretariats,
    currentSecretariat,
  } = useLetter();

  const { t } = useTranslation();
  return (
    <Grid container spacing={2} className={classes.grid}>
      {isMobile && type !== 'create' && (
        <Grid item sm={6} xs={12} classes={{ root: classes.numberLetter }}>
          <Typography variant="subtitle1" className="label">
            {t('commands.letters.numberLetter')}
          </Typography>
          <Typography variant="subtitle1">
            {' '}
            {formatLetterNumber(selectedLetter?.number) ??
              t('commands.letters.notNumberLetter')}{' '}
          </Typography>
        </Grid>
      )}
      <Grid item sm={6} xs={12}>
        <AWDatePicker
          name="date"
          id="date-picker"
          label={`${t('correspondence.letters.labels.dateLetters')}*`}
          value={new Date(state.date)}
          onChange={handleSelectedDate}
          isInvalid={isInvalid}
          getValidationError={getValidationError}
          disabled={isLetterEditable()}
          error={isInvalid('date')}
        />
      </Grid>
      {typeOfLetter !== 'incoming' ? (
        <Grid item sm={6} xs={12}>
          <PositionEmployeeInput
            organizationId={organizationId}
            label={t('correspondence.letters.labels.signers')}
            name="signer"
            selectedPositionEmployee={signerSelection}
            handleChangeAutocomplete={handleChangeSigner}
            openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogSigner}
            handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogSigner}
            handleClosePositionEmployeeViewerDialog={handleClosePositionEmployeeTreeDialogSigner}
            onSubmitPositionEmployee={handleChangeSigner}
            isMobile={isMobile}
            isLetterEditable={isLetterEditable}
            isInvalid={isInvalid}
            getValidationError={getValidationError}
            showFreePosition={false}
          />
        </Grid>
      ) : (
        !isMobile && <Grid item sm={6} xs={12} />
      )}
      {typeOfLetter === 'incoming' && (
        <>
          <Grid item sm={6} xs={12}>
            <AWDatePicker
              name="incomingDate"
              id="incoming-date"
              label={`${t('common.messages.dateReceiveLetter')}*` || ''}
              value={new Date(state.incomingDate)}
              onChange={handleSelectedDateIncomingLetter}
              isInvalid={isInvalid}
              getValidationError={getValidationError}
              error={isInvalid('incomingDate')}
              helperText={getValidationError('incomingDate') || ''}
              disabled={isIncomingNumberAndDateDisable()}
              clearable
            />
          </Grid>
          <Grid item sm={6} xs={12}>
            <AWCustomTextField
              id="incomingNumber"
              name="incomingNumber"
              fullWidth
              label={`${t('forwardLetter.labels.incomingNumber')}*` || ''}
              variant="outlined"
              value={state.incomingNumber}
              onChange={handleIncomingLetterNumberChange}
              error={isInvalid('incomingNumber')}
              helperText={getValidationError('incomingNumber') || ''}
              disabled={isIncomingNumberAndDateDisable()}
            />
          </Grid>
        </>
      )}
      <Grid item sm={6} xs={12}>
        <Autocomplete
          noOptionsText={t('common.messages.noOptions')}
          id="secretariat"
          name="secretariat"
          value={currentSecretariat || null}
          onChange={(event, newValue) => handleChangeSecretariat(event, newValue)}
          options={activeSecretariats || []}
          getOptionLabel={(option) => option.name || ''}
          // filterSelectedOptions
          className={classes.autoCompleteRecipient}
          disabled={isLetterEditableIndicator()}
          renderInput={(params) => (
            <TextField
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...params}
              variant="outlined"
              label={`${t('secretariats.labels.registerSecretariat')}*` || ''}
              error={isInvalid('secretariat')}
              helperText={getValidationError('secretariat') || ''}
            />
          )}
        />
      </Grid>
      <Grid item sm={6} xs={12}>
        <Autocomplete
          noOptionsText={t('common.messages.noOptions')}
          id="indicator"
          name="indicator"
          value={indicatorSelection}
          onChange={(event, newValue) => handleChangeIndicator(event, newValue)}
          options={indicatorsActived || []}
          getOptionLabel={(option) => option.name || ''}
          disabled={isLetterEditableIndicator() || !state.secretariat?.id}
          // filterSelectedOptions
          className={classes.autoCompleteRecipient}
          renderInput={(params) => (
            <TextField
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...params}
              variant="outlined"
              label={`${t('secretariats.labels.registrarIndicator')}*` || ''}
              error={isInvalid('indicator')}
              helperText={getValidationError('indicator') || ''}
            />
          )}
        />
      </Grid>
      <Grid item sm={6} xs={12}>
        <Autocomplete
          noOptionsText={t('common.messages.noOptions')}
          id="confidentialities"
          name="confidentiality"
          options={confidentialities}
          getOptionLabel={(option) => option.label || ''}
          filterSelectedOptions
          value={confidentialitySelection || null}
          onChange={(event, newValue) => handleChangeConfidentiality(event, newValue)}
          disabled={isLetterEditable()}
          renderInput={(params) => (
            <TextField
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...params}
              variant="outlined"
              label={t('secretariats.labels.confidentiality')}
              error={isInvalid('confidentiality')}
              helperText={getValidationError('confidentiality') || ''}
            />
          )}
        />
      </Grid>
      <Grid item sm={6} xs={12}>
        <Autocomplete
          noOptionsText={t('common.messages.noOptions')}
          id="priority"
          name="priority"
          options={priorities}
          getOptionLabel={(option) => option.label || ''}
          filterSelectedOptions
          // getOptionSelected={(d) => priorities.find((t) => t.label === d.label)}
          value={prioritySelection || null}
          onChange={(event, newValue) => handleChangePriority(event, newValue)}
          disabled={isLetterEditable()}
          renderInput={(params) => (
            <TextField
              // eslint-disable-next-line react/jsx-props-no-spreading
              {...params}
              label={t('secretariats.labels.priority')}
              variant="outlined"
              error={isInvalid('priority')}
              helperText={getValidationError('priority') || ''}
            />
          )}
        />
      </Grid>
    </Grid>
  );
};
RegistrationInfo.propTypes = {
  isMobile: PropTypes.bool,
};

RegistrationInfo.defaultProps = {
  isMobile: false,
};
export default RegistrationInfo;
