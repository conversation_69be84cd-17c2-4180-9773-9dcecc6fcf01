import { FC } from 'react';

import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ToggleButton from '@mui/material/ToggleButton';
import CheckIcon from '@mui/icons-material/Check';
import CloseIcon from '@mui/icons-material/Close';

import AWBox from 'components/AWComponents/AWBox';
import AWButton from 'components/AWComponents/AWButton';
import AWDialog from 'components/AWComponents/AWDialog';
import AWTypography from 'components/AWComponents/AWTypography';
import { useTranslation } from 'react-i18next';
import { makeStyles } from 'tss-react/mui';
import AWTextField from 'components/AWComponents/AWTextField';
import dubi from 'assets/images/dubi.svg';
import Loading from 'components/Loading';
import AWIconButton from 'components/AWComponents/AWIconButton';

const useStyles = makeStyles()((theme) => ({
  textfield: {
    margin: theme.spacing(1, 0),
    '& .MuiOutlinedInput-root': {
      borderRadius: '20px 20px 0px 20px',
      backgroundColor: theme.palette.background.lightPrimary,
    },
  },
  textfieldContent: {
    maxHeight: '200px',
    overflowY: 'auto',
  },
  popoverContainer: {
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'start',
    width: '600px',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'start',
  },
  typography: {
    marginBottom: theme.spacing(2),
    textAlign: 'center',
  },
  toggleButtonGroup: {
    marginBottom: theme.spacing(2),
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
  },
  toggleButtonContainer: {
    display: 'flex',
    gap: theme.spacing(2),
    width: '100%',
    justifyContent: 'center',
  },
  toggleButton: {
    borderRadius: '42px',
    flex: 1,
    padding: theme.spacing(1),
    border: '2px solid',
    borderColor: theme.palette.background.previewImage,
    '&.Mui-selected': {
      borderColor: theme.palette.text.link,
      color: theme.palette.text.link,
    },
    '&.MuiToggleButton-root': {
      backgroundColor: theme.palette.text.white,
      '&:hover': {
        backgroundColor: theme.palette.text.white,
      },
    },
  },
  checkIcon: {
    marginLeft: theme.spacing(1),
    color: theme.palette.text.link,
  },
  buttonContainer: {
    direction: 'ltr',
    width: '100%',
  },
  loading: {
    display: 'flex',
    alignItems: 'center',
    marginTop: theme.spacing(2),
    '& img': {
      width: 40,
      [theme.breakpoints.down('sm')]: {
        width: 25,
      },
    },
  },
  spacing: {
    marginTop: theme.spacing(1),
  },
  typing: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  action: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing(2),
  },
}));

type LetterAiDialogProps = {
  onClose: () => void;
  open: boolean;
  selectedTabCreateLetterByTopic: string;
  handleToggleChangeTabGenai: (event: React.MouseEvent<HTMLElement>, value: string) => void;
  setTextFieldGenai: (value: string) => void;
  onClick: () => void;
  genaiLoading: boolean;
  textFieldGenai: string;
};

const LetterAiDialog: FC<LetterAiDialogProps> = (props) => {
  const {
    open,
    onClose,
    onClick,
    selectedTabCreateLetterByTopic,
    handleToggleChangeTabGenai,
    setTextFieldGenai,
    genaiLoading,
    textFieldGenai,
  } = props;
  const { classes } = useStyles();
  const { t } = useTranslation();

  return (
    <AWDialog
      open={open}
      onClose={onClose}
      fullScreen
      showTopBorderInActions
      dialogActionChildren={
        <AWButton
          variant="contained"
          onClick={onClick}
          disabled={genaiLoading || !textFieldGenai.length}
        >
          {t('genai.labels.create-letter')}
        </AWButton>
      }
    >
      <AWBox className={classes.action}>
        <AWBox>
          <AWIconButton onClick={onClose}>
            <CloseIcon />
          </AWIconButton>
        </AWBox>
        <AWBox className={classes.header}>
          <AWTypography variant="button" className={classes.typography}>
            {t('genai.labels.help-genai')}
          </AWTypography>
          <img src={dubi} alt="dubi" />
        </AWBox>
        <ToggleButtonGroup
          value={selectedTabCreateLetterByTopic}
          onChange={handleToggleChangeTabGenai}
          exclusive
          className={classes.toggleButtonGroup}
          disabled={genaiLoading}
        >
          <AWBox className={classes.toggleButtonContainer}>
            <ToggleButton className={classes.toggleButton} value="official">
              {selectedTabCreateLetterByTopic === 'official' && (
                <CheckIcon className={classes.checkIcon} />
              )}
              {t('genai.labels.official')}
            </ToggleButton>
            <ToggleButton className={classes.toggleButton} value="friendly">
              {selectedTabCreateLetterByTopic === 'friendly' && (
                <CheckIcon className={classes.checkIcon} />
              )}
              {t('genai.labels.friendly')}
            </ToggleButton>
          </AWBox>
        </ToggleButtonGroup>
        <AWTextField
          fullWidth
          multiline
          value={textFieldGenai}
          disabled={genaiLoading}
          onChange={(e) => setTextFieldGenai(e.target.value)}
          placeholder={t('genai.labels.content')}
          className={`${classes.textfield} ${classes.textfieldContent}`}
        />
        {genaiLoading && (
          <AWBox className={classes.typing}>
            <AWBox className={classes.loading}>
              {t('genai.labels.create-letter-loading')}
              <Loading inline className={classes.spacing} />
            </AWBox>
            <img src={dubi} alt="dubi" />
          </AWBox>
        )}
      </AWBox>
    </AWDialog>
  );
};

export default LetterAiDialog;
