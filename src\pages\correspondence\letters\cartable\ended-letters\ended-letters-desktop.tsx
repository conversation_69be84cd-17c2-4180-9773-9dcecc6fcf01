import React from 'react';

import { makeStyles } from 'tss-react/mui';
import Grid from '@mui/material/Grid';

import SearchLetter from '../../shared/searchLetter';
import CorrespondenceEndedLettersTable from './components/ended-letters-table/correspondence-ended-letters-table';
import AWBox from '../../../../../components/AWComponents/AWBox';

const useStyles = makeStyles()((theme) => ({
  gridRoot: {
    display: 'table',
    height: '100%',
  },
  searchBox: {
    padding: theme.spacing(0, 2),
  },
}));

const DesktopEndedLetters = () => {
  const { classes } = useStyles();

  return (
    <Grid container spacing={2} classes={{ root: classes.gridRoot }}>
      <Grid item xs={12}>
        <AWBox classes={{ root: classes.searchBox }}>
          <SearchLetter />
        </AWBox>
      </Grid>
      <Grid item xs={12}>
        <AWBox>
          <CorrespondenceEndedLettersTable />
        </AWBox>
      </Grid>
    </Grid>
  );
};

export default DesktopEndedLetters;
