import { GetProjectMembersDto } from 'com.chargoon.cloud.contracts.management-center/dist/organizations/projects/dtos/get-project-members.dto';
import { GetProjectDto } from 'com.chargoon.cloud.contracts.management-center/dist/organizations/projects/dtos/get-project.dto';

import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import Services3 from 'api/v1/services/index';

const apiVersion = '/api/v1/';

const initialState = {
  createProject: {
    data: null,
    status: 'idle',
    success: false,
  },
  updateMembers: {
    data: null,
    status: 'idle',
    success: false,
  },
  positionProjects: {
    data: null,
    status: 'idle',
    success: false,
  },
  projectMembers: {
    data: null,
    status: 'idle',
    success: false,
  },
  projectsList: {
    data: null,
    meta: null,
    status: 'idle',
    success: false,
  },
  project: {
    data: null,
    status: 'idle',
    success: false,
  },
  updateProject: {
    data: null,
    status: 'idle',
    success: false,
  },
  updateProjectMembers: {
    data: null,
    status: 'idle',
    success: false,
  },
  updateTaskProjects: {
    data: null,
    status: 'idle',
    success: false,
  },
  allProjects: {
    data: null,
    meta: null,
    status: 'idle',
    success: false,
  },
};

export type CreateProjectDto = {
  id: string;
  title: string;
  description: string;
};

export const createProjectAsync = createAsyncThunk(
  'projects/createProject',
  async (options: CreateProjectDto) => {
    const opts = {
      apiVersion,
      domainRoot: 'projects',
      method: 'createProject',
    };
    const response = await Services3({ options: opts, data: options });
    return response;
  },
);

export type UpdateMembersDto = {
  id: string;
  members: Array<{
    user: { id: string } | null;
    position: { id: string; slot: number[] };
    action: 'add' | 'remove';
  }>;
  requester: {
    user: { id: string };
    position: { id: string; slot: number[] };
  };
};

export type ProjectsListDto = {
  limit: number;
  offset: number;
  organizationId: string;
  positionSlotId: string;
};

export const updateMembersAsync = createAsyncThunk(
  'projects/updateMembers',
  async (args: UpdateMembersDto) => {
    const { id } = args;

    const options = {
      apiVersion,
      domainRoot: 'projects',
      method: 'updateMembers',
      path: `${id}/members`,
    };
    const response = await Services3({ options, data: args });
    return response;
  },
);

export const projectsListAsync = createAsyncThunk(
  'projects/projectsList',
  async (args: ProjectsListDto) => {
    const { organizationId, positionSlotId, ...queries } = args;
    const options = {
      apiVersion,
      domainRoot: 'projects',
      method: 'projectsList',
      path: `/organization/${organizationId}/positionSlotId/${positionSlotId}`,
      queryObject: {
        ...queries,
      },
    };
    const response = await Services3({ options });
    return response;
  },
);

// TODO: added path and change code about argument function
export const getProjectAsync = createAsyncThunk(
  'projects/getProject',
  async (args: GetProjectDto) => {
    const { id } = args;
    const options = {
      apiVersion,
      domainRoot: 'projects',
      method: 'getProject',
      path: `${id}`,
    };
    const response = await Services3({ options });
    return response;
  },
);
export const getAllProjectsAsync = createAsyncThunk(
  'projects/allProjects',
  async (args: ProjectsListDto) => {
    const { organizationId, positionSlotId, ...queries } = args;
    const options = {
      apiVersion,
      domainRoot: 'projects',
      method: 'projectsList',
      path: `/organization/${organizationId}/positionSlotId/${positionSlotId}`,
      queryObject: {
        ...queries,
      },
    };
    const response = await Services3({ options });
    return response;
  },
);

export const getProjectMembersAsync = createAsyncThunk(
  'projects/getProjectMembers',
  async (args: GetProjectMembersDto) => {
    const { projectId } = args;
    const options = {
      apiVersion,
      domainRoot: 'projects',
      method: 'getProjectMembers',
      path: `${projectId}/members`,
    };
    const response = await Services3({ options });
    return response;
  },
);

export const updateProjectAsync = createAsyncThunk('projects/updateProject', async (args: any) => {
  const { data } = args;
  const options = {
    apiVersion,
    domainRoot: 'projects',
    method: 'updateProject',
    path: `${data.id}`,
  };
  const response = await Services3({ options, data });
  return response;
});

export const updateTaskProjectsAsync = createAsyncThunk('projects/updateTaskProjects', async () => {
  const options = {
    apiVersion,
    domainRoot: 'projects',
    method: 'updateTaskProjects',
    // path: ``,
  };
  const response = await Services3({ options });
  return response;
});

export const projectsSlice = createSlice({
  name: 'projects',
  initialState,
  reducers: {
    createProjectReset: (state) => {
      const st = state;
      st.createProject = initialState.createProject;
    },
    updateMembersReset: (state) => {
      const st = state;
      st.updateMembers = initialState.updateMembers;
    },
    projectsListsReset: (state) => {
      const st = state;
      st.projectsList = initialState.projectsList;
    },
    getProjectReset: (state) => {
      const st = state;
      st.project = initialState.project;
    },
    getProjectMembersReset: (state) => {
      const st = state;
      st.projectMembers = initialState.projectMembers;
    },
    allProjectsReset: (state) => {
      const st = state;
      st.allProjects = initialState.allProjects;
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(createProjectAsync.fulfilled, (state, action) => {
        const st = state;
        st.createProject.status = 'idle';
        st.createProject.data = action.payload.data;
        st.createProject.success = action.payload.success;
      })
      .addCase(createProjectAsync.pending, (state) => {
        const st = state;
        st.createProject.status = 'pending';
        st.createProject.data = null;
      })

      .addCase(updateMembersAsync.fulfilled, (state, action) => {
        const st = state;
        st.updateMembers.status = 'idle';
        st.updateMembers.data = action.payload.data;
        st.updateMembers.success = action.payload.success;
      })
      .addCase(updateMembersAsync.pending, (state) => {
        const st = state;
        st.updateMembers.status = 'pending';
        st.updateMembers.data = null;
        st.updateMembers.success = false;
      })
      .addCase(getProjectMembersAsync.pending, (state) => {
        const st = state;
        st.projectMembers.status = 'pending';
        st.projectMembers.data = null;
      })
      .addCase(getProjectMembersAsync.fulfilled, (state, action) => {
        const st = state;
        st.projectMembers.status = action.payload.success ? 'idle' : 'failed';
        st.projectMembers.data = action.payload.success
          ? action.payload.data
          : initialState.projectMembers.data;
        st.projectMembers.success = action.payload.success;
      })
      .addCase(getProjectMembersAsync.rejected, (state) => {
        const st = state;
        st.projectMembers.status = 'failed';
        st.projectMembers.data = null;
      })
      .addCase(projectsListAsync.pending, (state) => {
        const st = state;
        st.projectsList.status = 'pending';
      })
      .addCase(projectsListAsync.fulfilled, (state, action) => {
        const st = state;
        st.projectsList.status = 'idle';
        st.projectsList.data = action.payload.data;
        st.projectsList.meta = action.payload.meta as any;
        st.projectsList.success = action.payload.success;
      })
      .addCase(projectsListAsync.rejected, (state) => {
        const st = state;
        st.projectsList.status = 'failed';
        st.projectsList.data = null;
      })
      .addCase(getProjectAsync.pending, (state) => {
        const st = state;
        st.project.status = 'pending';
      })
      .addCase(getProjectAsync.fulfilled, (state, action) => {
        const st = state;
        st.project.status = 'idle';
        st.project.data = !action.payload.success ? null : action.payload.data;
        st.project.success = action.payload.success;
      })
      .addCase(updateProjectAsync.pending, (state) => {
        const st = state;
        st.updateProject.status = 'pending';
      })
      .addCase(updateProjectAsync.fulfilled, (state, action) => {
        const st = state;
        st.updateProject.status = 'idle';
        st.updateProject.data = action.payload.data;
        st.updateProject.success = action.payload.success;
      })
      .addCase(updateTaskProjectsAsync.pending, (state) => {
        const st = state;
        st.updateTaskProjects.status = 'pending';
      })
      .addCase(updateTaskProjectsAsync.fulfilled, (state, action) => {
        const st = state;
        st.updateProjectMembers.status = 'idle';
        st.updateProjectMembers.data = action.payload.data;
        st.updateProjectMembers.success = action.payload.success;
      })
      .addCase(getAllProjectsAsync.pending, (state) => {
        const st = state;
        st.allProjects.status = 'pending';
      })
      .addCase(getAllProjectsAsync.fulfilled, (state, action) => {
        const st = state;
        st.allProjects.status = 'idle';
        st.allProjects.data = action.payload.data;
        st.allProjects.meta = action.payload.meta as any;
        st.allProjects.success = action.payload.success;
      })
      .addCase(getAllProjectsAsync.rejected, (state) => {
        const st = state;
        st.allProjects.status = 'failed';
        st.allProjects.data = null;
      }),
});

export default projectsSlice.reducer;

export const {
  createProjectReset,
  updateMembersReset,
  projectsListsReset,
  getProjectReset,
  getProjectMembersReset,
  allProjectsReset,
} = projectsSlice.actions;
