import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act } from 'react-dom/test-utils';
import '../../../i18n';
import authReducer, { registerUserAsync } from '../../../pages/Auth/authSlice';
import { customRender } from '../../../mocks/render';
import SignUp from '../../../pages/Auth/Register/SignUp';
import { RegisterContext } from '../../../contexts/pageContext/auth/registerContext';

const testSignUpBehavior = (size) => {
  it('should render Login and redirect to /organizations on successful login', async () => {
    const { container, history, store } = customRender(
      <RegisterContext.Provider
        value={{
          getValidationErrorEx: jest.fn(),
          isInvalid: jest.fn(),
          getValidationError: jest.fn(),
          state: {},
        }}
      >
        <SignUp />
      </RegisterContext.Provider>,
      {
        reducerKey: 'auth',
        reducerValue: authReducer,
        size,
      },
      { initialEntries: ['/register?step=1'] },
    );

    const submitButton = container.querySelector('button[type="submit"]');
    const mobileInput = container.querySelector('input[name="mobile"]');
    const usernameInput = container.querySelector('input[name="username"]');
    const passwordInput = container.querySelector('input[name="password"]');
    const termsInput = container.querySelector('input[name="checkPermission"]');

    await act(async () => userEvent.type(mobileInput, '09101010101'));
    await act(async () => userEvent.type(usernameInput, 'maryam'));
    await act(async () => userEvent.type(passwordInput, 'Qwer@1234'));
    await act(async () => userEvent.click(termsInput));

    act(() => {
      fireEvent.click(submitButton);

      // Dispatch the action with the expected data
      store.dispatch(
        registerUserAsync.fulfilled({
          username: usernameInput.value,
          password: passwordInput.value,
          mobile: mobileInput.value,
        }),
      );
    });

    console.log('foo', store.getState().auth);

    // Wait for the action to complete and the redirection to occur
    await act(async () =>
      waitFor(() => {
        expect(history.location.pathname).toBe('/register?step=1');
      }),
    );
  });
};

describe('Login Component', () => {
  describe('Login Mobile Component', () => {
    testSignUpBehavior('sm');
  });

  describe('Login Desktop Component', () => {
    testSignUpBehavior('lg');
  });
});
