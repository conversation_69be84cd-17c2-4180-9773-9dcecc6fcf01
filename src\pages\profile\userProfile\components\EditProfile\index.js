import { useUserProfileContext } from 'contexts/pageContext/profile/userProfileContext';
import EditProfileInfoValidation from './validation';

const ProfileInfo = () => {
  const {
    showLoading,
    readOnlyState,
    submitEditForm,
    resetEditForm,
    onChange,
    onChangeAutocomplete,
    state,
    file,
    setFile,
    isImageValid,
    imageProfileProps,
    deleteFile,
    setState,
    initState,
    tabLanguage,
    handleChangeTabLanguage,
  } = useUserProfileContext();

  return (
    <EditProfileInfoValidation
      {...{
        showLoading,
        readOnlyState,
        state,
        onChange,
        onChangeAutocomplete,
        onEditForm: submitEditForm,
        onResetEditForm: resetEditForm,
        file,
        setFile,
        isImageValid,
        imageProfileProps,
        deleteFile,
        setState,
        initState,
        tabLanguage,
        handleChangeTabLanguage,
      }}
    />
  );
};

export default ProfileInfo;
