import ErrorOutlineOutlinedIcon from '@mui/icons-material/ErrorOutlineOutlined';

import AWBox from 'components/AWComponents/AWBox';
import AWButton from 'components/AWComponents/AWButton';
import AWDialog from 'components/AWComponents/AWDialog';
import AWTextField from 'components/AWComponents/AWTextField';
import AWTypography from 'components/AWComponents/AWTypography';
import Loading from 'components/Loading';
import { useLetter } from 'contexts/pageContext/letters/letterContext';
import { useTranslation } from 'react-i18next';
import { makeStyles } from 'tss-react/mui';

const useStyles = makeStyles()((theme) => ({
  textParent: {
    display: 'flex',
    padding: theme.spacing(1, 0),
  },
  text: {
    fontSize: '14px',
    paddingRight: theme.spacing(1),
  },
  button: {
    marginLeft: theme.spacing(1),
  },
}));

const DialogParaphCreator = () => {
  const {
    handleCreateForwardNotesSample,
    isSubmittingForwardNoteSample,
    openPreCreateParaph,
    toggleDialogCreateParaph,
    paraph,
  } = useLetter();

  const { t } = useTranslation();
  const { classes } = useStyles();
  const onClose = () => toggleDialogCreateParaph(false);

  return (
    <AWDialog
      open={openPreCreateParaph}
      isTitleShow
      title={t('correspondence.letters.labels.create-forward-letter-sample')}
      fullWidth
      maxWidth="sm"
      onClose={onClose}
      dialogActionClasses={classes.button}
      dialogActionChildren={
        <AWButton
          onClick={handleCreateForwardNotesSample}
          variant="contained"
          disabled={isSubmittingForwardNoteSample}
        >
          {isSubmittingForwardNoteSample ? <Loading color="white" /> : t('common.labels.save')}
        </AWButton>
      }
    >
      <AWBox>
        <AWTextField
          multiline
          rows={4}
          fullWidth
          value={paraph}
          name="paraph"
          id="paraph"
          label={t('correspondence.letters.labels.create-new-forward-letter')}
          InputProps={{
            readOnly: true,
          }}
        />
        <AWBox className={classes.textParent}>
          <ErrorOutlineOutlinedIcon color="warning" fontSize="small" />
          <AWTypography color="warning" className={classes.text}>
            {t('correspondence.letters.void-letter.messages.create-forward-letter-sample-warning')}
          </AWTypography>
        </AWBox>
      </AWBox>
    </AWDialog>
  );
};

export default DialogParaphCreator;
