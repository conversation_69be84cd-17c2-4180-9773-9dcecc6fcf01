import React from 'react';
import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import SocialMedia from '../../../../components/SocialMedia';
import logo from '../../../../assets/images/logo/Logo.svg';
import { useMainLayoutStyles } from '../../../../contexts/mainLayoutStylesContext';
import Loading from '../../../../components/Loading';
import { useRegisterContext } from '../../../../contexts/pageContext/auth/registerContext';

const useStyles = makeStyles()((theme) => ({
  root: {
    width: '100%',
    height: '100%',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    minHeight: 650,
  },
  mobileRoot: {
    position: 'relative',
    textAlign: 'center',
    flexGrow: 1,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  mobileMiddlePaper: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '24px 19px 44.2px 19px',
    margin: 'auto',
    marginTop: 0,
  },
  logo: {
    display: 'flex',
    margin: theme.spacing(2, 0),
  },
  form: {
    height: '100%',
    width: '100%',
    position: 'relative',
  },
  mobilePositionDown: {
    position: 'absolute',
    bottom: 10,
    width: '100%',
  },
  successIcon: {
    color: '#00c89c',
    fontSize: '5rem',
  },
  infoBox: {
    backgroundImage:
      'linear-gradient(to right, #d1d1d1 60%, transparent 50%), linear-gradient(to right, #d1d1d1 60%, transparent 60%), linear-gradient(to bottom, #d1d1d1 60%, transparent 60%), linear-gradient(to bottom, #d1d1d1 60%, transparent 60%)',
    backgroundPosition: 'left top, left bottom, left top, right top',
    backgroundRepeat: ' repeat-x, repeat-x, repeat-y, repeat-y',
    backgroundSize: '20px 1px, 20px 1px, 1px 20px, 1px 20px',
    display: 'flex',
    padding: 10,
    justifyContent: 'space-around',
    flexDirection: 'column',
    listStyle: 'none',
    width: '85%',
    borderRadius: 10,
    margin: '24px auto',
    '& li': {
      display: 'flex',
      justifyContent: 'space-between',
      fontSize: '0.875rem',
      padding: '8px',
      lineHeight: 2,
      fontWeight: 500,
      color: theme.palette.text.secondary,
    },
  },
  infoText: {
    color: theme.palette.text.gray,
  },
  ltr: {
    direction: 'ltr',
  },
  marginTop4: {
    marginTop: theme.spacing(4),
  },
}));

const BaseSignUpSuccess = ({ isMobile }) => {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes: globalClasses } = makeStyles()(stylesGenerator)();
  const { auth, submitLogin } = useRegisterContext();

  return (
    <Container
      component="div"
      maxWidth="xs"
      className={isMobile ? classes.mobileRoot : classes.root}
    >
      <div className={isMobile ? classes.mobileMiddlePaper : globalClasses.middlePaper}>
        <img src={logo} alt="Logo" className={classes.logo} />
        <form className={classes.form}>
          <CheckCircleOutlineIcon className={classes.successIcon} />
          <Typography className={globalClasses.bold} variant="body1">
            {t('auth.messages.successRegister')}
          </Typography>
          <ul className={classes.infoBox}>
            <li>
              <Typography>{t('common.labels.username')}</Typography>
              <Typography>{auth.userInfo?.username}</Typography>
            </li>
            <li>
              <Typography>{t('common.labels.mobile')}</Typography>
              <Typography className={classes.ltr}>{auth.userInfo?.mobile}</Typography>
            </li>
          </ul>
          <Typography variant="subtitle2" className={globalClasses.infoText}>
            {t('auth.strings.canUseInfo')}
          </Typography>
          <Button
            variant="contained"
            type="button"
            onClick={submitLogin}
            fullWidth
            className={classes.marginTop4}
            disabled={auth.loginByPassword.status === 'loading'}
          >
            {auth.loginByPassword.status === 'loading' ? (
              <Loading color="white" />
            ) : (
              t('common.labels.enter')
            )}
          </Button>
        </form>
      </div>
      <SocialMedia />
    </Container>
  );
};
BaseSignUpSuccess.propTypes = {
  isMobile: PropTypes.bool,
};

BaseSignUpSuccess.defaultProps = {
  isMobile: false,
};

export default BaseSignUpSuccess;
