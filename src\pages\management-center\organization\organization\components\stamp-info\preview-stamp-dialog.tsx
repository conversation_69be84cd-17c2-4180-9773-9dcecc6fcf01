import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';

import AWDialog from 'components/AWComponents/AWDialog';
import { useTranslation } from 'react-i18next';
import { makeStyles } from 'tss-react/mui';
import AWBox from 'components/AWComponents/AWBox';
import AWTypography from '../../../../../../components/AWComponents/AWTypography';
import AWButton from '../../../../../../components/AWComponents/AWButton';

const useStyles = makeStyles()((theme) => ({
  image: {
    width: '100%',
    height: 'auto',
    borderRadius: theme.spacing(1),
  },
  buttonBox: {
    display: 'flex',
    gap: theme.spacing(2),
  },
}));

type PreviewStampDialogProps = {
  handleUploadClick: () => void;
  handleCloseDialog: () => void;
  handleConfirmStamp: () => void;
  openPreviewDialog: boolean;
  previewImage: string | null;
};

const PreviewStampDialog = ({
  previewImage,
  openPreviewDialog,
  handleCloseDialog,
  handleUploadClick,
  handleConfirmStamp,
}: PreviewStampDialogProps) => {
  const { t } = useTranslation();
  const { classes } = useStyles();

  return (
    <AWDialog
      open={openPreviewDialog}
      onClose={handleCloseDialog}
      isTitleShow
      title={t('organization.labels.stamp-preview')}
      children={
        <>
          {previewImage ? (
            <img src={previewImage} className={classes.image} />
          ) : (
            <AWTypography> {t('organization.messages.no-image-data')}</AWTypography>
          )}
        </>
      }
      dialogActionChildren={
        <AWBox className={classes.buttonBox}>
          <AWButton onClick={handleUploadClick} variant="outlined">
            {t('common.labels.choose-new-file')}
          </AWButton>
          <AWButton onClick={handleConfirmStamp} variant="contained" name="stamps">
            {t('common.labels.upload')}
          </AWButton>
        </AWBox>
      }
    />
  );
};

export default PreviewStampDialog;
