import { createContext, useContext } from 'react';

import { IAWTableProps, IWithColumnOptions, IWithPagination } from '../interfaces';
import { SelectChangeEvent } from '@mui/material';

const defaultValue: IAWTableProps & IWithPagination & IWithColumnOptions = {
  limit: 10,
  offset: 0,
  total: 10,
  loading: false,
  loadingType: 'dot-gif',
  rows: [],
  noDataMessage: '',
  onRowClick: () => {},
  handleChangePage: () => {},
  handleNextPage: () => {},
  handlePrevPage: () => {},
  classes: {},
  columns: [],
  columnsRenderer: {},
  columnsVisibility: 'standard',
  actions: {},
  paginationPosition: 'footer',
  pagination: {
    pageCount: 1,
    onChange: () => {},
    currentPage: 1,
    from: 1,
    to: 1,
    goToPageValue: 1,
    pageLimit: 10,
    onChangeGoToPage: (e: React.ChangeEvent<HTMLInputElement>) => {},
    onChangePageLimit: (e: SelectChangeEvent) => {},
    submitGoToPage: () => {},
    pageLimitOptions: [],
    showLimit: false,
  },
  draggableItems: [],
  openColumnOptionsDialog: false,
  setOpenColumnOptionsDialog: (value: boolean) => {},
  onCloseColumnOptionsDialog: () => {},
  saveColumnOptions: () => {},
  onDragOptionEnd: () => {},
  onChangeDraggableItemCheckbox: (event: React.ChangeEvent<HTMLInputElement>) => {},
  highlights: {},
};

export const AWTableContext = createContext(defaultValue);

export const useAWTableContext = () => useContext(AWTableContext);
