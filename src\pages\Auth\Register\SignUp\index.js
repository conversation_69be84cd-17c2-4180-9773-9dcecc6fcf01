import React from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import DesktopSignUp from './sign-up-desktop';
import TabletSignUp from './sign-up-tablet';
import MobileSignUp from './sign-up-mobile';

const SignUp = () => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));

  if (mobile) {
    return <MobileSignUp isMobile />;
  }
  if (tablet) {
    return <TabletSignUp />;
  }
  if (desktop) {
    return <DesktopSignUp />;
  }
  return <></>;
};

export default SignUp;
