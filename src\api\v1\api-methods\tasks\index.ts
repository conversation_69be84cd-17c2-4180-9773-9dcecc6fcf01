import { ApiMethodDictionary } from 'api/v1/types/api-methods.type';

const methods: ApiMethodDictionary = {
  getTask: {
    httpMethod: 'GET',
  },
  createTask: {
    httpMethod: 'POST',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  updateTask: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  changeTaskStatus: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  allTasksInRange: {
    httpMethod: 'GET',
  },
  getUndoneTasksList: {
    httpMethod: 'GET',
  },
  getDoneTasksList: {
    httpMethod: 'GET',
  },
  deleteTask: {
    httpMethod: 'DELETE',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  assignees: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  projects: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  getUndoneAssignedTasksList: {
    httpMethod: 'GET',
  },
  getDoneAssignedTasksList: {
    httpMethod: 'GET',
  },
  allTasksAssignedInRange: {
    httpMethod: 'GET',
  },
  taskHistory: {
    httpMethod: 'GET',
  },
  getProjectTasks: {
    httpMethod: 'GET',
  },
  updateTaskProjects: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
};

export default methods;
