import React from 'react';

import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import { makeStyles } from 'tss-react/mui';
import Button from '@mui/material/Button';
import { useLocation } from 'react-router';

import { useMainLayoutStyles } from '../../../contexts/mainLayoutStylesContext';
import AppBarMobile from '../app-bar-mobile';
import AppBarDesktop from '../app-bar-desktop';
import snackbarUtils from '../../../utils/snackbarUtils';

const useStyles = makeStyles()((theme) => ({
  content: {
    display: 'flex',
  },
  root: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    textAlign: 'center',
  },
  title: {
    fontSize: '24px',
    fontWeight: 'bold',
    [theme.breakpoints.down('md')]: {
      fontSize: '16px',
    },
  },
  img: {
    padding: theme.spacing(1),
    margin: theme.spacing(5, 0),
    width: '328px',
    height: '328px',
    background: 'white',
    borderRadius: '50%',
    boxShadow: 'inset 1px 2px 1px 3px lightgray',
    [theme.breakpoints.down('md')]: {
      width: '260px',
      height: '260px',
    },
  },
  callSupport: {
    fontSize: '16px',
    fontWeight: 'bold',
    marginBottom: theme.spacing(2),
    [theme.breakpoints.down('md')]: {
      fontSize: '14px',
    },
  },
  awatSupport: {
    marginBottom: theme.spacing(1),
    display: 'flex',
    '& h5': {
      fontSize: '16px',
      fontWeight: 'normal',
      lineHeight: '1.5',
      [theme.breakpoints.down('md')]: {
        fontSize: '14px',
      },
    },
    alignItems: 'center',
    justifyContent: 'center',
    '& img': {
      width: '26px',
      height: '26px',
      borderRadius: '50%',
      marginLeft: theme.spacing(1),
    },
  },
  copyErrorButton: {
    color: theme.palette.text.link,
    fontWeight: 'bold',
    fontSize: '14px',
  },
}));

const ErrorBoundaryChildren = (props) => {
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes: globalClasses } = makeStyles()(stylesGenerator)();
  const { classes, cx } = useStyles();
  const { showAppBar, mobile, error, errorInfo, ...errorBoundaryProps } = props;
  const { isReadOnlyPositionSelection, showPositionSelection } = errorBoundaryProps;
  const location = useLocation();

  function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      const msg = successful ? 'successful' : 'unsuccessful';
      snackbarUtils.success('متن خطا با موفقیت کپی شد');
    } catch (err) {
      console.error('Fallback: Oops, unable to copy', err);
    }

    document.body.removeChild(textArea);
  }
  function copyTextToClipboard(text) {
    if (!navigator.clipboard) {
      fallbackCopyTextToClipboard(text);
      return;
    }
    navigator.clipboard.writeText(text).then(
      () => {
        snackbarUtils.success('متن خطا با موفقیت کپی شد');
      },
      (err) => {
        console.error('Async: Could not copy text: ', err);
      },
    );
  }

  const handleCopyErrorOnClipboard = () => {
    copyTextToClipboard(
      JSON.stringify({
        error,
        errorInfo,
        path: location.pathname,
      }),
    );
  };

  return (
    <Grid container dir={process.env.REACT_APP_SETTINGS_DIRECTION} id="dir">
      <Grid item>
        {showAppBar &&
          (mobile ? (
            <AppBarMobile {...{ isReadOnlyPositionSelection, showPositionSelection }} />
          ) : (
            <AppBarDesktop {...errorBoundaryProps} />
          ))}
      </Grid>
      <Box className={cx(globalClasses.content, classes.content)}>
        <Grid container className={classes.root} columns={12}>
          <Grid item xs={12}>
            <Typography variant="h3" className={classes.title}>
              متاسفیم! به نظر میرسه یه مشکلی به وجود اومده
            </Typography>
          </Grid>
          <Grid item xs={12}>
            <img src="/error-boundary.svg" alt="error-boundary" className={classes.img} />
          </Grid>
          <Grid item xs={12}>
            <Typography variant="h5" className={classes.callSupport}>
              لطفا با پشتیبانی آوات تماس بگیرید تا مشکل را برطرف کنیم.
            </Typography>
            <Box className={classes.awatSupport}>
              {!mobile && <img src="/awat-support.png" alt="awat-support" />}
              <Typography variant="h5">
                یک راه سریع برای تماس با پشتیبانی آوات، <b>سامانه گفتگوی آنلاین</b> است
              </Typography>
            </Box>
            <Button
              variant="text"
              onClick={handleCopyErrorOnClipboard}
              className={classes.copyErrorButton}
              id="copy-error-button"
            >
              کپی کردن جزئیات خطا
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Grid>
  );
};

export default ErrorBoundaryChildren;
