import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import Services3 from 'api/v1/services/index';
import { momentUTC } from '../../utils';

const apiVersion = '/api/v1/';

const initialState = {
  recoverPasswordByMobile: {
    status: 'idle',
    data: null,
    success: null,
  },
  loginByPassword: {
    status: 'idle',
    data: null,
    success: null,
  },
  otp: {
    status: 'idle',
    data: null,
  },
  registerUser: {
    status: 'idle',
    data: null,
  },
  username: {
    status: 'idle',
    data: null,
  },
  userInfo: {},
  lastLoginData: {
    data: null,
    status: 'idle',
  },
  userExist: {
    data: null,
    status: 'idle',
  },
};

export const getLastLoginDataAsync = createAsyncThunk('auth/lastLoginData', async (id) => {
  const opts = {
    apiVersion,
    domainRoot: 'users',
    method: 'lastLoginData',
    path: `${id}/last-login`,
  };
  const response = await Services3({ options: opts });
  return response;
});

export const recoverPasswordByMobileAsync = createAsyncThunk(
  'auth/recoverPasswordByMobile',
  async (options) => {
    const opts = { apiVersion, domainRoot: 'users', method: 'recoverPasswordByMobile' };
    const response = await Services3({
      options: opts,
      data: options.data,
      headers: options.headers,
    });
    return response;
  },
);

export const loginByPasswordAsync = createAsyncThunk('auth/loginByPassword', async (options) => {
  const opts = { apiVersion, domainRoot: 'auth', method: 'login' };
  const response = await Services3({ options: opts, data: options.data });
  return response;
});

export const otpAsync = createAsyncThunk('auth/otp', async (options) => {
  const opts = { apiVersion, domainRoot: 'auth', method: 'otp' };
  const response = await Services3({ options: opts, data: options.data });
  return response;
});

export const registerUserAsync = createAsyncThunk('auth/registerUser', async (options) => {
  const opts = { apiVersion, domainRoot: 'users' };
  const response = await Services3({
    options: opts,
    data: options.data,
    headers: options.headers,
  });
  return response;
});

export const usernameAsync = createAsyncThunk('auth/username', async (options) => {
  const opts = {
    apiVersion,
    domainRoot: 'users',
    method: 'username',
    path: `${options.path}`,
  };
  const response = await Services3({ options: opts, data: options.data });
  return response;
});

export const userExistAsync = createAsyncThunk('auth/userExist', async (options) => {
  const { username } = options;
  const opts = {
    apiVersion,
    domainRoot: 'users',
    method: 'userExist',
    path: username,
  };
  const response = await Services3({ options: opts });
  return response;
});

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    otpReset: (state) => {
      const st = state;
      st.otp = initialState.otp;
    },
    registerUserAsyncReset: (state) => {
      const st = state;
      st.registerUser = initialState.registerUser;
    },
    loginByPasswordReset: (state) => {
      const st = state;
      st.loginByPassword = initialState.loginByPassword;
    },
    recoverPasswordByMobileReset: (state) => {
      const st = state;
      st.recoverPasswordByMobile = initialState.recoverPasswordByMobile;
    },
    userInfoSet: (state, action) => {
      const st = state;
      st.userInfo = action.payload;
    },
    userExistAsyncReset: (state) => {
      const st = state;
      st.userExist = initialState.userExist;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(recoverPasswordByMobileAsync.pending, (state) => {
        const st = state;
        st.recoverPasswordByMobile.status = 'loading';
        st.recoverPasswordByMobile.success = null;
      })
      .addCase(recoverPasswordByMobileAsync.fulfilled, (state, action) => {
        const st = state;
        st.recoverPasswordByMobile.status = 'idle';
        if (action.payload.success) {
          st.recoverPasswordByMobile.data = action.payload.data;
          st.recoverPasswordByMobile.success = action.payload?.success ?? false;
        } else {
          st.recoverPasswordByMobile.success = false;
        }
      })
      .addCase(recoverPasswordByMobileAsync.rejected, (state) => {
        const st = state;
        st.recoverPasswordByMobile.status = 'idle';
        st.recoverPasswordByMobile.data = {};
        st.recoverPasswordByMobile.success = false;
      })
      .addCase(loginByPasswordAsync.pending, (state) => {
        const st = state;
        st.loginByPassword.status = 'loading';
      })
      .addCase(loginByPasswordAsync.fulfilled, (state, action) => {
        const st = state;
        st.loginByPassword.status = 'idle';
        st.loginByPassword.data = action.payload.data;
        st.loginByPassword.success = action.payload.success;
      })
      .addCase(loginByPasswordAsync.rejected, (state) => {
        const st = state;
        st.loginByPassword.status = 'idle';
        st.loginByPassword.data = {};
      })
      .addCase(otpAsync.pending, (state) => {
        const st = state;
        st.otp.status = 'loading';
      })
      .addCase(otpAsync.fulfilled, (state, action) => {
        const st = state;
        st.otp.status = 'idle';
        st.otp.data = action.payload.data;
      })
      .addCase(otpAsync.rejected, (state, action) => {
        const st = state;
        st.otp.status = 'idle';
        st.otp.data = { success: false };
      })
      .addCase(registerUserAsync.pending, (state) => {
        const st = state;
        st.registerUser.status = 'loading';
      })
      .addCase(registerUserAsync.fulfilled, (state, action) => {
        const st = state;
        st.registerUser.status = 'idle';
        st.registerUser.data = action.payload;
      })
      .addCase(registerUserAsync.rejected, (state, action) => {
        const st = state;
        st.registerUser.status = 'idle';
        st.registerUser.data = { success: false };
      })
      .addCase(usernameAsync.pending, (state) => {
        const st = state;
        st.username.status = 'loading';
      })
      .addCase(usernameAsync.fulfilled, (state, action) => {
        const st = state;
        st.username.status = 'idle';
        st.username.data = action.payload.data;
      })
      .addCase(getLastLoginDataAsync.pending, (state) => {
        const st = state;
        st.lastLoginData.status = 'loading';
      })
      .addCase(getLastLoginDataAsync.fulfilled, (state, action) => {
        const st = state;
        st.lastLoginData.status = 'idle';
        if (typeof action?.payload?.data === 'string') {
          st.lastLoginData.data = momentUTC(new Date(action.payload.data));
        } else {
          st.lastLoginData.data = undefined;
        }
      })
      .addCase(userExistAsync.pending, (state) => {
        const st = state;
        st.userExist.status = 'loading';
      })
      .addCase(userExistAsync.fulfilled, (state, action) => {
        const st = state;
        st.userExist.status = 'idle';
        st.userExist.data = action.payload.data;
      })
      .addCase(userExistAsync.rejected, (state) => {
        const st = state;
        st.userExist.status = 'idle';
        st.userExist.data = { success: false };
      });
  },
});

export const {
  otpReset,
  loginByPasswordReset,
  registerUserAsyncReset,
  recoverPasswordByMobileReset,
  userInfoSet,
  userExistAsyncReset,
} = authSlice.actions;

export const selectRecoverPasswordByMobileResponse = (state) => state.recoverPasswordByMobile.data;
export const selectLoginByPasswordResponse = (state) => state.loginByPassword.data;
export const selectOtpResponse = (state) => state.otp.data;
export const selectRegisterUserResponse = (state) => state.registerUser.data;
export const selectUsernameResponse = (state) => state.username.data;
export const selectLastLoginDataResponse = (state) => state.lastLoginData.data;

export default authSlice.reducer;
