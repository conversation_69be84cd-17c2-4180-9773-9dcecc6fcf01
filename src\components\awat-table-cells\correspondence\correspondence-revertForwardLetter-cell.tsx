import KeyboardReturnIcon from '@mui/icons-material/KeyboardReturn';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';

import i18n from 'i18n';

const CorrespondenceRevertForwardLetterCell = ({ row, onClick }: any) => (
  <>
    <Tooltip
      title={i18n.t('table.correspondence.revertForwardLetter')}
      arrow
      componentsProps={{
        tooltip: {
          sx: {
            bgcolor: '#232f34',
            padding: 1,
            '& .MuiTooltip-arrow': {
              color: '#232f34',
            },
          },
        },
      }}
    >
      <IconButton
        onClick={(e) => {
          e.stopPropagation();
          onClick();
        }}
        disabled={row?.operation?.read?.at}
        size="small"
        sx={{
          borderRadius: '8px',
          border: '1px solid #FFE7E7',
          backgroundColor: row?.operation?.read?.at ? '#F3F3F3!important' : '#FFE7E7',
          padding: '2px',
        }}
      >
        <KeyboardReturnIcon sx={{ color: row?.operation?.read?.at ? '#ABABAB' : '#D96D56' }} />
      </IconButton>
    </Tooltip>
  </>
);

export default CorrespondenceRevertForwardLetterCell;
