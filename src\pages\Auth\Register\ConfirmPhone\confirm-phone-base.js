import { useRef, useEffect } from 'react';

import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import Dialog from '@mui/material/Dialog';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';

import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import HighlightOffIcon from '@mui/icons-material/HighlightOff';
import RefreshIcon from '@mui/icons-material/Refresh';
import SocialMedia from '../../../../components/SocialMedia';
import logo from '../../../../assets/images/logo/Logo.svg';
import { useMainLayoutStyles } from '../../../../contexts/mainLayoutStylesContext';
import Loading from '../../../../components/Loading';
import { useRegisterContext } from '../../../../contexts/pageContext/auth/registerContext';

const useStyles = makeStyles()((theme) => ({
  root: {
    width: '100%',
    height: '100%',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    minHeight: 650,
  },
  mobileRoot: {
    position: 'relative',
    textAlign: 'center',
    flexGrow: 1,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  mobileMiddlePaper: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '24px 19px 44.2px 19px',
    margin: 'auto',
    marginTop: 0,
  },
  logo: {
    display: 'flex',
    margin: theme.spacing(2, 0),
  },
  form: {
    height: '100%',
    width: '100%',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    flexGrow: 2,
  },
  input: {
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(3),
  },
  mobilePositionDown: {
    position: 'absolute',
    bottom: 10,
    width: '100%',
  },
  center: {
    textAlign: 'center',
  },
  marginTop5: {
    marginTop: theme.spacing(5),
  },
  marginTop2: {
    marginTop: theme.spacing(2),
  },
  boldText: {
    fontWeight: 'bold',
  },
  grayText: {
    color: '#808080',
  },
  inputUnderline: {
    display: 'block',
    margin: '1em auto',
    border: 'none',
    padding: '0',
    width: '6ch',
    background:
      'repeating-linear-gradient(90deg, #cdcdcd 0, #cdcdcd 1ch, transparent 0, transparent 1.5ch) 0 100%/5.5ch 2px no-repeat',
    font: '5ch droid sans mono, consolas, monospace',
    letterSpacing: '.5ch',
    fontSize: '2.5rem',
    direction: 'ltr',
    outline: 'unset',
  },
  resendCode: {
    background: 'unset',
    color: theme.palette.text.link,
    boxShadow: 'unset',
    margin: 'auto',
    padding: 0,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
  },
  dialog: {
    paddingTop: '60px',
    padding: '60px 50px',
    textAlign: 'center',
  },
  errorText: {
    color: theme.palette.text.error,
  },
  endTime: {
    display: 'flex',
    margin: 'auto',
    alignItems: 'center',
    '& svg': {
      fontSize: '1rem',
    },
  },
}));

const BaseConfirmPhone = ({ isMobile }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslation();
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes: globalClasses } = makeStyles()(stylesGenerator)();
  const ref = useRef(null);

  useEffect(() => {
    ref?.current?.focus();
  }, []);

  const {
    auth,
    counter,
    changeConfirm,
    confirmValue,
    resendOtp,
    submitConfirm,
    handleEditInfo,
    openDialogUserExist,
    closeUserExistedDialog,
    handleToLogin,
    loadingConfirmCode,
  } = useRegisterContext();

  return (
    <>
      <Container
        component="div"
        maxWidth="xs"
        className={isMobile ? classes.mobileRoot : classes.root}
      >
        <div className={isMobile ? classes.mobileMiddlePaper : globalClasses.middlePaper}>
          <img src={logo} alt="Logo" className={classes.logo} />
          <form className={cx(classes.form, classes.marginTop5)} onSubmit={submitConfirm}>
            <Typography variant="body1" className={cx(classes.center, classes.boldText)}>
              {t('auth.messages.enterVerificationCode')}
            </Typography>
            <Typography
              variant="subtitle2"
              className={cx(classes.center, classes.grayText, classes.marginTop2)}
            >
              {t('auth.messages.sentVerificationCode', { value: auth?.otp?.data?.mobile })}
            </Typography>
            {counter === 0 && (
              <Typography
                variant="body1"
                className={cx(classes.center, classes.errorText, classes.endTime)}
              >
                <HighlightOffIcon />
                {t('auth.messages.requestEndTime')}
              </Typography>
            )}
            {counter > 0 &&
              ((auth.registerUser.data && auth.registerUser.data?.error) ||
                auth?.registerUser?.data?.success === false) && (
                <Typography
                  variant="body1"
                  className={cx(classes.center, classes.errorText, classes.endTime)}
                >
                  <HighlightOffIcon />
                  {t('common.messages.pinInvalid')}
                </Typography>
              )}
            <input
              name="otp"
              ref={ref}
              onChange={changeConfirm}
              value={confirmValue}
              maxLength={4}
              className={classes.inputUnderline}
              autoComplete="off"
            />
            <Typography variant="subtitle1" className={cx(classes.center, classes.grayText)}>
              {counter > 0 ? (
                t('auth.messages.resendCode3min', { value: counter })
              ) : (
                <Button variant="text" className={classes.resendCode} onClick={resendOtp}>
                  <RefreshIcon />
                  {t('auth.messages.resendCode')}
                </Button>
              )}
            </Typography>
            <Button
              variant="contained"
              type="submit"
              fullWidth
              className={classes.marginTop2}
              disabled={counter === 0 || confirmValue.length < 4 || loadingConfirmCode}
            >
              {loadingConfirmCode ? <Loading color="white" /> : t('auth.labels.confirmation')}
            </Button>
            <Button
              type="button"
              onClick={handleEditInfo}
              fullWidth
              className={classes.marginTop2}
              disabled={loadingConfirmCode}
            >
              {t('auth.labels.editInfo')}
            </Button>
          </form>
        </div>
        <SocialMedia />
      </Container>
      <Dialog
        open={openDialogUserExist}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        onClose={(e, reason) => closeUserExistedDialog(e, reason)}
      >
        <DialogContent className={classes.dialog}>
          <DialogContentText id="alert-dialog-description">
            <Typography variant="body1" className={globalClasses.bold}>
              {t('auth.messages.existUser')}
            </Typography>
            <Typography variant="subtitle1" className={classes.marginTop2}>
              {t('auth.messages.enterIfRegistered')}
            </Typography>
          </DialogContentText>
          <Button
            variant="contained"
            onClick={handleToLogin}
            fullWidth
            className={classes.marginTop5}
          >
            {t('auth.labels.goToLogin')}
          </Button>
          <Button onClick={handleEditInfo} fullWidth>
            {t('auth.labels.changeNumber')}
          </Button>
        </DialogContent>
      </Dialog>
    </>
  );
};

BaseConfirmPhone.propTypes = {
  isMobile: PropTypes.bool,
};

BaseConfirmPhone.defaultProps = {
  isMobile: false,
};

export default BaseConfirmPhone;
