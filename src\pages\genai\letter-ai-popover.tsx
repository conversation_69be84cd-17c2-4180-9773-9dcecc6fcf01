import React from 'react';

import Popover from '@mui/material/Popover';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
import ToggleButton from '@mui/material/ToggleButton';
import CloseIcon from '@mui/icons-material/Close';
import CheckIcon from '@mui/icons-material/Check';

import AWBox from 'components/AWComponents/AWBox';
import AWIconButton from 'components/AWComponents/AWIconButton';
import AWTypography from 'components/AWComponents/AWTypography';
import AWTextField from 'components/AWComponents/AWTextField';
import AWButton from 'components/AWComponents/AWButton';
import { makeStyles } from 'tss-react/mui';
import dubi from 'assets/images/dubi.svg';
import { useTranslation } from 'react-i18next';
import Loading from '../../components/Loading';

type LetterAiPopoverProps = {
  anchorEl: HTMLElement | null;
  handleClose: () => void;
  open: boolean;
  selectedTabCreateLetterByTopic: string;
  handleToggleChangeTabGenai: (event: React.MouseEvent<HTMLElement>, value: string) => void;
  setTextFieldGenai: (value: string) => void;
  submitCreateLetterByTopic: () => void;
  genaiLoading: boolean;
  textFieldGenai: string;
};

const useStyles = makeStyles()((theme) => ({
  textfield: {
    margin: theme.spacing(1, 0),
    '& .MuiOutlinedInput-root': {
      borderRadius: '20px 20px 0px 20px',
      backgroundColor: theme.palette.background.lightPrimary,
    },
  },
  textfieldContent: {
    maxHeight: '200px',
    overflowY: 'auto',
  },
  popoverContainer: {
    padding: theme.spacing(2),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'start',
    width: '600px',
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    width: '100%',
    alignItems: 'start',
  },
  typography: {
    marginBottom: theme.spacing(2),
    textAlign: 'center',
    paddingLeft: theme.spacing(1),
  },
  toggleButtonGroup: {
    marginBottom: theme.spacing(2),
    width: '100%',
    display: 'flex',
    justifyContent: 'center',
  },
  toggleButtonContainer: {
    display: 'flex',
    gap: theme.spacing(2),
    width: '100%',
    justifyContent: 'center',
  },
  toggleButton: {
    borderRadius: '42px',
    flex: 1,
    padding: theme.spacing(1),
    border: '2px solid',
    borderColor: theme.palette.background.previewImage,
    '&.Mui-selected': {
      borderColor: theme.palette.text.link,
      color: theme.palette.text.link,
    },
    '&.MuiToggleButton-root': {
      backgroundColor: theme.palette.text.white,
      '&:hover': {
        backgroundColor: theme.palette.text.white,
      },
    },
  },
  checkIcon: {
    marginLeft: theme.spacing(1),
    color: theme.palette.text.link,
  },
  buttonContainer: {
    direction: 'ltr',
    width: '100%',
  },
  loading: {
    display: 'flex',
    alignItems: 'center',
    margin: theme.spacing(2),
    '& img': {
      width: 40,
      [theme.breakpoints.down('sm')]: {
        width: 25,
      },
    },
  },
  spacing: {
    marginTop: theme.spacing(1),
  },
  typing: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: theme.spacing(2),
  },
}));

const LetterAiPopover: React.FC<LetterAiPopoverProps> = ({
  anchorEl,
  handleClose,
  open,
  selectedTabCreateLetterByTopic,
  handleToggleChangeTabGenai,
  setTextFieldGenai,
  submitCreateLetterByTopic,
  genaiLoading,
  textFieldGenai,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslation();

  return (
    <Popover
      id="AI_Letter-Popup"
      open={open}
      anchorEl={anchorEl}
      onClose={handleClose}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'left',
      }}
      transformOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
    >
      <AWBox className={classes.popoverContainer}>
        <AWIconButton tooltip={t('tooltip.close')} onClick={handleClose} disabled={genaiLoading}>
          <CloseIcon />
        </AWIconButton>
        <AWBox className={classes.header}>
          <AWTypography variant="button" className={classes.typography}>
            {t('genai.labels.help-genai')}
          </AWTypography>
          <img src={dubi} alt="dubi" />
        </AWBox>

        <ToggleButtonGroup
          value={selectedTabCreateLetterByTopic}
          onChange={handleToggleChangeTabGenai}
          exclusive
          className={classes.toggleButtonGroup}
          disabled={genaiLoading}
        >
          <AWBox className={classes.toggleButtonContainer}>
            <ToggleButton className={classes.toggleButton} value="official">
              {selectedTabCreateLetterByTopic === 'official' && (
                <CheckIcon className={classes.checkIcon} />
              )}
              {t('genai.labels.official')}
            </ToggleButton>
            <ToggleButton className={classes.toggleButton} value="friendly">
              {selectedTabCreateLetterByTopic === 'friendly' && (
                <CheckIcon className={classes.checkIcon} />
              )}
              {t('genai.labels.friendly')}
            </ToggleButton>
          </AWBox>
        </ToggleButtonGroup>

        <AWTextField
          id="AI_Create_Letter_Prompt_Input"
          fullWidth
          multiline
          value={textFieldGenai}
          disabled={genaiLoading}
          onChange={(e) => setTextFieldGenai(e.target.value)}
          placeholder={t('genai.labels.content')}
          className={`${classes.textfield} ${classes.textfieldContent}`}
        />
        <AWBox className={classes.buttonContainer}>
          <AWButton
            id="AI_Create_Letter_Button"
            variant="contained"
            onClick={submitCreateLetterByTopic}
            disabled={genaiLoading || !textFieldGenai.length}
          >
            {t('genai.labels.create-letter')}
          </AWButton>
        </AWBox>
      </AWBox>
      {genaiLoading && (
        <AWBox className={classes.typing}>
          <AWBox className={classes.loading}>
            {t('genai.labels.create-letter-loading')}
            <Loading inline className={classes.spacing} />
          </AWBox>
          <img src={dubi} alt="dubi" />
        </AWBox>
      )}
    </Popover>
  );
};

export default LetterAiPopover;
