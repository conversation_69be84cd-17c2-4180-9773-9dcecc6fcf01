import { forwardRef } from 'react';

import { PatternFormat } from 'react-number-format';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import TextField from '@mui/material/TextField';
import FormHelperText from '@mui/material/FormHelperText';
import OutlinedInput from '@mui/material/OutlinedInput';
import FormControl from '@mui/material/FormControl';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import IconButton from '@mui/material/IconButton';

import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckIcon from '@mui/icons-material/Check';
import RemoveIcon from '@mui/icons-material/Remove';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import SocialMedia from '../../../../components/SocialMedia';
import logo from '../../../../assets/images/logo/Logo.svg';
import Loading from '../../../../components/Loading';
import { useMainLayoutStyles } from '../../../../contexts/mainLayoutStylesContext';
import { useRecoverPasswordContext } from '../../../../contexts/pageContext/auth/recoverPasswordContext';

const useStyles = makeStyles()((theme) => ({
  root: {
    width: '100%',
    height: '100%',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    minHeight: 650,
  },
  mobileRoot: {
    position: 'relative',
    textAlign: 'center',
    flexGrow: 1,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  mobileMiddlePaper: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '24px 19px 44.2px 19px',
    margin: 'auto',
    marginTop: 0,
    '& .marginTopMobile': {
      marginTop: theme.spacing(3),
    },
  },
  logo: {
    display: 'flex',
    margin: '16px auto',
  },
  form: {
    height: '100%',
    width: '100%',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
  },
  input: {
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(3),
  },
  ltrInput: {
    '& input': {
      direction: 'ltr',
    },
  },
  phoneNumberInput: {
    marginTop: theme.spacing(1),
    direction: 'ltr',
    '& input': {
      letterSpacing: '2px',
    },
  },
  center: {
    textAlign: 'center',
  },
  marginTop: {
    marginTop: theme.spacing(5),
  },
  boldText: {
    fontWeight: 'bold',
  },
  titleRecover: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    '& p': {
      flexGrow: 2,
    },
    '& svg': {
      position: 'absolute',
      right: 0,
    },
  },
  goToLoginBtn: {
    padding: 8,
    backgroundColor: '#f5ecff',
    color: theme.palette.primary.main,
    position: 'absolute',
    fontSize: '2rem',
    borderRadius: '50%',
  },
  logoRow: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
}));

const NumericFormatCustom = forwardRef((props) => {
  const { onChange, ...other } = props;

  return (
    <PatternFormat
      {...other}
      format="#### ### ####"
      value="09"
      onValueChange={(values) => {
        onChange({
          target: {
            name: props.name,
            value: values.value,
          },
        });
      }}
      valueIsNumericString
    />
  );
});

function BaseGetMobile({ isMobile }) {
  const { classes, cx } = useStyles();
  const { t } = useTranslation();
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes: globalClasses } = makeStyles()(stylesGenerator)();

  const {
    getValidationErrorEx,
    getValidationError,
    isInvalid,
    state,
    showPassword,
    onShowPassword,
    goToLogin,
    submitForgetPassword,
    onChange, // setState,
    phonErrorText,
    passwordError,
    setPasswordError,
    loadingForgetPassword,
  } = useRecoverPasswordContext();

  return (
    <>
      <Container
        component="div"
        maxWidth="xs"
        className={isMobile ? classes.mobileRoot : classes.root}
      >
        <div className={isMobile ? classes.mobileMiddlePaper : globalClasses.middlePaper}>
          <div className={classes.logoRow}>
            {isMobile && (
              <IconButton aria-label="arrowForward" onClick={goToLogin}>
                <ArrowForwardIcon className={classes.goToLoginBtn} />
              </IconButton>
            )}
            <img src={logo} alt="Logo" className={classes.logo} />
          </div>
          <form className={cx(classes.form)} onSubmit={submitForgetPassword}>
            <div className={classes.titleRecover}>
              {!isMobile && (
                <IconButton aria-label="arrowForward" onClick={goToLogin}>
                  <ArrowForwardIcon />
                </IconButton>
              )}
              <Typography variant="body1" className={cx(classes.center, globalClasses.bold)}>
                {t('auth.labels.recoverPassword')}
              </Typography>
            </div>
            <TextField
              name="mobile"
              value={state?.mobile}
              onChange={onChange}
              fullWidth
              label={`* ${t('common.labels.mobile')} `}
              variant="outlined"
              className={cx(classes.phoneNumberInput, classes.marginTop)}
              error={phonErrorText || isInvalid('mobile')}
              helperText={
                getValidationError('mobile') ? getValidationError('mobile') : phonErrorText
              }
              InputProps={{
                inputComponent: NumericFormatCustom,
              }}
            />
            <FormControl variant="outlined" className={classes.input} fullWidth>
              <InputLabel
                error={passwordError || isInvalid('password')}
                htmlFor="outlined-adornment-password"
              >
                {`${t('auth.labels.newPassword')} * `}
              </InputLabel>
              <OutlinedInput
                className={classes.ltrInput}
                id="outlined-adornment-password"
                type={showPassword ? 'text' : 'password'}
                name="password"
                value={state?.password}
                onChange={onChange}
                onFocus={() => setPasswordError(false)}
                error={passwordError || isInvalid('password') || getValidationError('password')}
                endAdornment={
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={onShowPassword}
                      edge="end"
                    >
                      {showPassword ? <Visibility /> : <VisibilityOff />}
                    </IconButton>
                  </InputAdornment>
                }
              />
              <FormHelperText id="my-helper-text" component="div">
                <ul className={cx(globalClasses.validationPassword)}>
                  <li className={cx(globalClasses.itemValidationPassword)}>
                    {getValidationErrorEx('password') &&
                    !getValidationErrorEx('password').find(
                      (x) => x.type === 'passwordComplexity.tooShort',
                    ) ? (
                      <CheckIcon />
                    ) : (
                      <RemoveIcon className="dash" />
                    )}
                    <span>{t('common.validation.min8')}</span>
                  </li>
                  <li className={cx(globalClasses.itemValidationPassword)}>
                    {getValidationErrorEx('password') &&
                    !getValidationErrorEx('password').find(
                      (x) => x.type === 'passwordComplexity.lowercase',
                    ) &&
                    !getValidationErrorEx('password').find(
                      (x) => x.type === 'passwordComplexity.uppercase',
                    ) ? (
                      <CheckIcon />
                    ) : (
                      <RemoveIcon className="dash" />
                    )}
                    <span>{t('common.validation.lowerAndUpper')}</span>
                  </li>
                  <li className={cx(globalClasses.itemValidationPassword)}>
                    {getValidationErrorEx('password') &&
                    !getValidationErrorEx('password').find(
                      (x) => x.type === 'passwordComplexity.symbol',
                    ) ? (
                      <CheckIcon />
                    ) : (
                      <RemoveIcon className="dash" />
                    )}
                    <span>{t('common.validation.specialChar')}</span>
                  </li>
                  <li className={cx(globalClasses.itemValidationPassword)}>
                    {getValidationErrorEx('password') &&
                    !getValidationErrorEx('password').find(
                      (x) => x.type === 'passwordComplexity.numeric',
                    ) ? (
                      <CheckIcon />
                    ) : (
                      <RemoveIcon className="dash" />
                    )}
                    <span>{t('common.validation.includeNumber')}</span>
                  </li>
                </ul>
              </FormHelperText>
            </FormControl>
            <div className={classes.marginTop}>
              <Button variant="contained" type="submit" fullWidth disabled={loadingForgetPassword}>
                {loadingForgetPassword ? <Loading color="white" /> : t('common.labels.continue')}
              </Button>
            </div>
          </form>
        </div>
        <SocialMedia />
      </Container>
    </>
  );
}

BaseGetMobile.propTypes = {
  isMobile: PropTypes.bool,
};

BaseGetMobile.defaultProps = {
  isMobile: false,
};

export default BaseGetMobile;
