import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import <PERSON><PERSON> from 'joi';
import { v4 as uuidv4 } from 'uuid';
import { connect } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import useMediaQuery from '@mui/material/useMediaQuery';
import axios from 'axios';

import { t } from 'i18next';
import { baseUrl } from 'api/v1/services/api-client';
import DesktopOrganization from './organization2-Desktop';
import TabletOrganization from './organization2-tablet';
import { useUser } from '../../../../contexts/userContext';
import {
  checkExistOrganizationAsync as checkExistOrganizationAsync_,
  organizationInfoAsync as organizationInfoAsync_,
  organizationInfoReset as organizationInfoReset_,
  userOrganizationsAsync as userOrganizationsAsync_,
  ownedOrganizationsAsync as ownedOrganizationsAsync_,
  editOrganizationAsync as editOrganizationAsync_,
  createOrganizationAsync as createOrganizationAsync_,
  createOrganizationReset as createOrganizationReset_,
  editOrganizationLegalInfoAsync as editOrganizationLegalInfoAsync_,
  organizationLegalInfoReset as organizationLegalInfoReset_,
  organizationLegalInfoAsync as organizationLegalInfoAsync_,
  userOrganizationsReset as userOrganizationsReset_,
  ownedOrganizationsReset as ownedOrganizationsReset_,
} from '../organizationSlice';
import { uploadDocumentsAsync as uploadDocumentsAsync_ } from '../../../documents/documentsSlice';
import { withValidation } from '../../../../common/validation';
import { RouterPrompt } from '../../../../components/RouterPrompt';
import { OrganizationContext } from '../../../../contexts/pageContext/organization/organizationContext';
import { convertDigitsToEnglish } from '../../../../utils';
import withOrganizationSecurity, {
  propTypesOrganizationSecurity,
} from './withOrganizationSecurity';
import { withManagementCenterInitializer } from '../../with-management-center-initializer';
import { IOrganization, IOrganizationLegalInfo } from './Organization.model';
import MobileOrganization from './organization-mobile/organization2-mobile';
import snackbarUtils from '../../../../utils/snackbarUtils';

interface IOrganizationState {
  id?: string;
  organization: IOrganization;
  legalInfo: IOrganizationLegalInfo;
}

const initialState: IOrganizationState = {
  organization: {
    name: '',
    displayName: '',
    displayName_en: '',
    logoDocumentId: '',
    stamps: [],
    id: '',
    _id: '',
  },
  legalInfo: {
    businessName: '',
    registeredCode: '',
    nationalId: '',
    commercialCode: '',
    legalRepresentativeName: '',
    legalRepresentativePosition: '',
    legalRepresentativeNationalId: '',
    legalInfoUpdateDate: '',
    phone: '',
    address: '',
    city: '',
    province: '',
    postalCode: '',
  },
};

const schema = {
  name: Joi.string()
    .min(3)
    .max(32)
    .required()
    .regex(/^[A-Za-z\d_]+$/),
  displayName: Joi.string().required().min(3).max(100),
  displayName_en: Joi.string().optional().min(3).max(100),
  stamp: Joi.object({
    type: Joi.string().valid('image/png').messages({
      'any.only': 'فرمت فایل باید PNG باشد.',
    }),
    size: Joi.number()
      .max(1 * 1024 * 1024)
      .messages({
        'number.max': 'حجم فایل نباید بیشتر از ۱ مگابایت باشد.',
      }),
  }),
};

const Organization2Media = () => {
  const mobile = useMediaQuery((theme: any) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme: any) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme: any) => theme.breakpoints.up('lg'));

  if (mobile) return <MobileOrganization />;
  if (tablet) return <TabletOrganization />;
  if (desktop) return <DesktopOrganization />;
  return <></>;
};

const Organization = withValidation({
  initialState: initialState.organization,
  schema,
  comparers: null,
})((props: any) => {
  const {
    organization,
    isInvalid,
    getValidationError,
    validationOnChange,
    validationOnSubmit,
    uploadDocumentsAsync,
    editOrganizationAsync,
    createOrganizationAsync,
    createOrganizationReset,
    organizationInfoAsync,
    organizationInfoReset,
    organizationLegalInfoAsync,
    organizationLegalInfoReset,
    editOrganizationLegalInfoAsync,
    ownedOrganizationsReset,
    userOrganizationsReset,
    security,
  } = props;

  const params = useParams();
  const { organizationId } = params;
  const navigate = useNavigate();

  const [file, setFile] = useState<FormData | null | string>();
  const [isImageValid, setIsImageValid] = useState(false);
  const [showLoading, setShowLoading] = useState(true);
  const [showLoadingInButton, setShowLoadingInButton] = useState(false);
  const newFileRef = useRef<{ id: string } | null>(null);
  const [nameChanged, setNameChanged] = useState<boolean>(false);
  const [isShowDialogEditOrganization, setIsShowDialogEditOrganization] = useState(false);

  const [organizationExisted, setOrganizationExisted] = useState(false);

  const [state, setState] = useState<IOrganizationState>(initialState);
  const [initState, setInitState] = useState<IOrganizationState>(initialState);
  const [tab, setTab] = useState<'fa' | 'en'>('fa');
  //* stamp states ---------------------------------------
  const newStampsRef = useRef<{ id: string }[] | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [RemoveStampDialog, setRemoveStampDialog] = useState<boolean>(false);
  const [openPreviewDialog, setOpenPreviewDialog] = useState(false);

  // onMounted
  useEffect(() => {
    (async () => {
      setShowLoading(true);
      await initalData();

      setTimeout(() => {
        setShowLoading(false);
      }, 200);
    })();
  }, []);

  // onUnmounted
  useEffect(
    () => () => {
      resetAll();
    },
    [],
  );
  const type = state?.organization?._id ? 'editAndView' : 'create';

  const [touched, setTouched] = useState<string[]>([]);
  const [touchedLegalInfo, setTouchedLegalInfo] = useState<string[]>([]);
  const isTouched = touched?.length > 0 || touchedLegalInfo?.length > 0;
  const isBlocking = touched?.length > 0 || touchedLegalInfo?.length > 0;

  const getOrganizationLegalInfo = async () => {
    if (organization.organizationLegalInfo.data) return organization.organizationLegalInfo.data;
    const { payload: organizationLegalInfoResult } = await organizationLegalInfoAsync({
      organizationId,
    });
    if (organizationLegalInfoResult?.success === false) return initialState.legalInfo;
    if (organization.organizationLegalInfo.data) return organization.organizationLegalInfo.data;
    return organizationLegalInfoResult ?? initialState.legalInfo;
  };

  const getOrganization = async () => {
    if (organization.organizationInfo.data) return organization.organizationInfo.data;
    const options = {
      organizationId,
    };

    const { payload: organizationResult } = await organizationInfoAsync(options);
    if (organizationResult?.success === false) return initialState.organization;
    if (organization.organizationInfo.data) return organization.organizationInfo.data;
    return organizationResult ?? initialState.organization;
  };

  // inital data
  const initalData = async () => {
    if (organizationId) {
      const organizationInfoData = await getOrganization();
      const organizationLegalInfoData = await getOrganizationLegalInfo();
      setInitData({ organization: organizationInfoData, legalInfo: organizationLegalInfoData });
    }
  };
  const resetAll = async () => {
    organizationInfoReset();
    organizationLegalInfoReset();
    createOrganizationReset();
    organizationLegalInfoReset();
    ownedOrganizationsReset();
    userOrganizationsReset();
    setFile(null);
    newFileRef.current = null;
    newStampsRef.current = null;
  };

  const setInitData = (data: IOrganizationState) => {
    setState((preState: IOrganizationState) => ({
      ...preState,
      organization: data.organization,
      legalInfo: data.legalInfo,
    }));
    setInitState((preState: IOrganizationState) => ({
      ...preState,
      organization: data.organization,
      legalInfo: data.legalInfo,
    }));
  };
  const editOrganizationLegalInfo = async (value?: IOrganizationLegalInfo) => {
    if (!touchedLegalInfo?.length) return;
    const legalInfo = value ?? state.legalInfo;
    const { _id, legalInfoUpdateDate, ...other } = {
      ...legalInfo,
      id: legalInfo?.id ?? organizationId,
    };
    const { payload } = await editOrganizationLegalInfoAsync(other);
    if (payload?.success) resetTouchedLegalInfo();
  };

  const upload = async (fileData?: FormData | null) => {
    if (fileData) {
      const correlationId = uuidv4();
      fileData.append('domain', 'management-center');
      fileData.append('organization', JSON.stringify({ id: organizationId }));

      const headers = {
        domain: 'management-center',
        organization: organizationId,
      };

      const { payload: result } = await uploadDocumentsAsync({
        correlationId,
        data: fileData,
        headers,
      });
      return result;
    }
    return null;
  };

  const uploadLogo = async () => {
    const result = await upload(file as FormData);
    if (result?.[0]) newFileRef.current = result?.[0];
    else newFileRef.current = null;
  };

  const uploadStamp = async () => {
    if (state.organization.stamps?.length) {
      if (state.organization.stamps[0].stamp) {
        const result = await upload(state.organization.stamps[0].stamp);
        newStampsRef.current = result.map((i: any) => i.id);
      } else {
        newStampsRef.current = state.organization.stamps.map((i: any) => i.id);
      }
      return;
    }
    newStampsRef.current = null;
  };

  const submitData = async () => {
    try {
      if (!validationOnSubmit(state.organization)) return;
      setShowLoadingInButton(true);

      if (type === 'editAndView') {
        const legalInfoKeys = [
          'businessName',
          'registeredCode',
          'nationalId',
          'commercialCode',
          'legalRepresentativeName',
          'legalRepresentativePosition',
          'legalRepresentativeNationalId',
          'legalInfoUpdateDate',
          'phone',
          'address',
          'city',
          'province',
          'postalCode',
        ];
        const includeLegalInfoKeys = touchedLegalInfo.some((item) => legalInfoKeys.includes(item));

        if (includeLegalInfoKeys) {
          setIsShowDialogEditOrganization(true);
        } else {
          await uploadLogo();
          await uploadStamp();
          await updateOrg();
          await editOrganizationLegalInfo();
          resetAll();
        }
      }
      if (type === 'create') {
        await uploadLogo();
        await uploadStamp();
        const result = await createOrg();
        if (!result) throw new Error('error');
        await editOrganizationLegalInfo({ ...state.legalInfo, id: result.data.id });
        resetAll();
        setTimeout(() => {
          navigate('/organizations');
        }, 200);
      }
    } catch {
      setShowLoadingInButton(false);
    } finally {
      setShowLoadingInButton(false);
    }
  };

  const submitEditOrganization = async () => {
    try {
      if (!validationOnSubmit(state.organization)) return;
      setShowLoadingInButton(true);
      await uploadLogo();
      await uploadStamp();
      await updateOrg();
      await editOrganizationLegalInfo();
      resetAll();
    } catch {
      setShowLoadingInButton(false);
    } finally {
      setShowLoadingInButton(false);
      setIsShowDialogEditOrganization(false);
    }
  };

  const BackToOrgList = () => {
    navigate('/organizations');
  };

  const deleteFile = async () => {
    try {
      setShowLoadingInButton(true);
      const options = {
        data: {
          name: convertDigitsToEnglish(state.organization.name),
          displayName: state.organization.displayName,
          displayName_en: state.organization.displayName_en,
          id: state.organization._id,
          logoDocumentId: null,
          stamps: [],
        },
      };

      const { payload: editResult } = await editOrganizationAsync(options);

      if (editResult?.success) {
        setInitData({
          ...state,
          organization: { ...state.organization, logoDocumentId: null },
        });
        resetTouched();
        resetAll();
      }
    } finally {
      setTimeout(() => {
        setShowLoadingInButton(false);
      }, 200);
    }
  };

  const checkExistOrganization = async () =>
    new Promise<any>((resolve, reject) => {
      axios
        .get(`${baseUrl}/api/v1/organizations/name/${state.organization.name}`)
        .then((res: any) => {
          resolve(res);
        })
        .catch((err: any) => {
          resolve(err?.response);
        });
    });

  const createOrg = async (): Promise<{ data: IOrganization } | null> => {
    if (!touched.length) return null;
    const res = await checkExistOrganization();
    const { data: existData, status } = res;

    if (status === 200 && (existData.data.id || existData.data._id)) {
      setOrganizationExisted(true);
      setShowLoadingInButton(false);
      return null;
    }
    if (status === 404 && existData?.data === null) {
      setOrganizationExisted(false);
      const options = {
        data: {
          name: convertDigitsToEnglish(state.organization.name),
          displayName: state.organization.displayName,
          displayName_en: state.organization.displayName_en,
          id: uuidv4(),
          logoDocumentId: newFileRef?.current?.id,
          stamps: state.organization.stamps.map(({ fileSize, fileName, uploadDate }, index) => ({
            uploadDate,
            fileSize,
            fileName,
            id: newStampsRef.current?.[index],
          })),
        },
      };

      const { payload } = await createOrganizationAsync(options);
      if (payload?.success) {
        setInitData({
          ...state,
          organization: {
            ...state.organization,
            _id: payload?.data?._id,
            id: payload?.data?.id,
            logoDocumentId: payload?.data?.logoDocumentId,
            stamps: payload?.data.stamps,
          },
        });
        resetAll();
        resetTouched();
        return payload;
      }
    }
    return null;
  };

  const updateOrg = async () => {
    if (
      !touched.length &&
      !newFileRef?.current?.id &&
      newStampsRef.current !== null &&
      !newStampsRef.current?.length
    )
      return false;
    const options = {
      data: {
        name: convertDigitsToEnglish(state.organization.name),
        displayName: state.organization.displayName,
        displayName_en: state.organization.displayName_en,
        id: state.organization._id,
        logoDocumentId: newFileRef?.current?.id || state.organization.logoDocumentId,
        stamps: state.organization.stamps.map(({ fileSize, fileName, uploadDate }, index) => ({
          uploadDate,
          fileSize,
          fileName,
          id: newStampsRef.current?.[index],
        })),
      },
    };

    const { payload } = await editOrganizationAsync(options);
    if (payload?.success) {
      setInitData({
        ...state,
        organization: {
          ...state.organization,
          logoDocumentId: payload?.data?.logoDocumentId,
          stamps: payload?.data?.stamps,
        },
      });
      resetAll();
      resetTouched();
      return true;
    }
    return false;
  };

  const imageProps = {
    imageId: state.organization?.logoDocumentId ?? '',
    typeOfImage: 'profile',
    typeOfPage: type,
    data: {},
    style: {},
    description: [t('organization.labels.fileSize')],
    desktopTextBtn: t('organization.labels.newLogo'),
    mobileText: t('organization.labels.logoOrganization'),
    setFile,
    setIsImageValid,
    size: 1,
  };

  const resetTouched = useCallback(() => {
    setTouched([]);
  }, []);

  const resetTouchedLegalInfo = () => {
    setTouchedLegalInfo([]);
  };

  const checkTouchedItem = (name: string, value: any): void => {
    setTouched((preState: string[]) => {
      if (initState.organization[name as keyof IOrganization] === value) {
        const newState = preState.filter((item: any) => item !== name);
        return [...newState];
      }

      const exist = touched.find((item: any) => item === name);
      if (exist) return preState;

      return [...preState, name];
    });
  };

  const checkTouchedLegalInfoItem = (name: string, value: any): void => {
    setTouchedLegalInfo((preState: string[]) => {
      if (initState.legalInfo[name as keyof IOrganizationLegalInfo] === value) {
        const newState = preState.filter((item: any) => item !== name);
        return [...newState];
      }

      const exist = touchedLegalInfo.find((item: any) => item === name);
      if (exist) return preState;

      return [...preState, name];
    });
  };

  const onChange = (e: any) => {
    validationOnChange(e);

    checkTouchedItem(e.target.name, e.target.value);

    if (e.target.name === 'name') setNameChanged(true);
    setState((preState: IOrganizationState) => ({
      ...preState,
      organization: { ...preState?.organization, [e.target.name]: e.target.value },
    }));
  };

  const onChangeLegalInfo = (e: any) => {
    // validationOnChange(e);
    checkTouchedLegalInfoItem(e.target.name, e.target.value);

    setState((preState: IOrganizationState) => ({
      ...preState,
      legalInfo: { ...preState?.legalInfo, [e.target.name]: e.target.value },
    }));
  };

  const onChangeAutocomplete = (stateKey: any, e: any, newValue: any) => {
    onChangeLegalInfo({
      target: { name: stateKey, value: newValue || '' },
    });
  };

  //* stamp ----------------------------------------
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const fileStamp = event.target.files?.[0];
    if (!fileStamp) return;

    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    const { error } = schema.stamp.validate({
      type: fileStamp.type,
      size: fileStamp.size,
    });
    if (error) {
      snackbarUtils.error(error.details[0].message);
      setPreviewImage(null);
      return;
    }

    // Generate preview
    const reader = new FileReader();
    reader.onload = () => {
      const stamp = new FormData();
      stamp.append('file', fileStamp);

      setPreviewImage(reader.result as string);

      setState((preState: IOrganizationState) => ({
        ...preState,
        organization: {
          ...preState?.organization,
          stamps: [
            {
              ...preState?.organization.stamps[0],
              stamp,
            },
          ],
        },
      }));

      setOpenPreviewDialog(true);
    };
    reader.readAsDataURL(fileStamp);
  };

  const handleOpenRemoveStampsDialog = () => {
    setRemoveStampDialog(true);
  };

  const handleCloseRemoveStampsDialog = () => {
    setRemoveStampDialog(false);
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleCloseDialog = () => {
    setOpenPreviewDialog(false);
    setPreviewImage(null);
  };

  // Function to handle removing the uploaded image
  const handleRemoveImage = () => {
    setPreviewImage(null);
    newStampsRef.current = [];
    setState((preState: IOrganizationState) => ({
      ...preState,
      organization: { ...preState?.organization, stamps: [] },
    }));
    setRemoveStampDialog(false);
  };

  const handleConfirmStamp = () => {
    const stampForm = state.organization.stamps?.[0].stamp;
    const data = stampForm?.get('file') as File;
    if (stampForm && data) {
      setState((preState: IOrganizationState) => ({
        ...preState,
        organization: {
          ...preState?.organization,
          stamps: [
            {
              ...preState?.organization.stamps[0],
              src: previewImage,
              uploadDate: new Date().toISOString(),
              fileName: data.name,
              fileSize: data.size,
            },
          ],
        },
      }));
      setPreviewImage(null);
      setOpenPreviewDialog(false);
    }
  };

  const isStampChanges = useMemo(() => {
    const currentStamps = state.organization.stamps || [];
    const initialStamps = initState.organization.stamps || [];

    if (currentStamps.length !== initialStamps.length) {
      return false;
    }

    if (initialStamps.length && currentStamps[0]?.src) {
      return false;
    }

    return true;
  }, [state.organization.stamps, initState.organization.stamps]);

  const isDisabled = useMemo(() => {
    if (isImageValid) {
      return showLoadingInButton || (!isTouched && isStampChanges);
    }

    const isFileInvalid = !file || file === 'delete';
    return showLoadingInButton || (!isTouched && isFileInvalid && isStampChanges);
  }, [isImageValid, showLoadingInButton, isTouched, isStampChanges, file]);

  return (
    <OrganizationContext.Provider
      value={{
        state: state.organization,
        legalState: state.legalInfo,
        type,
        organization,
        touched,
        isInvalid,
        imageProps,
        getValidationError,
        file,
        setFile,
        isTouched,
        submitData,
        deleteFile,
        showLoading,
        showLoadingInButton,
        BackToOrgList,
        validationOnChange,
        setTouched: () => {},
        isBlocking,
        onChangeLegalInfo,
        onChange,
        onChangeAutocomplete,
        isImageValid,
        security,
        organizationExisted,
        isShowDialogEditOrganization,
        setIsShowDialogEditOrganization,
        submitEditOrganization,
        setTab,
        tab,
        handleFileUpload,
        handleUploadClick,
        handleCloseDialog,
        handleRemoveImage,
        openPreviewDialog,
        previewImage,
        fileInputRef,
        handleConfirmStamp,
        isDisabled,
        RemoveStampDialog,
        handleOpenRemoveStampsDialog,
        handleCloseRemoveStampsDialog,
      }}
    >
      <RouterPrompt isBlocking={isBlocking} />
      <Organization2Media />
    </OrganizationContext.Provider>
  );
});

const mapStateToProps = (state: any) => {
  const { organization, documents, profile } = state;
  return {
    organization,
    documents,
    profile,
  };
};

const Origanization2WithData = connect(mapStateToProps, {
  checkExistOrganizationAsync: checkExistOrganizationAsync_,
  organizationInfoAsync: organizationInfoAsync_,
  organizationInfoReset: organizationInfoReset_,
  userOrganizationsAsync: userOrganizationsAsync_,
  ownedOrganizationsAsync: ownedOrganizationsAsync_,
  uploadDocumentsAsync: uploadDocumentsAsync_,
  editOrganizationAsync: editOrganizationAsync_,
  createOrganizationAsync: createOrganizationAsync_,
  createOrganizationReset: createOrganizationReset_,
  editOrganizationLegalInfoAsync: editOrganizationLegalInfoAsync_,
  organizationLegalInfoReset: organizationLegalInfoReset_,
  organizationLegalInfoAsync: organizationLegalInfoAsync_,
  userOrganizationsReset: userOrganizationsReset_,
  ownedOrganizationsReset: ownedOrganizationsReset_,
})(Organization);

const WithInit = withManagementCenterInitializer(withOrganizationSecurity(Origanization2WithData));

const withTypeOrganizationPage = (WrappedComponent: any) => (props: any) => {
  const location = useLocation();
  if (location.pathname === '/organizations/create-organization') {
    return <WrappedComponent {...props} />;
  }
  return <WithInit {...props} />;
};

export default withTypeOrganizationPage(withOrganizationSecurity(Origanization2WithData));
