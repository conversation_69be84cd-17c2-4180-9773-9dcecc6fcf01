import React from 'react';

import { makeStyles } from 'tss-react/mui';
import Grid from '@mui/material/Grid';
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined';

import { t } from 'i18next';
import { useCartableForwardedLettersContext } from 'contexts/pageContext/letters/cartable-components/forwarded-letters-context';
import AWBox from 'components/AWComponents/AWBox';
import PositionEmployeeInput from 'pages/correspondence/letters/letter/components/position-employee-input';
import CustomizedDatePicker from 'components/DatePicker/data-picker';
import AWButton from 'components/AWComponents/AWButton';

const useStyles = makeStyles()({
  boxFilter: {
    display: 'flex',
    justifyContent: 'space-around',
    alignItems: 'center',
    border: '1px solid #e5e0e0',
    margin: '16px 0px',
    padding: '8px',
    borderRadius: '8px',
  },
  itemFilter: {
    padding: 8,
  },
});

const AllForwardedLettersFilterDesktop = () => {
  const { classes } = useStyles();
  const {
    organizationId,
    allForwardedLettersFilter,
    submitAllForwardedLettersFilter,
    openPositionEmployeeTreeDialogRecipient,
    openPositionEmployeeTreeDialogSender,
    handleOpenPositionEmployeeTreeDialogRecipient,
    handleClosePositionEmployeeTreeDialogRecipient,
    handleClosePositionEmployeeTreeDialogSender,
    handleOpenPositionEmployeeTreeDialogSender,
    allForwardedLettersMeta,
  } = useCartableForwardedLettersContext();

  const {
    senderValue,
    setSenderValue,
    recipientValue,
    setRecipientValue,
    toValue,
    setToValue,
    fromValue,
    setFromValue,
  } = allForwardedLettersFilter;

  return (
    <AWBox className={classes.boxFilter}>
      <Grid container columns={12}>
        <Grid xs={3} item className={classes.itemFilter}>
          <PositionEmployeeInput
            organizationId={organizationId}
            label={t('correspondence.letters.labels.forwardSender')}
            name="sender"
            isSingleSelection
            selectedPositionEmployee={senderValue}
            handleChangeAutocomplete={(e: any, value: any) => setSenderValue(value)}
            openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogSender}
            handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogSender}
            handleClosePositionEmployeeViewerDialog={handleClosePositionEmployeeTreeDialogSender}
            onSubmitPositionEmployee={(e: any, value: any) => setSenderValue(value)}
            isLetterEditable={() => false}
          />
        </Grid>
        <Grid xs={3} item className={classes.itemFilter}>
          <PositionEmployeeInput
            organizationId={organizationId}
            label={t('secretariats.labels.forwardReceivers')}
            name="recipient"
            isSingleSelection
            selectedPositionEmployee={recipientValue}
            handleChangeAutocomplete={(e: any, value: any) => setRecipientValue(value)}
            openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogRecipient}
            handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogRecipient}
            handleClosePositionEmployeeViewerDialog={handleClosePositionEmployeeTreeDialogRecipient}
            onSubmitPositionEmployee={(e: any, value: any) => setRecipientValue(value)}
          />
        </Grid>
        <Grid xs={3} item className={classes.itemFilter}>
          <CustomizedDatePicker
            name="fromDate"
            id="fromDate"
            label={t('secretariats.labels.fromDate')}
            value={fromValue}
            onChange={(value) => setFromValue(value)}
            hideClose={false}
          />
        </Grid>
        <Grid xs={3} item className={classes.itemFilter}>
          <CustomizedDatePicker
            name="toDate"
            id="toDate"
            label={t('secretariats.labels.toDate')}
            value={toValue}
            onChange={(value) => setToValue(value)}
            hideClose={false}
          />
        </Grid>
      </Grid>
      <AWButton
        disabled={allForwardedLettersMeta.loading}
        type="submit"
        onClick={submitAllForwardedLettersFilter}
        startIcon={<FilterAltOutlinedIcon />}
        variant="contained"
      >
        {t('secretariats.labels.FilterAdded')}
      </AWButton>
    </AWBox>
  );
};

export default AllForwardedLettersFilterDesktop;