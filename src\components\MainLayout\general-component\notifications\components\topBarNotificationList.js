import React from 'react';

import { makeStyles } from 'tss-react/mui';
import Typography from '@mui/material/Typography';
import Switch from '@mui/material/Switch';
import FormControlLabel from '@mui/material/FormControlLabel';
import RefreshOutlinedIcon from '@mui/icons-material/RefreshOutlined';
import CheckOutlinedIcon from '@mui/icons-material/CheckOutlined';
import IconButton from '@mui/material/IconButton';
import Box from '@mui/material/Box';

import { useNotificationsContext } from '../../../../../contexts/notificationContext';
import CustomTooltip from '../../../../tooltip';

const useStyles = makeStyles()((theme) => ({
  topBar: {
    display: 'flex',
    alignItems: 'center',
    boxShadow: '0 2px 4px 0 rgba(0, 0, 0, 0.09)',
    borderBottom: 'solid 1px #d1d1d1',
    height: 56,
    padding: theme.spacing(2.1, 1.5),
    position: 'absolute',
    width: '100%',
    background: '#fff',
  },
  title: {
    flexGrow: 1,
    fontWeight: 'bold',
  },
  topBarActions: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    [theme.breakpoints.down('sm')]: {
      width: '100%',
    },
  },
  FormControlRoot: {
    direction: 'ltr',
    margin: 0,
  },
  FormControlLabel: {
    fontSize: 12,
    color: '#606060',
  },
  icon: {
    background: '#f3f3f3',
    color: theme.palette.primary.main,
    margin: 4,
    width: 32,
    height: 32,
    padding: 4,
    borderRadius: 4,
  },
}));

const TopBarNotificationList = () => {
  const { classes } = useStyles();

  const {
    mobile,
    readAllNotifications,
    refreshNotifications,
    isAllNotificationMode,
    setIsAllNotificationMode,
  } = useNotificationsContext();

  return (
    <Box className={classes.topBar}>
      {!mobile && <Typography className={classes.title}>اعلان‌ها</Typography>}
      <Box className={classes.topBarActions}>
        <FormControlLabel
          control={
            <Switch
              checked={isAllNotificationMode}
              onChange={(_, checked) => setIsAllNotificationMode(checked)}
            />
          }
          label="فقط خوانده نشده‌ها"
          classes={{ root: classes.FormControlRoot, label: classes.FormControlLabel }}
        />
        <Box>
          <CustomTooltip title="notification.update">
            <IconButton classes={{ root: classes.icon }} onClick={refreshNotifications}>
              <RefreshOutlinedIcon />
            </IconButton>
          </CustomTooltip>
          <CustomTooltip title="notification.readAll">
            <IconButton classes={{ root: classes.icon }} onClick={readAllNotifications}>
              <CheckOutlinedIcon />
            </IconButton>
          </CustomTooltip>
        </Box>
      </Box>
    </Box>
  );
};
export default TopBarNotificationList;
