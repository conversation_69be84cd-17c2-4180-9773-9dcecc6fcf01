/* eslint-disable react/jsx-props-no-spreading */
import IconButton from '@mui/material/IconButton';
import { makeStyles } from 'tss-react/mui';
import Button from '@mui/material/Button';
import Collapse from '@mui/material/Collapse';
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import KeyboardDoubleArrowDownRoundedIcon from '@mui/icons-material/KeyboardDoubleArrowDownRounded';

import { t } from 'i18next';
import CustomizedDatePicker from 'components/DatePicker/data-picker';
import { useSearchLetterContext } from '../../../../../contexts/pageContext/letters/searchContext';
import SearchIn from './components/searchIn';
import LetterTypes from './components/letterTypes';
import NumberLetter from './components/numberLetter';
import BodyLetter from './components/bodyLetter';
import SenderLetter from './components/senderLetter';
import RecipientLetter from './components/recipientLetter';
import Subject from './components/subject';
import CustomTooltip from '../../../../../components/tooltip';
import AWBox from '../../../../../components/AWComponents/AWBox';
import IncomingNumberLetter from './components/incoming-number-letter';
import Secretariats from './components/secretariats';
import Indicators from './components/indicators';

const useStyles = makeStyles()((theme, { isCollapsed }) => ({
  root: {
    border: '1px solid #D1D1D1',
    borderRadius: theme.spacing(1),
    padding: theme.spacing(2),
  },
  gridRoot: {
    marginTop: -16,
  },
  searchButton: {
    color: theme.palette.text.gray,
    borderRadius: theme.spacing(1),
    paddingLeft: 0,
  },
  arrowDownBtn: {
    background: '#f8f8f8',
    borderRadius: 8,
    border: '1px solid #d1d1d1',
    '&:hover': {
      borderRadius: 8,
    },
  },
  collapsRoot: {
    marginTop: isCollapsed ? theme.spacing(2) : 0,
  },
}));

const SearchLetterDesktop = () => {
  const {
    isCollapsed,
    setIsCollapsed,
    handleSearch,
    typeOfLetterValue,
    fromValue,
    setFromValue,
    toValue,
    setToValue,
    isSelectedForSearchSecretariats,
    loading,
  } = useSearchLetterContext();
  const { classes } = useStyles({ isCollapsed });

  const uiColumnSize = {
    number: 6,
    fromDate: 6,
    toDate: 6,
    incomingNumber: 6,
    body: 6,
    indicators: 6,
    secretariats: 6,
    sender: 6,
    receiver: 6,
  };

  return (
    <form className={classes.root} onSubmit={handleSearch}>
      <Grid container spacing={2} columns={16} classes={{ root: classes.gridRoot }}>
        <Grid item md={6} lg={5}>
          <AWBox>
            <Subject />
          </AWBox>
        </Grid>
        <>
          <Grid item md={5} lg={4}>
            <AWBox>
              <LetterTypes />
            </AWBox>
          </Grid>
          <Grid item md={5} lg={4}>
            <AWBox>
              <SearchIn />
            </AWBox>
          </Grid>
          <Grid item md={1} lg={1}>
            <AWBox>
              <CustomTooltip
                title={isCollapsed ? 'letter.list.hideMoreOptions' : 'letter.list.moreOptions'}
              >
                <IconButton
                  id="arrowDown"
                  onClick={() => setIsCollapsed((prev) => !prev)}
                  classes={{ root: classes.arrowDownBtn }}
                >
                  <KeyboardDoubleArrowDownRoundedIcon />
                </IconButton>
              </CustomTooltip>
            </AWBox>
          </Grid>
          <Grid item md={3} lg={2}>
            <AWBox>
              <Button type="submit" id="searchBtn" variant="contained" fullWidth disabled={loading}>
                {t('common.labels.search')}
              </Button>
            </AWBox>
          </Grid>
        </>
      </Grid>
      <Collapse in={isCollapsed} timeout="auto" classes={{ root: classes.collapsRoot }}>
        <Grid container spacing={2}>
          <Grid item md={uiColumnSize.body}>
            <BodyLetter />
          </Grid>
          <Grid item md={uiColumnSize.number}>
            <NumberLetter />
          </Grid>
          <Grid item md={uiColumnSize.fromDate}>
            <CustomizedDatePicker
              name="fromDate"
              id="fromDate"
              label={t('secretariats.labels.fromDate')}
              value={fromValue}
              onChange={(value) => setFromValue(value)}
              hideClose={false}
            />
          </Grid>
          <Grid item md={uiColumnSize.toDate}>
            <CustomizedDatePicker
              name="toDate"
              id="toDate"
              label={t('secretariats.labels.toDate')}
              value={toValue}
              onChange={(value) => setToValue(value)}
              hideClose={false}
            />
          </Grid>
          {typeOfLetterValue.value === 'incoming' && (
            <Grid item md={uiColumnSize.incomingNumber}>
              <IncomingNumberLetter />
            </Grid>
          )}
          <Grid item md={uiColumnSize.sender}>
            <SenderLetter />
          </Grid>
          <Grid item md={uiColumnSize.receiver}>
            <RecipientLetter />
          </Grid>
          {isSelectedForSearchSecretariats && (
            <>
              <Grid item md={uiColumnSize.secretariats}>
                <Secretariats />
              </Grid>
              <Grid item md={uiColumnSize.indicators}>
                <Indicators />
              </Grid>
            </>
          )}
        </Grid>
      </Collapse>
    </form>
  );
};

export default SearchLetterDesktop;
