import { ApiMethodDictionary } from 'api/v1/types/api-methods.type';

const methods: ApiMethodDictionary = {
  sendECE: {
    httpMethod: 'POST',
    defaultMethod: 'send-ece',
    checkStatus: false,
  },
  incomingEceMailsList: {
    httpMethod: 'GET',
  },
  outgoingEceMailsList: {
    httpMethod: 'GET',
  },
  eceMail: {
    httpMethod: 'GET',
  },
  convertMailToLetter: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'eceMails',
  },
  receipts: {
    httpMethod: 'POST',
    defaultMethod: 'receipts',
    checkStatus: false,
    showSnackbar: false,
  },
  eceReceiptMails: {
    httpMethod: 'GET',
  },
  eceReceiptMail: {
    httpMethod: 'GET',
  },
  retryEceReceipt: {
    httpMethod: 'POST',
    checkStatus: false,
  },
};

export default methods;
