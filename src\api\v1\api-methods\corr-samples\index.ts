import { ApiMethodDictionary } from 'api/v1/types/api-methods.type';

const methods: ApiMethodDictionary = {
  correspondenceSamplesList: {
    httpMethod: 'GET',
  },
  correspondenceSample: {
    httpMethod: 'GET',
  },
  createCorrespondenceSample: {
    httpMethod: 'POST',
    checkStatus: false,
  },
  deleteCorrespondenceSample: {
    httpMethod: 'DELETE',
    checkStatus: false,
    domainCheckStatus: 'corr-samples',
  },
  forwardNotesSamples: {
    httpMethod: 'GET',
  },
  createForwardNotesSample: {
    httpMethod: 'POST',
    showSnackbar: true,
    checkStatus: false,
  },
  deleteForwardNotesSamples: {
    httpMethod: 'DELETE',
    checkStatus: false,
    showSnackbar: true,
  },
};

export default methods;
