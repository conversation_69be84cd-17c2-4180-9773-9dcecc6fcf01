import React from 'react';

import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';

const useStyles = makeStyles()((theme) => ({
  textfield: {
    direction: 'ltr',
    margin: 0,
    marginTop: [theme.spacing(1), '!important'],
  },
}));

const FontSizeBehavior = ({ tokenProperties = {}, path, handleChangeTokenFontSize }) => {
  const { classes } = useStyles();
  const { fontSize } = tokenProperties;
  const { t } = useTranslation();

  return (
    <TextField
      value={fontSize ?? 14}
      id="token-font-size"
      fullWidth
      className={classes.textfield}
      label={t('behaviorToken.labels.fontSize')}
      onChange={(e) => handleChangeTokenFontSize(e, path)}
      type="number"
      variant="outlined"
      helperText={t('secretariats.labels.fontSizeRange')}
      InputProps={{
        startAdornment: (
          <InputAdornment style={{ marginTop: 0 }} position="start">
            px
          </InputAdornment>
        ),
      }}
    />
  );
};

export default FontSizeBehavior;
