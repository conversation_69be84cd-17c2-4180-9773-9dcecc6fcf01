import React from 'react';
import { fireEvent, waitFor, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { act } from 'react-dom/test-utils';
import '../../../i18n';
import authReducer, {
  loginByPasswordAsync,
  getLastLoginDataAsync,
} from '../../../pages/Auth/authSlice';
import { customRender } from '../../../mocks/render';
import Login from '../../../pages/Auth/Login';

beforeAll(() => jest.mock('../../../utils/snackbarUtils', () => jest.fn()));

const testLoginBehavior = (size) => {
  it('should render Login and redirect to /organizations on successful login', async () => {
    const { container, history, store } = customRender(
      <Login />,
      {
        reducerKey: 'auth',
        reducerValue: authReducer,
        size,
      },
      { initialEntries: ['/login'] },
    );

    const submitButton = container.querySelector('button[type="submit"]');
    const passwordInput = container.querySelector('input[name="password"]');
    const usernameInput = container.querySelector('input[name="username"]');

    await act(async () => userEvent.type(usernameInput, 'maryam'));
    await act(async () => userEvent.type(passwordInput, 'Qwer@1234'));

    act(() => {
      fireEvent.click(submitButton);

      // Dispatch the action with the expected data
      store.dispatch(
        loginByPasswordAsync.fulfilled({
          refresh_token: 'mock_token',
          data: { id: '1' },
        }),
        getLastLoginDataAsync.fulfilled('1'),
      );
    });

    // Wait for the action to complete and the redirection to occur
    await act(async () =>
      waitFor(() => {
        expect(history.location.pathname).toBe('/organizations');
      }),
    );

    setTimeout(() => {
      act(() => expect(screen.getByText(/آخرین ورود شما/i)).toBeInTheDocument());
    }, 10000);
  });

  it('should render Login and redirect to /recover-password on forget password', async () => {
    const { history, getByRole } = customRender(
      <Login />,
      {
        reducerKey: 'auth',
        reducerValue: authReducer,
        size,
      },
      { initialEntries: ['/login'] },
    );

    const forgetPasswordLink = getByRole('forget-password');

    await act(async () => userEvent.click(forgetPasswordLink));

    await act(async () =>
      waitFor(() => {
        expect(history.location.pathname).toContain('recover-password');
      }),
    );
  });

  it('handleClickShowPassword toggles showPassword state', () => {
    const { container, getByRole } = customRender(
      <Login />,
      {
        reducerKey: 'auth',
        reducerValue: authReducer,
        size,
      },
      { initialEntries: ['/login'] },
    );
    const showPasswordButton = getByRole('show-password-button');

    const passwordInput = container.querySelector('input[name="password"]');
    expect(passwordInput.type).toBe('password');

    fireEvent.click(showPasswordButton);

    expect(passwordInput.type).toBe('text');

    fireEvent.click(showPasswordButton);

    expect(passwordInput.type).toBe('password');
  });

  it('should render Login and show not found text if `refresh_token` dooes not exist', async () => {
    const { store } = customRender(
      <Login />,
      {
        reducerKey: 'auth',
        reducerValue: authReducer,
        size,
      },
      { initialEntries: ['/login'] },
    );

    store.dispatch(
      loginByPasswordAsync.fulfilled({
        data: {},
      }),
    );

    await act(async () =>
      waitFor(() => {
        expect(screen.getByText(/صحیح نمی باشد/i)).toBeInTheDocument();
      }),
    );
  });
};

describe('Login Component', () => {
  describe('Login Mobile Component', () => {
    testLoginBehavior('sm');
  });

  describe('Login Desktop Component', () => {
    testLoginBehavior('lg');
  });
});
