import React from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import DesktopRecoverPassword from './recover-password-success-desktop';
import TabletRecoverPassword from './recover-password-success-tablet';
import MobileRecoverPassword from './recover-password-success-mobile';

const RecoverPasswordSuccess = () => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));

  if (mobile) {
    return <MobileRecoverPassword isMobile />;
  }
  if (tablet) {
    return <TabletRecoverPassword />;
  }
  if (desktop) {
    return <DesktopRecoverPassword />;
  }
  return <></>;
};

export default RecoverPasswordSuccess;
