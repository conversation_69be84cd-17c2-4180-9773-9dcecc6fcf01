import React from 'react';

import { makeStyles } from 'tss-react/mui';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import ArrowBackIosRoundedIcon from '@mui/icons-material/ArrowBackIosRounded';
import ArrowForwarIosRoundedIcon from '@mui/icons-material/ArrowForwardIosRounded';

import AWSwipeableDrawer from 'components/AWComponents/AWSwipeableDrawer';
import { t } from 'i18next';
import AWBox from 'components/AWComponents/AWBox';
import AWButton from 'components/AWComponents/AWButton';
import AWDivider from 'components/AWComponents/AWDivider';
import { useLetter } from '../../../../../../../contexts/pageContext/letters/letterContext';
import LetterHistoryCard from './components/letter-history-card';
import { useLetterHistoryContext } from '../../../../../../../contexts/pageContext/letters/letter-history/letter-history-context';
import { momentDateUTC } from '../../../../../../../utils';
import Loading from '../../../../../../../components/Loading';
import DocumentsListMobile from './components/documnet-list-mobile';
import LetterForwardsFilter from './components/letter-forwards-filter';
import PrintableTable from './components/table-history-print-mode';

const useStyles = makeStyles()((theme) => ({
  fixed: {
    position: 'fixed',
    width: '100%',
    top: '64px',
    zIndex: '10',
    background: '#fff',
    padding: '18px 0 0',
    borderBottom: `3px solid transparent`,
    borderImage: `
    linear-gradient(to right, rgba(101, 90, 175, 0.5) 0%, rgba(101, 90, 175, 1) 33% 66%, rgba(101, 90, 175, 0.5) 100%)
    `,
    borderImageSlice: '1',
    '&:after': {
      content: '""',
      width: 12,
      height: 12,
      background: theme.palette.primary.main,
      borderRadius: 50,
      position: 'absolute',
      margin: 'auto',
      bottom: -7,
      left: 0,
      right: 0,
      zIndex: 999,
    },
  },
  topContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing(0, 1.5),
    marginBottom: theme.spacing(1.5),
  },
  arrowButton: {
    border: `1px solid ${theme.palette.background.light}`,
    color: theme.palette.primary.main,
    padding: theme.spacing(0.5),
  },
  root: {
    minWidth: '100vw',
    maxWidth: '100vw',
    height: 'calc(100vh - 330px)', //! this code should be changed also AWSwipeableDrawer and etc
    overflowY: 'auto',
    marginTop: '140px',
    // paddingBottom: '24px',
  },
  container: {
    display: 'flex',
    width: '100%',
    overflow: 'hidden',
  },
  currentDate: {
    fontSize: '17px',
    color: theme.palette.primary.main,
    fontWeight: 'bold',
  },
  current: {
    marginTop: theme.spacing(1),
    position: 'relative',
    width: '50%',
  },
  previous: {
    marginTop: theme.spacing(1),
    width: '25%',
    opacity: '.5',
  },
  next: {
    direction: 'ltr',
    marginTop: theme.spacing(1),
    width: '25%',
    opacity: '.5',
  },
  letterHistoryCard: {
    margin: theme.spacing(3, 0),
    marginBottom: theme.spacing(2),
  },
  filter: {
    direction: 'ltr',
    margin: theme.spacing(0, 0, 3, 0),
  },
  printButton: {
    direction: 'ltr',
    padding: theme.spacing(1, 2),
  },
}));

const MobileLetterHistory = () => {
  const { classes } = useStyles();
  const {
    isVisibleLetterHistoryDialog,
    toggleVisibleLetterHistoryDialog,
    letterHistoryLoading,
    letters,
  } = useLetter();

  const {
    mobileLetterHistorySegments,
    handlePrivousLetterHistory,
    handleNextLetterHistory,
    handleChangeTouchStartX,
    handleChangeTouchEndX,
    openDocListSidebar,
    handleCloseDocumentListSidebar,
    checkedLetterForwardsFilter,
    setCheckedLetterForwardsFilter,
    handlePrint,
    letterHistoryRecords,
  } = useLetterHistoryContext();

  const { subject, number } = letters?.selectedLetter?.data ?? {};

  const previousCard =
    Object.keys(mobileLetterHistorySegments[0]).length > 0
      ? mobileLetterHistorySegments[0][Object.keys(mobileLetterHistorySegments[0])[0]]
      : false;

  const currentCard =
    Object.keys(mobileLetterHistorySegments[1]).length > 0
      ? mobileLetterHistorySegments[1][Object.keys(mobileLetterHistorySegments[1])[0]]
      : false;

  const nextCard = Object.keys(mobileLetterHistorySegments[2]).length
    ? mobileLetterHistorySegments[2][Object.keys(mobileLetterHistorySegments[2])[0]]
    : false;

  return (
    <>
      <AWSwipeableDrawer
        isScrollableY={false}
        title={t('solutionHistory.labels.letter.history') ?? ''}
        type="titled"
        isFullHeight
        onClose={() => toggleVisibleLetterHistoryDialog(false)}
        open={isVisibleLetterHistoryDialog}
        id="mobile-letter-history-dialog"
        onTouchStart={handleChangeTouchStartX}
        onTouchEnd={(e) => handleChangeTouchEndX(e, mobileLetterHistorySegments[1])}
        onTouchMove={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <div className={classes.fixed}>
          <AWBox className={classes.filter}>
            {!letterHistoryLoading && (
              <LetterForwardsFilter
                checkedLetterForwardsFilter={checkedLetterForwardsFilter}
                setCheckedLetterForwardsFilter={setCheckedLetterForwardsFilter}
              />
            )}
          </AWBox>

          <div className={classes.topContainer}>
            <IconButton
              id="next-button"
              onClick={() => handleNextLetterHistory(mobileLetterHistorySegments[1])}
              classes={{ root: classes.arrowButton }}
              disabled={!nextCard}
            >
              <ArrowForwarIosRoundedIcon />
            </IconButton>

            <Typography id="current-date" className={classes.currentDate}>
              {Object.keys(mobileLetterHistorySegments[1]).length > 0 && !letterHistoryLoading
                ? momentDateUTC(Object.keys(mobileLetterHistorySegments[1])[0])
                : ''}
            </Typography>
            <IconButton
              id="previous-button"
              onClick={() => handlePrivousLetterHistory(mobileLetterHistorySegments[1])}
              classes={{ root: classes.arrowButton }}
              disabled={!previousCard}
            >
              <ArrowBackIosRoundedIcon />
            </IconButton>
          </div>
        </div>
        <div className={classes.root}>
          <div className={classes.container}>
            {letterHistoryLoading ? (
              <Loading />
            ) : (
              <>
                <div className={classes.next}>
                  {nextCard &&
                    nextCard.map((card: any) => (
                      <div className={classes.letterHistoryCard}>
                        <LetterHistoryCard item={card} key={card.id} />
                      </div>
                    ))}
                </div>
                <div className={classes.current}>
                  {currentCard &&
                    currentCard.map((card: any) => (
                      <div className={classes.letterHistoryCard}>
                        <LetterHistoryCard item={card} key={card.id} />
                      </div>
                    ))}
                </div>
                <div className={classes.previous}>
                  {previousCard &&
                    previousCard.map((card: any) => (
                      <div className={classes.letterHistoryCard}>
                        <LetterHistoryCard item={card} key={card.id} />
                      </div>
                    ))}
                </div>
                <DocumentsListMobile
                  open={openDocListSidebar.open}
                  onClose={handleCloseDocumentListSidebar}
                  data={openDocListSidebar.attachments}
                />
              </>
            )}
          </div>
        </div>
        {!letterHistoryLoading && (
          <>
            <AWDivider />
            <AWBox className={classes.printButton}>
              <AWButton variant="contained" onClick={handlePrint}>
                {t(`solutionHistory.labels.letter.history-print`)}
              </AWButton>
            </AWBox>
          </>
        )}
      </AWSwipeableDrawer>
      <div id="printableDiv">
        <PrintableTable
          data={letterHistoryRecords}
          historyType="letter"
          subject={subject}
          number={number}
        />
      </div>
    </>
  );
};

export default MobileLetterHistory;
