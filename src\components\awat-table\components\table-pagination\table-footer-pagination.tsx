import React from 'react';

import Pagination from '@mui/material/Pagination';
import { makeStyles } from 'tss-react/mui';
import { useAWTableContext } from '../../context/table-context';
import AWBox from '../../../AWComponents/AWBox';
import AWTableGoToPage from '../table-go-to-page/table-go-to-page';
import AWTablePageLimit from '../table-page-limit/table-page-limit';

const useStyles = makeStyles()((theme) => ({
  root: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    // background: '#F8F8F8',
    borderRadius: theme.spacing(1),
    margin: theme.spacing(1, 0),
  },
  item: {
    '& li': {
      '& button': {
        fontSize: 12,
        fontWeight: 700,
      },
      '&:first-child, &:last-child': {
        '& button': {
          borderRadius: 2,
        },
      },
    },
  },
}));

const AWTableFooterPagination = () => {
  const { pagination, loading, handleChangeLimit } = useAWTableContext();
  const { pageCount, currentPage, onChange, showLimit } = pagination;
  const { classes } = useStyles();

  return (
    <>
      <AWBox className={classes.root}>
        {pageCount > 1 && (
          <>
            <Pagination
              count={pageCount}
              onChange={onChange}
              page={currentPage}
              color="primary"
              variant="outlined"
              classes={{ ul: classes.item }}
              disabled={loading}
            />
            <AWTableGoToPage />
          </>
        )}
        <AWTablePageLimit />
      </AWBox>
    </>
  );
};

export default AWTableFooterPagination;
