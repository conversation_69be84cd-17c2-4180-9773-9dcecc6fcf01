import { useEffect, useRef, useState } from 'react';

import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { NotificationsContext } from 'contexts/notificationContext';
import {
  notificationsAsync as notificationsAsync_,
  readAllNotificationsAsync as readAllNotificationsAsync_,
  readNotificationAsync as readNotificationAsync_,
  unReadNotificationAsync as unReadNotificationAsync_,
  notificationsReset as notificationsReset_,
  getUnReadNotificationsAsync as getUnReadNotificationsAsync_,
  filterNotificationToUnread as filterNotificationToUnread_,
} from 'pages/notifications/notificationSlice';
import { useUser } from 'contexts/userContext';
import { withManagementCenterInitializer } from 'pages/management-center/with-management-center-initializer';
import NotificationsBase from './notifications-base';

const NotificationsComponent = ({
  notification,
  mobile,
  notificationsAsync,
  getUnReadNotificationsAsync,
  readAllNotificationsAsync,
  readNotificationAsync,
  unReadNotificationAsync,
  notificationsReset,
  filterNotificationToUnread,
}) => {
  const { signedInUser } = useUser();
  const [loadingAllNotifiaction, setLoadingAllNotifiaction] = useState(false);
  const [loadingReadAll, setLoadingReadAll] = useState(false);
  const [hasMoreNotification, setHasMoreNotification] = useState(true);
  const offset = useRef(0);
  const [limit] = useState(10);
  const [isAllNotificationMode, setIsAllNotificationMode] = useState(false);
  const fetchRef = useRef(null);

  useEffect(() => {
    refreshNotifications();
  }, [isAllNotificationMode]);

  const getNotificationsAsync = (options) => {
    if (isAllNotificationMode) {
      return getUnReadNotificationsAsync(options);
    }
    return notificationsAsync(options);
  };

  const getNotifications = (offset1) => {
    if (!signedInUser?.id) return;
    const options = {
      path: `${signedInUser?.id}`,
      queryObject: {
        limit,
        offset: offset1 ?? offset?.current,
        unread: true,
      },
    };
    setLoadingAllNotifiaction(true);
    fetchRef.current = getNotificationsAsync(options);
    fetchRef.current
      .unwrap()
      ?.then(() => {
        offset.current += limit;
      })
      ?.finally(() => {
        setLoadingAllNotifiaction(false);
      });
  };

  const fetchMoreNotifications = () => {
    const { notifications } = notification;
    if (notifications?.data?.length >= notifications?.meta?.pagination?.total) {
      setHasMoreNotification(false);
      return;
    }
    getNotifications();
  };

  const refreshNotifications = () => {
    fetchRef?.current?.abort();
    offset.current = 0;
    setHasMoreNotification(true);
    notificationsReset();
    getNotifications(0);
  };

  const readAllNotifications = () => {
    const options = {
      userId: signedInUser?.id,
    };
    setLoadingReadAll(true);
    const result = readAllNotificationsAsync(options);
    result
      .unwrap()
      ?.then(() => {
        notificationsReset();
        getNotifications(0);
      })
      ?.finally(() => {
        setLoadingReadAll(false);
      });
  };

  const unReadNotification = (id) => {
    const options = {
      path: `${id}/unread`,
      id,
    };
    const result = unReadNotificationAsync(options);
    result
      .unwrap()
      ?.then(() => {
        if (isAllNotificationMode) {
          getNotifications();
        }
      })
      ?.finally(() => {});
  };

  const readNotification = (id) => {
    const options = {
      path: `${id}/read`,
      id,
    };
    setLoadingReadAll(true);
    const result = readNotificationAsync(options);
    result
      .unwrap()
      ?.then(() => {
        if (isAllNotificationMode) {
          filterNotificationToUnread();
          getNotifications();
        }
      })
      ?.finally(() => {});
  };

  return (
    <NotificationsContext.Provider
      value={{
        mobile,
        notification,
        fetchMoreNotifications,
        hasMoreNotification,
        loadingAllNotifiaction,
        readAllNotifications,
        readNotification,
        unReadNotification,
        refreshNotifications,
        isAllNotificationMode,
        setIsAllNotificationMode,
      }}
    >
      <NotificationsBase />
    </NotificationsContext.Provider>
  );
};
NotificationsComponent.propTypes = {
  mobile: PropTypes.bool,
  notification: PropTypes.shape({
    notifications: PropTypes.shape({
      meta: PropTypes.shape({
        pagination: PropTypes.shape({
          total: PropTypes.number,
        }),
      }),
      data: PropTypes.arrayOf(PropTypes.shape({})),
    }),
  }),
  notificationsAsync: PropTypes.func.isRequired,
  getUnReadNotificationsAsync: PropTypes.func.isRequired,
  readAllNotificationsAsync: PropTypes.func.isRequired,
  readNotificationAsync: PropTypes.func.isRequired,
  unReadNotificationAsync: PropTypes.func.isRequired,
  notificationsReset: PropTypes.func.isRequired,
  filterNotificationToUnread: PropTypes.func.isRequired,
};
NotificationsComponent.defaultProps = {
  mobile: false,
  notification: {},
};

const mapStateToProps = (state) => {
  const { notification } = state;
  return {
    notification,
  };
};

export default withManagementCenterInitializer(
  connect(mapStateToProps, {
    notificationsAsync: notificationsAsync_,
    getUnReadNotificationsAsync: getUnReadNotificationsAsync_,
    readAllNotificationsAsync: readAllNotificationsAsync_,
    readNotificationAsync: readNotificationAsync_,
    unReadNotificationAsync: unReadNotificationAsync_,
    notificationsReset: notificationsReset_,
    filterNotificationToUnread: filterNotificationToUnread_,
  })(NotificationsComponent),
);
