import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import Services3 from 'api/v1/services/index';

const apiVersion = '/api/v1/';

const initialState = {
  secretariatsList: {
    status: 'idle',
    data: null,
  },
  createSecretariat: {
    status: 'idle',
    data: null,
    success: null,
  },
  editSecretariat: {
    status: 'idle',
    data: null,
    success: null,
  },
  indicatorsList: {
    status: 'idle',
    data: null,
    success: null,
  },
  indicator: {
    status: 'idle',
    data: null,
    success: null,
  },
  lastSerialNumberIndicator: {
    status: 'idle',
    data: null,
  },
  letterLayoutsList: {
    status: 'idle',
    data: null,
  },
  createIndicator: {
    status: 'idle',
    data: null,
    success: null,
  },
  editIndicator: {
    status: 'idle',
    data: null,
    success: null,
  },
  disableIndicator: {
    status: 'idle',
    data: null,
  },
  enableIndicator: {
    status: 'idle',
    data: null,
  },
  createLetterLayout: {
    status: 'idle',
    data: null,
    success: null,
  },
  editLetterLayout: {
    status: 'idle',
    data: null,
    success: null,
  },
  deleteLetterLayout: {
    status: 'idle',
    data: null,
    success: null,
  },
  eceMailConfig: {
    status: 'idle',
    data: null,
    success: null,
  },
  editEceMailConfig: {
    status: 'idle',
    data: null,
    success: null,
  },
  testEceMailConfig: {
    status: 'idle',
    data: null,
    errors: null,
    success: null,
  },
  eceLettersRefresh: {
    status: 'idle',
    data: null,
    timeRefresh: null,
  },
};

export const getEceLettersRefreshAsync = createAsyncThunk(
  'secretariats/eceLettersRefresh',
  async ({ secretariatId }) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'eceRefresh',
      path: `${secretariatId}/ece-mail-config/force-fetch`,
    };
    const response = await Services3({ options: opts, data: secretariatId });
    return response.data;
  },
);

export const testEceMailConfigAsync = createAsyncThunk(
  'secretariats/testEceMailConfig',
  async ({ secretariatId, data }) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'testEceMailConfig',
      path: `${secretariatId}/ece-mail-config/validate`,
    };

    const response = await Services3({ options: opts, data });
    return response;
  },
);

export const editEceMailConfigAsync = createAsyncThunk(
  'secretariats/editEceMailConfig',
  async ({ secretariatId, data, successfulMessage }) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'editEceMailConfig',
      path: `${secretariatId}/ece-mail-config`,
    };

    const response = await Services3({ options: opts, data });
    return response;
  },
);

export const eceMailConfigAsync = createAsyncThunk(
  'secretariats/eceMailConfig',
  async (secretariatId) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'eceMailConfig',
      path: `${secretariatId}/ece-mail-config`,
    };
    const response = await Services3({ options: opts });
    return response;
  },
  {
    condition: (arg, api) => {
      if (api.getState().secretariats.eceMailConfig.status === 'loading') {
        return false;
      }
      return true;
    },
  },
);

export const createSecretariatAsync = createAsyncThunk(
  'secretariats/createSecretariat',
  async (options) => {
    const opts = { apiVersion, domainRoot: 'secretariats', method: 'secretariats', path: '' };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
);

export const getSecretariatsAsync = createAsyncThunk(
  'secretariats/secretariatsList',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'organization',
      path: options.path,
    };
    const response = await Services3({ options: opts });
    return response;
  },
  {
    condition: (arg, api) => {
      if (
        api.getState().secretariats.secretariatsList.status === 'loading' ||
        api.getState().secretariats.secretariatsList.data
      ) {
        return false;
      }
      return true;
    },
  },
);

export const editSecretariatAsync = createAsyncThunk(
  'secretariats/editSecretariat',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'editSecretariats',
    };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
);

export const getIndicatorsAsync = createAsyncThunk(
  'secretariats/indicatorsList',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'indicators',
      method: 'secretariat',
      path: options.path,
    };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
  {
    condition: (arg, api) => {
      if (
        api.getState().secretariats.indicatorsList.status === 'loading' ||
        api.getState().secretariats.indicatorsList.data
      ) {
        return false;
      }
      return true;
    },
  },
);

export const getIndicatorAsync = createAsyncThunk(
  'secretariats/indicator',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'indicators',
      method: 'indicator',
      path: options.path,
    };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
  {
    condition: (arg, api) => {
      if (
        api.getState().secretariats.indicator.status === 'loading' ||
        api.getState().secretariats.indicator.data
      ) {
        return false;
      }
      return true;
    },
  },
);

export const getLastSerialNumberIndicatorAsync = createAsyncThunk(
  'secretariats/lastSerialNumberIndicator',
  async ({ secretariatId, indicatorId }) => {
    const opts = {
      apiVersion,
      domainRoot: 'indicators',
      method: 'lastSerialNumberIndicator',
      path: `${indicatorId}/lastSerialNumber?secretariatId=${secretariatId}`,
    };
    const response = await Services3({ options: opts });
    return response;
  },
);

export const getLetterLayoutsAsync = createAsyncThunk(
  'secretariats/letterLayoutsList',
  async (options) => {
    const { secretariatId } = options;
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'letterLayouts',
      path: `${secretariatId}/letter-layouts`,
    };
    const response = await Services3({ options: opts });
    return response;
  },
  {
    condition: (arg, api) => {
      if (
        api.getState().secretariats.letterLayoutsList.status === 'loading' ||
        api.getState().secretariats.letterLayoutsList.data
      ) {
        return false;
      }
      return true;
    },
  },
);

export const createIndicatorAsync = createAsyncThunk(
  'secretariats/createIndicator',
  async (options) => {
    const opts = { apiVersion, domainRoot: 'indicators', method: 'indicators' };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
);

export const editIndicatorAsync = createAsyncThunk(
  'secretariats/editIndicator',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'indicators',
      method: 'indicatorEdit',
    };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
);

export const disableIndicatorAsync = createAsyncThunk(
  'secretariats/disableIndicator',
  async ({ id, isDisabled, ...other }) => {
    const opts = {
      apiVersion,
      domainRoot: 'indicators',
      method: 'indicatorStatusEdit',
      path: 'disable',
    };
    const response = await Services3({ options: opts, data: { id, isDisabled, ...other } });
    return response;
  },
);

export const enableIndicatorAsync = createAsyncThunk(
  'secretariats/enableIndicator',
  async ({ id, isDisabled, ...other }) => {
    const opts = {
      apiVersion,
      domainRoot: 'indicators',
      method: 'indicatorStatusEdit',
      path: 'enable',
    };
    const response = await Services3({ options: opts, data: { id, isDisabled, ...other } });
    return response;
  },
);

export const createLetterLayoutAsync = createAsyncThunk(
  'secretariats/createLetterLayout',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'createLetterLayout',
    };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
);

export const editLetterLayoutAsync = createAsyncThunk(
  'secretariats/editLetterLayout',
  async (options) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'editLetterLayout',
    };
    const response = await Services3({ options: opts, data: options.data });
    return response;
  },
);

export const deleteLetterLayoutAsync = createAsyncThunk(
  'secretariats/deleteLetterLayout',
  async ({ secretariatId, layoutId }) => {
    const opts = {
      apiVersion,
      domainRoot: 'secretariats',
      method: 'deleteLetterLayout',
      path: `${secretariatId}/letter-layouts/${layoutId}`,
    };
    const response = await Services3({ options: opts });
    return response;
  },
);

export const secretariatsSlice = createSlice({
  name: 'secretariats',
  initialState,
  reducers: {
    eceLettersRefreshReset: (state) => {
      const st = state;
      st.eceLettersRefresh.timeRefresh = initialState.eceLettersRefresh.timeRefresh;
      localStorage.removeItem('ece-letters-refresh');
    },
    createSecretariatReset: (state) => {
      const st = state;
      st.createSecretariat = initialState.createSecretariat;
    },
    getSecretariatsReset: (state) => {
      const st = state;
      st.secretariatsList = initialState.secretariatsList;
    },
    eceMailConfigReset: (state) => {
      const st = state;
      st.secretariatsList = initialState.eceMailConfig;
    },
    editEceMailConfigReset: (state) => {
      const st = state;
      st.secretariatsList = initialState.editEceMailConfig;
    },
    testEceMailConfigReset: (state) => {
      const st = state;
      st.secretariatsList = initialState.testEceMailConfig;
    },
    getIndicatorsReset: (state) => {
      const st = state;
      st.indicatorsList = initialState.indicatorsList;
    },
    getLetterLayoutsReset: (state) => {
      const st = state;
      st.letterLayoutsList = initialState.letterLayoutsList;
    },
    createLetterLayoutsReset: (state) => {
      const st = state;
      st.createLetterLayout = initialState.createLetterLayout;
    },
    createIndicatorReset: (state) => {
      const st = state;
      st.createIndicator = initialState.createIndicator;
    },
    getIndicatorReset: (state) => {
      const st = state;
      st.indicator = initialState.indicator;
    },
    getLastSerialNumberIndicatorReset: (state) => {
      const st = state;
      st.lastSerialNumberIndicator = initialState.lastSerialNumberIndicator;
    },
    editLetterLayoutReset: (state) => {
      const st = state;
      st.editLetterLayout = initialState.editLetterLayout;
    },
    deleteLetterLayoutAsync: (state) => {
      const st = state;
      st.deleteLetterLayout = initialState.deleteLetterLayout;
    },
  },
  extraReducers: (builder) => {
    builder
      // eceRefresh
      .addCase(getEceLettersRefreshAsync.pending, (state) => {
        const st = state;
        st.eceLettersRefresh.status = 'pending';
        const now = Date.now();
        st.eceLettersRefresh.timeRefresh = now;
        localStorage.setItem('ece-letters-refresh', now);
      })
      .addCase(getEceLettersRefreshAsync.fulfilled, (state, actions) => {
        const st = state;
        st.eceLettersRefresh.status = 'success';
        st.eceLettersRefresh.data = actions.payload.data;
        st.eceLettersRefresh.timeRefresh = Date.now();
      })
      .addCase(getEceLettersRefreshAsync.rejected, (state, actions) => {
        const st = state;
        st.eceLettersRefresh.status = 'idle';
        st.eceLettersRefresh.data = { success: false };
      })
      // createSecretariat
      .addCase(createSecretariatAsync.pending, (state) => {
        const st = state;
        st.createSecretariat.status = 'loading';
      })
      .addCase(createSecretariatAsync.fulfilled, (state, action) => {
        const st = state;
        st.createSecretariat.status = 'idle';
        st.createSecretariat.data = action.payload.data;
        st.createSecretariat.success = action.payload.success;
      })
      // getSecretariats
      .addCase(getSecretariatsAsync.pending, (state) => {
        const st = state;
        st.secretariatsList.status = 'loading';
      })
      .addCase(getSecretariatsAsync.fulfilled, (state, action) => {
        const st = state;
        st.secretariatsList.status = 'idle';
        st.secretariatsList.data = action.payload.data;
      })
      .addCase(getSecretariatsAsync.rejected, (state) => {
        const st = state;
        st.secretariatsList = initialState.secretariatsList;
      })
      // editSecretariat
      .addCase(editSecretariatAsync.pending, (state) => {
        const st = state;
        st.editSecretariat.status = 'loading';
      })
      .addCase(editSecretariatAsync.fulfilled, (state, action) => {
        const st = state;
        st.editSecretariat.status = 'idle';
        st.editSecretariat.data = action.payload.data;
        st.editSecretariat.success = action.payload.success;
      })
      // getIndicators
      .addCase(getIndicatorsAsync.pending, (state) => {
        const st = state;
        st.indicatorsList.status = 'loading';
      })
      .addCase(getIndicatorsAsync.fulfilled, (state, action) => {
        const st = state;
        st.indicatorsList.status = 'idle';
        st.indicatorsList.data = action.payload.data;
        st.indicatorsList.success = action.payload.success;
      })
      // getIndicator
      .addCase(getIndicatorAsync.pending, (state) => {
        const st = state;
        st.indicator.status = 'loading';
      })
      .addCase(getIndicatorAsync.fulfilled, (state, action) => {
        const st = state;
        st.indicator.status = 'idle';
        st.indicator.data = action.payload.data;
        st.indicator.success = action.payload.success;
      })
      // getLastSerialNumberIndicator
      .addCase(getLastSerialNumberIndicatorAsync.pending, (state) => {
        const st = state;
        st.lastSerialNumberIndicator.status = 'loading';
      })
      .addCase(getLastSerialNumberIndicatorAsync.fulfilled, (state, action) => {
        const st = state;
        st.lastSerialNumberIndicator.status = 'idle';
        st.lastSerialNumberIndicator.data = action.payload.data;
      })
      // getLetterLayouts
      .addCase(getLetterLayoutsAsync.pending, (state) => {
        const st = state;
        st.letterLayoutsList.status = 'loading';
      })
      .addCase(getLetterLayoutsAsync.fulfilled, (state, action) => {
        const st = state;
        st.letterLayoutsList.status = 'idle';
        st.letterLayoutsList.data = action.payload.data;
        st.letterLayoutsList.success = action.payload.success;
      })
      // createIndicator
      .addCase(createIndicatorAsync.pending, (state) => {
        const st = state;
        st.createIndicator.status = 'loading';
      })
      .addCase(createIndicatorAsync.fulfilled, (state, action) => {
        const st = state;
        st.createIndicator.status = 'idle';
        st.createIndicator.data = action.payload.data;
        st.createIndicator.meta = action.payload.meta;
        st.createIndicator.success = action.payload.success;
      })
      // editIndicator
      .addCase(editIndicatorAsync.pending, (state) => {
        const st = state;
        st.editIndicator.status = 'loading';
      })
      .addCase(editIndicatorAsync.fulfilled, (state, action) => {
        const st = state;
        st.editIndicator.status = 'idle';
        st.editIndicator.data = action.payload.data;
        st.editIndicator.success = action.payload.success;
      })
      .addCase(enableIndicatorAsync.pending, (state) => {
        const st = state;
        st.enableIndicator.status = 'loading';
      })
      .addCase(enableIndicatorAsync.fulfilled, (state, action) => {
        const st = state;
        st.enableIndicator.status = 'idle';
        st.enableIndicator.data = action.payload.data;
      })
      .addCase(enableIndicatorAsync.rejected, (state, action) => {
        const st = state;
        st.enableIndicator.status = 'idle';
        st.enableIndicator.data = { success: false };
      })
      .addCase(disableIndicatorAsync.pending, (state) => {
        const st = state;
        st.disableIndicator.status = 'loading';
      })
      .addCase(disableIndicatorAsync.fulfilled, (state, action) => {
        const st = state;
        st.disableIndicator.status = 'idle';
        st.disableIndicator.data = action.payload.data;
      })
      .addCase(disableIndicatorAsync.rejected, (state, action) => {
        const st = state;
        st.disableIndicator.status = 'idle';
        st.disableIndicator.data = { success: false };
      })
      // eceMailConfig
      .addCase(eceMailConfigAsync.pending, (state) => {
        const st = state;
        st.eceMailConfig.status = 'loading';
      })
      .addCase(eceMailConfigAsync.fulfilled, (state, action) => {
        const st = state;
        st.eceMailConfig.status = 'idle';
        st.eceMailConfig.data = action.payload.data;
        st.eceMailConfig.success = action.payload.success;
      })
      .addCase(eceMailConfigAsync.rejected, (state) => {
        const st = state;
        st.eceMailConfig.status = 'idle';
        st.eceMailConfig.data = null;
      })
      // editEceMailConfig
      .addCase(editEceMailConfigAsync.pending, (state) => {
        const st = state;
        st.editEceMailConfig.status = 'loading';
      })
      .addCase(editEceMailConfigAsync.fulfilled, (state, action) => {
        const st = state;
        st.editEceMailConfig.status = 'idle';
        st.editEceMailConfig.data = action.payload.data;
        st.editEceMailConfig.success = action.payload.success;
      })
      .addCase(editEceMailConfigAsync.rejected, (state) => {
        const st = state;
        st.editEceMailConfig.status = 'idle';
        st.editEceMailConfig.data = null;
      })
      // testEceMailConfig
      .addCase(testEceMailConfigAsync.pending, (state) => {
        const st = state;
        st.testEceMailConfig.status = 'loading';
      })
      .addCase(testEceMailConfigAsync.fulfilled, (state, action) => {
        const st = state;
        st.testEceMailConfig.status = 'idle';
        st.testEceMailConfig.data = action.payload;
        st.testEceMailConfig.success = action.payload.success;
      })
      .addCase(testEceMailConfigAsync.rejected, (state) => {
        const st = state;
        st.testEceMailConfig.status = 'idle';
        st.testEceMailConfig.data = null;
      })
      // createLetterLayout
      .addCase(createLetterLayoutAsync.pending, (state) => {
        const st = state;
        st.createLetterLayout.status = 'loading';
      })
      .addCase(createLetterLayoutAsync.fulfilled, (state, action) => {
        const st = state;
        st.createLetterLayout.status = 'idle';
        st.createLetterLayout.data = action.payload.data;
        st.createLetterLayout.success = action.payload.success;
      })
      // editLetterLayout
      .addCase(editLetterLayoutAsync.pending, (state) => {
        const st = state;
        st.editLetterLayout.status = 'loading';
      })
      .addCase(editLetterLayoutAsync.fulfilled, (state, action) => {
        const st = state;
        st.editLetterLayout.status = 'idle';
        st.editLetterLayout.data = action.payload.data;
        st.editLetterLayout.success = action.payload.success;
      })
      .addCase(editLetterLayoutAsync.rejected, (state, action) => {
        const st = state;
        st.editLetterLayout.status = 'idle';
        st.editLetterLayout.data = { success: false };
      })
      // deleteLetter
      .addCase(deleteLetterLayoutAsync.pending, (state) => {
        const st = state;
        st.deleteLetterLayout.status = 'loading';
      })
      .addCase(deleteLetterLayoutAsync.fulfilled, (state, action) => {
        const st = state;
        st.deleteLetterLayout.status = 'idle';
        st.deleteLetterLayout.data = action.payload.data;
        st.deleteLetterLayout.success = action.payload.success;
      })
      .addCase(deleteLetterLayoutAsync.rejected, (state, action) => {
        const st = state;
        st.deleteLetterLayout.status = 'idle';
        st.deleteLetterLayout.data = { success: false };
      });
  },
});

export const {
  createSecretariatReset,
  createLetterLayoutsReset,
  getSecretariatsReset,
  getLetterLayoutsReset,
  getIndicatorsReset,
  createIndicatorReset,
  getIndicatorReset,
  getLastSerialNumberIndicatorReset,
  editLetterLayoutReset,
  editEceMailConfigReset,
  eceMailConfigReset,
  testEceMailConfigReset,
  eceLettersRefreshReset,
} = secretariatsSlice.actions;

export default secretariatsSlice.reducer;
