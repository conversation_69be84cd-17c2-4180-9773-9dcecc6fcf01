import React from 'react';

import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';

import AWBox from '../../../../../../../components/AWComponents/AWBox';
import AWTextField from '../../../../../../../components/AWComponents/AWTextField';
import AWTypography from '../../../../../../../components/AWComponents/AWTypography';
import { useLetter } from '../../../../../../../contexts/pageContext/letters/letterContext';

const useStyles = makeStyles()((theme) => ({
  label: {
    fontSize: '14px',
    fontWeight: 700,
    color: `${theme.palette.text.dark} !important`,
    margin: theme.spacing(1, 0),
  },
}));

const EnterECEEmail = () => {
  const { t } = useTranslation();
  const { classes } = useStyles();
  const { eceEmailRecipient, onChangeEceEmailRecipient, ECEEmailRecipientErrorText } = useLetter();

  return (
    <AWBox>
      <AWTypography variant="body1" className={classes.label}>
        {t('correspondence.letters.labels.enter-ece-email-recipient')}
      </AWTypography>
      <AWTextField
        fullWidth
        value={eceEmailRecipient}
        onChange={onChangeEceEmailRecipient}
        error={ECEEmailRecipientErrorText}
        helperText={ECEEmailRecipientErrorText && t('correspondence.letters.labels.correct-email')}
        placeholder={`${t('correspondence.letters.labels.ece-email-recipient')} *`}
      />
    </AWBox>
  );
};

export default EnterECEEmail;
