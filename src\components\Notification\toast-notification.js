import React, { useState, useCallback, useEffect, useLayoutEffect } from 'react';
import { makeStyles } from 'tss-react/mui';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardActions from '@mui/material/CardActions';
import NotificationsIcon from '@mui/icons-material/Notifications';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import CloseIcon from '@mui/icons-material/Close';
import DoneIcon from '@mui/icons-material/Done';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { useTranslation } from 'react-i18next';
import AWTooltip from 'components/AWComponents/AWTooltip';
import notificationTemplateConverter from './notification-template-converter';
import CustomTooltip from '../tooltip';

const useStyles = makeStyles()((theme) => ({
  // root: {
  //   [theme.breakpoints.up('sm')]: {
  //     minWidth: '344px !important',
  //   },
  // },
  notificationCounter: {
    fontSize: '12px',
    background: '#1f67d3',
    color: '#fff',
    width: 'fit-content',
    padding: theme.spacing(0.5, 1.5),
    borderRadius: theme.spacing(0.5, 0.5, 0.0),
  },
  card: {
    // backgroundColor: '#fddc6c',
    minWidth: '450px',
    maxWidth: '450px',
    padding: theme.spacing(0, 1.5),
    boxShadow: '0 1px 10px 0 rgb(0 0 0 / 10%), 0 2px 15px 0 rgb(0 0 0 / 5%)',
  },
  typography: {
    fontWeight: 'bold',
  },
  actionRoot: {
    padding: theme.spacing(1, 0, 1),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  title: {
    display: 'flex',
    alignItems: 'center',
    // justifyContent: 'center',
    '& p': {
      fontSize: '14px',
      // whiteSpace: 'nowrap',
    },
    '& svg': {
      color: '#1f67d3',
      marginLeft: theme.spacing(0.5),
    },
  },
  toastDescription: {
    marginBottom: theme.spacing(1.25),
  },
  description: {
    fontSize: '14px',
    // textAlign: 'justify',
    '& p': {
      lineHeight: '25px !important',
      width: '100%',
      // lineHeight: '1.5em !important',
      // height: '50px',
      // overflow: 'hidden',
      // whiteSpace: 'nowrap',
      // textOverflow: 'ellipsis',
      '& a': {
        color: theme.palette.text.link,
        fontWeight: 'bold',
      },
    },
  },
  wrapDescription: {
    // '& p': {
    display: '-webkit-box',
    WebkitLineClamp: 2,
    WebkitBoxOrient: 'vertical',
    overflow: 'hidden',
    // },
  },
  icons: {
    display: 'flex',
    alignItems: 'center',
    zIndex: 15000,
    '& button:nth-child(1)': {
      zIndex: 15000,
      color: '#f29423',
      padding: theme.spacing(0.5),
      '& svg': {
        borderRadius: 50,
        border: '2px solid #f29423',
        // transition: 'border 500ms ease-out',
        // '&:hover': {
        //   border: '2px solid #f29423',
        // },
      },
    },
    '& button:nth-child(2)': {
      zIndex: 15000,
      color: '#3fc063',
      padding: theme.spacing(0.5),
      '& svg': {
        borderRadius: 50,
        border: '2px solid #3fc063',
      },
    },
  },
  expand: {
    padding: '8px 8px',
    transform: 'rotate(0deg)',
    transition: theme.transitions.create('transform', {
      duration: theme.transitions.duration.shortest,
    }),
  },
  expandOpen: {
    transform: 'rotate(180deg)',
  },
  collapse: {
    padding: 16,
  },
  button: {
    float: 'left',
  },
}));

const ToastNotification = (props) => {
  const { classes, cx } = useStyles();
  const [expanded, setExpanded] = useState(false);
  const [isWarp, setIsWrap] = useState(false);
  const [component, setComponent] = useState({ title: '', description: '' });
  const { t } = useTranslation();
  const { data, readNotification, toastProps } = props;

  const handleExpandClick = useCallback(() => {
    setExpanded((oldExpanded) => !oldExpanded);
  }, []);

  const handleReadNotification = () => {
    try {
      const id = data && data[0]?._id;
      const options = {
        path: `${id}/read`,
      };
      id && readNotification && readNotification(options);
    } catch (e) {
      // console.log(e);
    }
    toastProps?.deleteToast();
  };

  useEffect(() => {
    data[0]?.template && setComponent(notificationTemplateConverter(data[0]));
  }, [data]);

  useLayoutEffect(() => {
    setIsWrap(document.getElementById('toast-description')?.clientHeight > 50);
  }, [document.getElementById('toast-description')]);

  return (
    <>
      {data && data?.length > 1 && (
        <Typography variant="subtitle2" className={classes.notificationCounter}>
          + {data.length - 1} اعلان جدید
        </Typography>
      )}
      <Card className={classes.card} id="toast-notification" onClick={(e) => e.stopPropagation()}>
        <CardActions classes={{ root: classes.actionRoot }}>
          <div className={classes.title}>
            <NotificationsIcon />
            {component.title !== '' && (
              <Typography
                variant="subtitle2"
                className={classes.typography}
                dangerouslySetInnerHTML={{
                  __html: component?.title,
                }}
              />
            )}
          </div>
          <div className={classes.icons}>
            <AWTooltip title={t('tooltip.notification.close')}>
              <IconButton id="close-notification" onClick={(e) => toastProps?.deleteToast()}>
                <CloseIcon />
              </IconButton>
            </AWTooltip>
            <AWTooltip title={t('tooltip.notification.read')}>
              <IconButton id="read-notification" onClick={handleReadNotification}>
                <DoneIcon />
              </IconButton>
            </AWTooltip>
          </div>
        </CardActions>
        <div id="toast-description" className={classes.toastDescription}>
          {component.description !== '' && (
            <Typography
              variant="subtitle2"
              className={cx(
                classes.description,
                isWarp && !expanded ? classes.wrapDescription : '',
              )}
              dangerouslySetInnerHTML={{ __html: component?.description }}
            />
          )}
        </div>
        {isWarp && (
          <div className={classes.button}>
            {/* <span>.....</span> */}
            {expanded ? (
              <Button onClick={handleExpandClick} endIcon={<KeyboardArrowUpIcon />}>
                کمتر
              </Button>
            ) : (
              <Button onClick={handleExpandClick} endIcon={<KeyboardArrowDownIcon />}>
                بیشتر
              </Button>
            )}
          </div>
        )}
      </Card>
    </>
  );
};

export default ToastNotification;
