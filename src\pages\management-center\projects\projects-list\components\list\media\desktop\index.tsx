import { makeStyles } from 'tss-react/mui';
import { IColumnsRenderer } from 'components/awat-table/interfaces';
import AWTable from 'components/awat-table';
import { useTranslation } from 'react-i18next';
import AWTooltip from '../../../../../../../../components/AWComponents/AWTooltip';
import { useProjectsListContext } from '../../../../context';
import { MemberType } from '../../../../type';
import { userInfo } from '../../../../../../../../utils';

const useStyles = makeStyles()({
  content: {
    display: 'flex',
    flexDirection: 'row',
  },
  tooltip: {
    whiteSpace: 'pre-wrap',
    backgroundColor: '#000',
    marginTop: 'unset !important',
  },
  ellipsisText: {
    direction: 'rtl',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: 200,
    display: 'block',
    textAlign: 'right',
  },
});

const ProjectListDesktop: React.FC = () => {
  const columns: string[] = ['title', 'owner', 'members'];

  const {
    dataList,
    meta,
    handlePrevPage,
    loading,
    changePage,
    handleNextPage,
    reOrderMembers,
    handleSelectProject,
    handleChangeLimit,
  } = useProjectsListContext();
  const { t } = useTranslation();
  const { classes } = useStyles();

  const columnsRenderer: IColumnsRenderer = {
    title: {
      head: t('projects.labels.title-project'),
      renderCell: ({ title }) => <span className={classes.ellipsisText}>{title}</span>,
    },
    owner: {
      head: t('projects.labels.project-owner'),
      renderCell: ({ owner }) => (
        <span className={classes.ellipsisText}>{userInfo(owner.user, owner.position)}</span>
      ),
    },
    members: {
      head: t('projects.labels.project-member'),
      renderCell: ({ members, owner }) => {
        const reOrderedList = reOrderMembers(members, owner);
        const { user, position } = reOrderedList[0];

        return (
          <AWTooltip
            title={reOrderedList.map((member: MemberType) => (
              <span className={classes.ellipsisText}>{userInfo(member.user, member.position)}</span>
            ))}
            classes={{ tooltip: classes.tooltip }}
          >
            <div className={classes.content}>
              <span className={classes.ellipsisText}>{`${userInfo(user, position)}`}</span>
              <span>{members?.length > 1 ? `(${members.length - 1}+)` : ''}</span>
            </div>
          </AWTooltip>
        );
      },
    },
  };

  return (
    <AWTable
      rows={dataList}
      columns={columns}
      loading={loading}
      total={meta.total}
      limit={meta.limit}
      offset={meta.offset}
      loadingType="skeleton"
      handleChangePage={changePage}
      handleNextPage={handleNextPage}
      handlePrevPage={handlePrevPage}
      columnsRenderer={columnsRenderer}
      onRowClick={handleSelectProject}
      handleChangeLimit={handleChangeLimit}
    />
  );
};

export default ProjectListDesktop;
