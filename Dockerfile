FROM localhost:32001/node:20.18.2-alpine3.21 AS dev-spa
COPY .npmrc .
COPY tsconfig.json .
COPY package.json .
RUN npm install
ARG GOOGLE_TAG_MANAGER_ID
COPY .env .
ENV REACT_APP_GOOGLE_TAG_MANAGER_ID=${GOOGLE_TAG_MANAGER_ID}
ADD public public
ADD src src
RUN npm run build

FROM localhost:32001/nginx:1.23.1 AS prod-spa
COPY --from=dev-spa build/ /usr/share/nginx/html
RUN rm /etc/nginx/conf.d/default.conf
COPY ./nginx/default.conf /etc/nginx/conf.d
