{"name": "com.chargoon.cloud.front.spa", "version": "0.0.90", "private": true, "scripts": {"start": "set \"REACT_APP_BACKEND_URL=https://apps.awatcloud.com\" && set \"REACT_APP_VERSION=v0.0.90\" && set \"ESLINT_NO_DEV_ERRORS=true\" && react-scripts start", "start:31002": "set \"REACT_APP_BACKEND_URL=http://devbuild-srv.chargoon.net:31002\" && set \"REACT_APP_VERSION=v0.0.90\" && set \"ESLINT_NO_DEV_ERRORS=true\" && set \"REACT_APP_GOOGLE_TAG_MANAGER_ID=GTM-KPLS52TC\" && react-scripts start", "start:31003": "set \"REACT_APP_BACKEND_URL=http://devbuild-srv.chargoon.net:31003\" && set \"REACT_APP_VERSION=v0.0.90\" && set \"ESLINT_NO_DEV_ERRORS=true\" && set \"REACT_APP_GOOGLE_TAG_MANAGER_ID=GTM-KPLS52TC\" && react-scripts start", "start:awat": "set \"REACT_APP_BACKEND_URL=https://apps.awatcloud.com\" && set \"REACT_APP_VERSION=v0.0.90\" && set \"ESLINT_NO_DEV_ERRORS=true\" && set \"REACT_APP_GOOGLE_TAG_MANAGER_ID=GTM-KPLS52TC\" && react-scripts start", "start:stage": "set \"REACT_APP_BACKEND_URL=https://stage2.awatcloud.com/\" && set \"REACT_APP_VERSION=v0.0.90\" && set \"ESLINT_NO_DEV_ERRORS=true\" && set \"REACT_APP_GOOGLE_TAG_MANAGER_ID=GTM-KPLS52TC\" && react-scripts start", "build": "cross-env REACT_APP_VERSION=v0.0.90-202506081229 DISABLE_ESLINT_PLUGIN=true react-scripts build", "build:develop": "set \"REACT_APP_BACKEND_URL=http://devbuild-srv.chargoon.net:31002\" && set \"DISABLE_ESLINT_PLUGIN=true\" && set \"REACT_APP_VERSION=v0.0.90\" && set \"REACT_APP_GOOGLE_TAG_MANAGER_ID=GTM-KPLS52TC\" && react-scripts build", "test": "react-scripts test --transformIgnorePatterns 'node_modules/", "test:e2e": "react-scripts test ./test/e2e --transformIgnorePatterns 'node_modules/", "test:all": "react-scripts test --watchAll=false --transformIgnorePatterns 'node_modules/", "test:cov": "react-scripts test --coverage --transformIgnorePatterns 'node_modules/", "eject": "react-scripts eject", "lint": "eslint ./src --ext=js --ext=jsx --quiet", "lint:fix": "eslint ./src --ext=js --ext=jsx --fix", "prepare": "husky install", "format": "prettier --write ."}, "dependencies": {"@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@fontsource/roboto": "5.2.5", "@fontsource/vazir": "4.5.4", "@fullcalendar/core": "6.1.17", "@fullcalendar/daygrid": "6.1.17", "@fullcalendar/interaction": "6.1.17", "@fullcalendar/react": "6.1.17", "@fullcalendar/timegrid": "6.1.17", "@hassanmojab/react-modern-calendar-datepicker": "3.1.7", "@mui/icons-material": "6.4.6", "@mui/lab": "6.0.0-beta.29", "@mui/material": "6.4.6", "@mui/x-date-pickers": "7.27.1", "@mui/x-tree-view": "7.26.0", "@reduxjs/toolkit": "2.6.0", "axios": "1.7.7", "axios-hooks": "5.0.2", "chart.js": "4.4.9", "com-chargoon-cloud-front-editor": "1.0.67", "com-chargoon-cloud-front-editor-2": "0.0.118", "com.chargoon.cloud.market.common": "0.0.11", "css-mediaquery": "0.1.2", "date-fns": "4.1.0", "date-fns-jalali": "4.0.0-0", "history": "5.3.0", "i18next": "23.16.8", "jalaali-js": "1.2.8", "joi": "17.13.3", "konva": "9.3.20", "marked": "15.0.11", "moment": "2.30.1", "moment-jalaali": "0.10.4", "notistack": "3.0.2", "prop-types": "15.8.1", "react": "18.3.1", "react-beautiful-dnd": "13.1.1", "react-chartjs-2": "5.3.0", "react-dom": "18.3.1", "react-i18next": "15.4.1", "react-konva": "18.2.10", "react-modern-calendar-datepicker": "3.1.6", "react-number-format": "5.4.4", "react-redux": "9.2.0", "react-router": "7.5.3", "react-router-dom": "7.5.3", "react-scripts": "5.0.1", "react-toastify": "10.0.6", "reflect-metadata": "0.2.2", "stylis-plugin-rtl": "2.1.1", "swiper": "11.2.6", "tss-react": "4.9.16", "uuid": "11.1.0", "web-vitals": "4.2.4"}, "devDependencies": {"@babel/core": "7.26.10", "@babel/plugin-proposal-decorators": "7.25.9", "@babel/plugin-proposal-private-property-in-object": "7.21.11", "@babel/preset-env": "7.26.9", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@types/css-mediaquery": "0.1.4", "@types/jalaali-js": "1.2.0", "@types/jest": "29.5.14", "@types/moment-jalaali": "0.7.9", "@types/react": "18.3.16", "@types/react-beautiful-dnd": "13.1.8", "@types/react-dom": "18.3.0", "@types/uuid": "10.0.0", "@typescript-eslint/eslint-plugin": "8.31.1", "axios-mock-adapter": "2.1.0", "com-chargoon-cloud-contracts-tasks": "0.0.63", "com.chargoon.cloud.contracts.correspondence": "0.0.94", "com.chargoon.cloud.contracts.management-center": "0.0.43", "cross-env": "7.0.3", "eslint": "8.57.1", "eslint-config-airbnb": "19.0.4", "eslint-config-prettier": "10.1.2", "eslint-config-react-app": "7.0.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.6", "eslint-plugin-react": "7.37.5", "http-proxy-middleware": "3.0.5", "husky": "9.1.7", "jest": "29.7.0", "lint-staged": "15.5.1", "prettier": "3.5.3", "typescript": "4.9.5"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,scss,md}": "prettier --cache --write", "*.{js,jsx,ts,tsx}": "eslint --fix"}}