import { useState, memo } from 'react';
import ListItemText from '@mui/material/ListItemText';
import ListItem from '@mui/material/ListItem';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import DoneIcon from '@mui/icons-material/Done';
import DoneAllIcon from '@mui/icons-material/DoneAll';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import AWTypography from 'components/AWComponents/AWTypography';
import { momentDateUTC, momentTimeUTC, userInfo } from 'utils';
import AWIconButton from 'components/AWComponents/AWIconButton';
import AWDialog from 'components/AWComponents/AWDialog';
import AWBox from 'components/AWComponents/AWBox';
import CorrespondenceRevertForwardLetterCell from 'components/awat-table-cells/correspondence/correspondence-revertForwardLetter-cell';
import { useCartableForwardedLettersContext } from 'contexts/pageContext/letters/cartable-components/forwarded-letters-context';

const Recipient = memo(({ row, fontSize }) => (
  <AWTypography
    startIcon={
      row?.read?.at ? (
        <DoneAllIcon color="success" fontSize="inherit" />
      ) : (
        <DoneIcon fontSize="inherit" />
      )
    }
    variant="body1"
    fontSize={fontSize}
    fontWeight={400}
    color="black"
  >
    {userInfo(row.recipient.user, row.recipient.position)}
  </AWTypography>
));

const ForwardRecipient = ({ row }) => {
  const { operation } = row;
  const { t } = useTranslation();
  const [openInfoDialog, setOpenInfoDialog] = useState(false);
  const { handleOpenForwardRevertDialog, setForwardedLetterData } =
    useCartableForwardedLettersContext();

  return (
    <>
      <ListItem disablePadding onClick={(e) => e.preventDefault()}>
        <ListItemText primary={t('table.correspondence.forwardRecipient')} />
        <Recipient row={operation} fontSize={12} />
        <AWIconButton onClick={() => setOpenInfoDialog(true)}>
          <InfoOutlinedIcon color="primary" />
        </AWIconButton>
        <AWIconButton disabled={Boolean(operation?.read?.at)}>
          <CorrespondenceRevertForwardLetterCell
            row={row}
            onClick={() => {
              handleOpenForwardRevertDialog();
              setForwardedLetterData(row);
            }}
          />
        </AWIconButton>
        <AWDialog
          isTitleShow
          open={openInfoDialog}
          onClose={() => setOpenInfoDialog(false)}
          maxWidth="sm"
          fullWidth
          title={t('table.correspondence.forwardRecipient')}
        >
          <AWBox>
            <Recipient row={operation} fontSize={14} />
          </AWBox>
          <AWBox
            sx={{
              '& p': {
                marginY: 0.5,
              },
            }}
          >
            {operation?.read?.at ? (
              <>
                <AWTypography variant="body1" sx={{ fontSize: 14 }} color="GrayText">
                  {t('correspondence.letters.labels.read-in')}:
                </AWTypography>
                <AWTypography variant="body1" sx={{ fontSize: 14 }} color="black">
                  {t('common.labels.date')}: {momentDateUTC(operation.read.at)}
                </AWTypography>
                <AWTypography variant="body1" sx={{ fontSize: 14 }} color="black">
                  {t('common.labels.hour')}: {momentTimeUTC(operation.read.at)}
                </AWTypography>
              </>
            ) : (
              <AWTypography variant="body1" sx={{ fontSize: 14 }}>
                {t('correspondence.letters.labels.unread')}
              </AWTypography>
            )}
          </AWBox>
        </AWDialog>
      </ListItem>
    </>
  );
};

ForwardRecipient.propTypes = {
  row: PropTypes.shape({}).isRequired,
};
ForwardRecipient.defaultProps = {};

export default ForwardRecipient;
