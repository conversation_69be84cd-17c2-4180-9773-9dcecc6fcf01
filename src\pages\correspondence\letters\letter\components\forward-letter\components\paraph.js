import InputAdornment from '@mui/material/InputAdornment';
import PostAddOutlinedIcon from '@mui/icons-material/PostAddOutlined';
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined';

import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { t } from 'i18next';
import AWTooltip from 'components/AWComponents/AWTooltip';
import AWIconButton from 'components/AWComponents/AWIconButton';
import AWTextField from 'components/AWComponents/AWTextField';

const useStyles = makeStyles()({
  paraphField: {
    '& input': {
      textAlign: 'right',
    },
  },
  textArea: {
    minHeight: '80px',
  },
  inputLabelRoot: {
    lineHeight: 'unset',
    transform: 'translate(-14px, 16px) scale(1)!important',
  },
});

const Paraph = ({
  paraph,
  handleChangeParaph,
  toggleDialogParaphList,
  forwardLetterLoading,
  toggleDialogCreateParaph,
  security,
  validForwardNotesSample,
}) => {
  const { classes } = useStyles();

  return (
    <div>
      <AWTextField
        multiline
        maxRows={2}
        fullWidth
        id="paraph"
        name="paraph"
        label={t('forwardLetter.labels.textParaph') || ''}
        variant="outlined"
        classes={{ root: classes.paraphField }}
        value={paraph || ''}
        onChange={handleChangeParaph}
        autoFocus={!!paraph}
        disabled={forwardLetterLoading}
        InputLabelProps={{ classes: { root: classes.inputLabelRoot } }}
        InputProps={{
          classes: { root: classes.textArea },
          endAdornment: (
            <InputAdornment>
              <AWTooltip title={t('tooltip.select-forward-letter')} placement="top-end">
                <AWIconButton
                  id="paraph-dialog-button-list"
                  onClick={() => toggleDialogParaphList(true)}
                  color="info"
                >
                  <TextSnippetOutlinedIcon />
                </AWIconButton>
              </AWTooltip>
              <AWTooltip title={t('tooltip.create-forward-letter-paraph')} placement="top-end">
                <AWIconButton
                  id="paraph-dialog-button-creator"
                  onClick={() => toggleDialogCreateParaph(true)}
                  disabled={!paraph.length || !validForwardNotesSample.valid}
                  color="info"
                  security={security.createForwardNote}
                >
                  <PostAddOutlinedIcon />
                </AWIconButton>
              </AWTooltip>
            </InputAdornment>
          ),
        }}
      />
    </div>
  );
};

Paraph.propTypes = {
  paraph: PropTypes.string.isRequired,
  handleChangeParaph: PropTypes.func.isRequired,
  toggleDialogParaphList: PropTypes.func.isRequired,
  toggleDialogCreateParaph: PropTypes.func.isRequired,
};

export default Paraph;
