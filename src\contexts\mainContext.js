import React, { createContext, useEffect } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import {
  getLastLoginDataAsync as getLastLoginDataAsync_,
  selectLastLoginDataResponse,
} from '../pages/Auth/authSlice';

const MainContext = createContext();

const MainContextProvider = ({ getLastLoginDataAsync, lastLoginData, ...props }) => {
  useEffect(() => {
    // getLastLoginDataAsync();
  });

  return <MainContext.Provider value={{ lastLoginData }}>{props?.children}</MainContext.Provider>;
};

MainContextProvider.propTypes = {
  getLastLoginDataAsync: PropTypes.func.isRequired,
  lastLoginData: PropTypes.shape({}).isRequired,
  children: PropTypes.element.isRequired,
};

const mapStateToProps = (state) => {
  const lastLoginData = selectLastLoginDataResponse(state);

  return {
    lastLoginData,
  };
};

export default connect(mapStateToProps, {
  getLastLoginDataAsync: getLastLoginDataAsync_,
})(MainContextProvider);
