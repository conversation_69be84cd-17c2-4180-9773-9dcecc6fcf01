import { apiClient } from '../api-client';
import { RefreshToken } from '../types/refresh-token.type';

export const refreshTokenAsync = (): Promise<RefreshToken> => {
  const refreshToken = JSON.parse(localStorage.getItem('tokens') || '{}')?.refresh_token;
  return new Promise((resolve: (value: RefreshToken) => void, reject) => {
    apiClient({
      url: '/api/v1/auth/tokens/refresh',
      method: 'POST',
      withCredentials: true,
      headers: {
        Authorization: `Bearer ${refreshToken}`,
      },
      data: {},
    })
      .then((response) => resolve(response))
      .catch((reason) => resolve(reason.response));
  });
};
