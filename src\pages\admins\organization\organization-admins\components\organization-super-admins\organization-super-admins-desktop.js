import React from 'react';

import { Box } from '@mui/material';
import { t } from 'i18next';
import OrganizationSettingsTab from '../../../organization-settings/component/organization-settings-tab';

import DesktopAddUsersBar from '../../../../components/desktop-add-users-bar';
import AccessesDialog from '../../../../components/accesses-dialog';
import { useOrganizationSuperAdminsContext } from '../../../../../../contexts/pageContext/admins/organization-super-admins-context';
import OrganizationSuperAdminTable from './components/oragnizations-super-admin-table';

const DesktopOrganizationSuperAdmins = () => {
  const {
    organizationSuperAdmins,
    employees,
    buttonLoading,
    selectedUser,
    openPermissionsDialog,
    setOpenPermissionsDialog,
    permissionsDialogLoading,
    addOrganizationSuperAdmin,
    removeOrganizationSuperAdmin,
    viewOrganizationAdminsAccesses,
    security,
    dataList,
    tableLoading,
  } = useOrganizationSuperAdminsContext();

  return (
    <Box>
      <OrganizationSettingsTab security={security.superAdmin} />
      <DesktopAddUsersBar
        users={employees.organizationEmployeeContacts.data || []}
        usageUsers={organizationSuperAdmins.roleUsers.data}
        label={t('organization.labels.addEmployeeOrganizationSuperAdmin')}
        addUser={addOrganizationSuperAdmin}
        removeUser={removeOrganizationSuperAdmin}
        handleViewAccesses={viewOrganizationAdminsAccesses}
        buttonLoading={buttonLoading}
        selectedUser={selectedUser}
        type="organization_super_admins"
        security={security.addAndRemoveSuperAdmin}
        grantedListLoading={tableLoading}
      />
      <OrganizationSuperAdminTable
        buttonLoading={buttonLoading}
        selectedUser={selectedUser}
        removeOrganizationSuperAdmin={removeOrganizationSuperAdmin}
        security={security}
        dataList={dataList}
        tableLoading={tableLoading}
      />

      <AccessesDialog
        title={t('organization.labels.accessesOrganizationSuperAdmins')}
        open={openPermissionsDialog}
        handleClose={() => setOpenPermissionsDialog(false)}
        accesses={organizationSuperAdmins.rolePermissions.data?.permissions || []}
        loading={permissionsDialogLoading}
      />
    </Box>
  );
};

export default DesktopOrganizationSuperAdmins;
