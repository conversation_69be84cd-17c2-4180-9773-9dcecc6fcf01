import { ApiMethodDictionary } from 'api/v1/types/api-methods.type';

const methods: ApiMethodDictionary = {
  createProject: {
    httpMethod: 'POST',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  updateMembers: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'tasks',
  },
  projectsList: {
    httpMethod: 'GET',
    domainCheckStatus: 'tasks',
  },
  getProject: {
    httpMethod: 'GET',
  },
  getProjectMembers: {
    httpMethod: 'GET',
  },
  getProjectTask: {
    httpMethod: 'GET',
  },
  updateProject: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'projects',
  },
  updateProjectMembers: {
    httpMethod: 'PATCH',
    checkStatus: true,
    domainCheckStatus: 'Projects',
  },
  updateTaskProjects: {
    httpMethod: 'PATCH',
    checkStatus: true,
    // domainCheckStatus: 'tasks',
  },
};

export default methods;
