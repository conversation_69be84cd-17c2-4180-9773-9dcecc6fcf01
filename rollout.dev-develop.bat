@ECHO OFF
@REM call npm run build
call docker build -t com.chargoon.cloud.front.spa:0.0.90 --build-arg GOOGLE_TAG_MANAGER_ID=GTM-KPLS52TC .
call docker tag com.chargoon.cloud.front.spa:0.0.90 devbuild-srv.chargoon.net:32001/com.chargoon.cloud.front.spa:0.0.90
call docker push devbuild-srv.chargoon.net:32001/com.chargoon.cloud.front.spa:0.0.90
call type deployment.dev-develop.yaml | ssh -p 22443 -i ../k8s/rsa <EMAIL> kubectl apply -f -
call ssh -p 22443 -i ../k8s/rsa <EMAIL> kubectl rollout restart deployment com-chargoon-cloud-front-spa -n develop
