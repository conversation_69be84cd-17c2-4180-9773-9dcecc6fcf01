import React, { useEffect, useState, useRef, useMemo, useCallback } from 'react';

import PropTypes from 'prop-types';
import { v4 as uuidv4 } from 'uuid';
import { connect } from 'react-redux';
import useAxios from 'axios-hooks';
import { useLocation, useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import { useUploadDocument } from 'hooks/useUploadDocument';
import { useUser } from 'contexts/userContext';
import snackbarUtils from 'utils/snackbarUtils';
import { useLayout } from 'contexts/layoutContext';
import useListener from 'hooks/useListener';
import { useSecurity } from 'common/security/security-context';
import {
  correspondenceExchangeRegistryPermissionsAsync as correspondenceExchangeRegistryPermissionsAsync_,
  correspondenceExchangeRegistryPermissionsReset as correspondenceExchangeRegistryPermissionsReset_,
  correspondenceExchangeRegistryReducerName,
} from 'pages/correspondence/exchange-registry/correspondence-exchange-registry-slice';
import useSecretariatHasAccess from 'hooks/useSecretariatsHasAccess';
import useIndicatorsHasAccess from 'hooks/useIndicatorsHasAccess';
import {
  createCorrespondenceSampleReset as createCorrespondenceSampleReset_,
  createCorrespondenceSampleAsync as createCorrespondenceSampleAsync_,
  correspondenceSamplesListAsync as correspondenceSamplesListAsync_,
  correspondenceSamplesListReset as correspondenceSamplesListReset_,
  deleteCorrespondenceSampleAsync as deleteCorrespondenceSampleAsync_,
  createForwardNotesSampleAsync as createForwardNotesSampleAsync_,
  getForwardNotesSamplesAsync as getForwardNotesSamplesAsync_,
  deleteForwardNotesSamplesAsync as deleteForwardNotesSamplesAsync_,
  forwardNotesSamplesReset as forwardNotesSamplesReset_,
} from 'pages/correspondence/correspondence-samples/correspondence-samples-slice';
import { baseUrl } from 'api/v1/services/api-client';
import { createLetterContentByTopicAsync as createLetterContentByTopicAsync_ } from 'pages/genai/genai.slice';
import securityKeys from 'common/security/securityKeys';
import {
  getSecretariatsAsync as getSecretariatsAsync_,
  getSecretariatsReset as getSecretariatsReset_,
  getIndicatorsAsync as getIndicatorsAsync_,
  getIndicatorsReset as getIndicatorsReset_,
  getLetterLayoutsAsync as getLetterLayoutsAsync_,
  getLetterLayoutsReset as getLetterLayoutsReset_,
} from '../../secretariats/secretariatsSlice';
import { uploadDocumentsAsync as uploadDocumentsAsync_ } from '../../../documents/documentsSlice';
import {
  contactsOrganizationAsync as contactsOrganizationAsync_,
  createLetterAsync as createLetterAsync_,
  updateLetterAsync as updateLetterAsync_,
  setLetterNumberAsync as setLetterNumberAsync_,
  signLetterAsync as signLetterAsync_,
  getLetterAsync as getLetterAsync_,
  contactsOrganizationReset as contactsOrganizationReset_,
  getLetterReset as getLetterReset_,
  updateLetterReset as updateLetterReset_,
  signLetterReset as signLetterReset_,
  createLetterReset as createLetterReset_,
  parseEceReset as parseEceReset_,
  setLetterNumberReset as setLetterNumberReset_,
  composeEce as composeEce_,
  forwardLetterAsync as forwardLetterAsync_,
  forwardedLetterByMeReset as forwardedLetterByMeReset_,
  forwardLetterReset as forwardLetterReset_,
  forwardedLetterToMeReset as forwardedLetterToMeReset_,
  lettersNotExchangedReset as lettersNotExchangedReset_,
  terminateLetterReset as terminateLetterReset_,
  discardLetterReset as discardLetterReset_,
  terminateLetterAsync as terminateLetterAsync_,
  discardLetterAsync as discardLetterAsync_,
  getLetterHistoryAsync as getLetterHistoryAsync_,
  letterHistoryReset as letterHistoryReset_,
  voidLetterAsync as voidLetterAsync_,
  voidLetterReset as voidLetterReset_,
  searchReferencesLettersAsync as searchReferencesLettersAsync_,
  checkingIncomingNumberRepetitiveAsync as checkingIncomingNumberRepetitiveAsync_,
  checkingIncomingNumberRepetitiveReset as checkingIncomingNumberRepetitiveReset_,
} from '../lettersSlice';
import {
  sendECEAsync as sendECEAsync_,
  sendECEReset as sendECEReset_,
  eceReceiptAsync as eceReceiptAsync_,
} from '../../secretariats/ece-mails-slice';
import LetterValidation from './LetterValidation';
import {
  organizationEmployeeContactsAsync as organizationEmployeeContactsAsync_,
  organizationEmployeeContactsReset as organizationEmployeeContactsReset_,
} from '../../../management-center/employees/employeesSlice';
import { priorities, confidentialities, referenceTypes, letterTypes } from '../letter-utility';
import {
  getOrganizationPositionsAsync as getOrganizationPositionsAsync_,
  getOrganizationPositionsReset as getOrganizationPositionsReset_,
} from '../../../management-center/positions/positionsSlice';
import withLetterComposeSecurity, {
  propTypesLetterComposeSecurity,
} from './withLetterComposeSecurity';
import { withCorrespondenceInitializer } from '../../withCorresponseInitializer';

const initialState = {
  attachments: [],
  bcc: [],
  cc: [],
  confidentiality: '',
  date: '',
  description: '',
  id: '',
  incomingNumber: '',
  incomingDate: '',
  indicator: null,
  organization: {},
  priority: '',
  recipient: [],
  referenceDate: '',
  referenceNumber: '',
  referenceType: '',
  references: [],
  secretariat: null,
  sender: [],
  signer: [],
  subject: '',
  type: '',
  body: '',
  additionalAttachments: [],
  extraData: undefined,
};

const Letter = ({
  eceMails,
  letters,
  secretariats,
  documents,
  getOrganizationPositionsAsync,
  organizationEmployeeContactsAsync,
  getOrganizationPositionsReset,
  organizationEmployeeContactsReset,
  contactsOrganizationReset,
  contactsOrganizationAsync,
  getSecretariatsAsync,
  getSecretariatsReset,
  getIndicatorsAsync,
  getIndicatorsReset,
  createLetterAsync,
  updateLetterAsync,
  setLetterNumberAsync,
  signLetterAsync,
  uploadDocumentsAsync,
  getLetterAsync,
  getLetterReset,
  createLetterReset,
  updateLetterReset,
  signLetterReset,
  parseEceReset,
  setLetterNumberReset,
  composeEce,
  positions,
  employees,
  forwardLetterAsync,
  lettersNotExchangedReset,
  forwardedLetterToMeReset,
  forwardedLetterByMeReset,
  forwardLetterReset,
  terminateLetterReset,
  discardLetterReset,
  terminateLetterAsync,
  discardLetterAsync,
  getLetterHistoryAsync,
  letterHistoryReset,
  security,
  getLetterLayoutsAsync,
  getLetterLayoutsReset,
  sendECEAsync,
  sendECEReset,
  correspondenceExchangeRegistryPermissionsAsync,
  correspondenceExchangeRegistryPermissionsReset,
  correspondenceExchangeRegistry,
  voidLetterAsync,
  voidLetterReset,
  organization,
  correspondenceAuthz,
  accessibility,
  securityLevelDictionary,
  createCorrespondenceSampleAsync,
  createCorrespondenceSampleReset,
  correspondenceSamples,
  correspondenceSamplesListAsync,
  correspondenceSamplesListReset,
  deleteCorrespondenceSampleAsync,
  searchReferencesLettersAsync,
  createLetterContentByTopicAsync,
  genai,
  checkingIncomingNumberRepetitiveAsync,
  checkingIncomingNumberRepetitiveReset,
  eceReceiptAsync,
  createForwardNotesSampleAsync,
  getForwardNotesSamplesAsync,
  deleteForwardNotesSamplesAsync,
  forwardNotesSamplesReset,
}) => {
  const [isOpenComposeInfoConfig, setIsOpenComposeInfoConfig] = useState(false);
  const additionalAttachmentsRef = useRef(null);
  const { t } = useTranslation();
  const referenceRef = useRef(null);
  const registrationInfoRef = useRef(null);
  const attachmentsRef = useRef(null);
  const location = useLocation();
  const params = useParams();
  const navigate = useNavigate();
  const { signedInUser } = useUser();
  const { setTitle, selectedPosition, handleOpenCheckStatusOveredDialog } = useLayout();
  const { organizationId, letterId } = params;
  const { search, state: defaultState } = location;
  const { getSecretariatAdminPermissions } = useSecurity();
  const ownerId = organization.selectedOrganization?.owner?.id;
  const typeOfLetter = new URLSearchParams(search).get('type');
  const operationId = new URLSearchParams(search).get('operationId');
  const secretariatId = new URLSearchParams(search).get('secretariatId');
  //* defaultProps -------------------------------------------------------
  const localStoragePath = useMemo(() => `compose-info-config-${organizationId}`, [organizationId]);

  const defaultProps = useMemo(() => {
    const sender =
      defaultState?.defaultProps?.sender?.filter?.((item) => item?.id || item?._id) ?? [];
    const recipient =
      defaultState?.defaultProps?.recipient?.filter?.(
        (item) => item?.user?.id || item?.user?._id,
      ) ?? [];
    const cc =
      defaultState?.defaultProps?.cc?.filter?.((item) => item?.user?.id || item?.user?._id) ?? [];
    const bcc =
      defaultState?.defaultProps?.bcc?.filter?.((item) => item?.user?.id || item?.user?._id) ?? [];

    const extraData = [];
    if (defaultState?.defaultProps?.ece) {
      extraData.push({
        data: defaultState?.defaultProps?.ece,
        domain: 'ecemails',
      });
    }

    return defaultState?.defaultProps
      ? {
          ...defaultState?.defaultProps,
          recipient,
          sender,
          cc,
          bcc,
          extraData: extraData.length > 0 ? extraData : undefined,
        }
      : null;
  }, []);

  //* state ------------------------------------------------------------
  const [loading, setLoading] = useState(false);
  const [selectedLetter, setSelectedLetter] = useState(null);
  const [type, setType] = useState('');
  const [initState, setInitState] = useState(defaultProps ?? initialState);
  const [state, setState] = useState(defaultProps ?? initialState);

  const [isBlocking, setIsBlocking] = useState(false);
  const [openBackdrop, setOpenBackdrop] = useState(false);
  const [composeInfoConfig, setComposeInfoConfig] = useState({
    allShow: false,
    attachment: false,
    registerInformation: false,
    references: false,
    additionalAttachments: false,
  });
  const [extraItems, setExtraItems] = useState([
    { id: 'allShow', title: t('common.labels.allShow'), checked: false },
    {
      id: 'attachment',
      title: t('secretariats.labels.attachment'),
      checked: false,
      ref: attachmentsRef,
    },
    {
      id: 'registerInformation',
      title: t('secretariats.labels.registerInformation'),
      checked: typeOfLetter === 'incoming',
      ref: registrationInfoRef,
    },
    {
      id: 'references',
      title: t('secretariats.labels.references'),
      checked: false,
      ref: referenceRef,
    },
    {
      id: 'additionalAttachments',
      title: t('secretariats.labels.additionalAttachments'),
      checked: false,
      ref: additionalAttachmentsRef,
    },
  ]);
  const [showBcc, setShowBcc] = useState(false);
  const [contacts, setContacts] = useState([]);
  const [users, setUsers] = useState([]);
  const [isLetterCreating, setIsLetterCreating] = useState(false);
  const [isLetterUpdating, setIsLetterUpdating] = useState(false);
  const [isFileUploading, setIsFileUploading] = useState(false);
  const [referencesList, setReferencesList] = useState([]);
  const [docCorrelationIds, setDocCorrelationIds] = useState([]);
  const [tab, setTab] = useState('main');
  const [openAddReferenceDrawer, setOpenAddReferenceDrawer] = useState(false);
  const [senderSelection, setSenderSelection] = useState(null);
  const [recipientSelection, setRecipientSelection] = useState([]);
  const [ccSelection, setCcSelection] = useState([]);
  const [bccSelection, setBccSelection] = useState([]);
  const [signerSelection, setSignerSelection] = useState([]);
  const [secretariatSelection, setSecretariatSelection] = useState(null);
  const [indicatorSelection, setIndicatorSelection] = useState(null);
  const [prioritySelection, setPrioritySelection] = useState({});
  const [confidentialitySelection, setConfidentialitySelection] = useState({});
  const [showDialogNumberedLetter, setShowDialogNumberedLetter] = useState(false);
  const [showDialogNumberedErrorForIndicatorStatus, setShowDialogNumberedErrorForIndicatorStatus] =
    useState(false);
  const [showDialogSaveAndSetLetterNumber, setShowDialogSaveAndSetLetterNumber] = useState(false);
  const [loadingLetterNumber, setLoadingLetterNumber] = useState(false);
  const [isCallSetLetterNumber, setIsCallSetLetterNumber] = useState(false);
  const [isDocUploaded, setIsDocUploaded] = useState(false);
  const [isNumberedNow, setIsNumberedNow] = useState(false);
  const [openPositionEmployeeTreeDialogRecipient, setOpenPositionEmployeeTreeDialogRecipient] =
    useState(false);
  const [openPositionEmployeeTreeDialogSender, setOpenPositionEmployeeTreeDialogSender] =
    useState(false);
  const [openPositionEmployeeTreeDialogCC, setOpenPositionEmployeeTreeDialogCC] = useState(false);
  const [openPositionEmployeeTreeDialogBCC, setOpenPositionEmployeeTreeDialogBCC] = useState(false);
  const [openPositionEmployeeTreeDialogSigner, setOpenPositionEmployeeTreeDialogSigner] =
    useState(false);
  //* states for tab forward letter and radio button and loading
  //* states for radio button forward letter
  //* states for forward letter loading
  const [tabPreParaphValue, setTabPreParaphValue] = useState('forwardNotesSamples');
  const [selectedRadio, setSelectedRadio] = useState({});
  const [forwardNotesSamples, setForwardNoteSamples] = useState([]);
  const [isSubmittingForwardNoteSample, setIsSubmittingForwardNoteSample] = useState(false);
  const [isLoadingForwardNoteSamples, setIsLoadingForwardNoteSamples] = useState(false);
  const [deleteForwardNoteSampleLoading, setDeleteForwardNoteSampleLoading] = useState(null);
  const [validForwardNotesSample, setValidForwardNotesSample] = useState({
    valid: true,
    text: null,
  });
  //* -----
  const [openForwardDialogRecipient, setOpenForwardDialogRecipient] = useState(false);
  const [openForwardLetter, setOpenForwardLetter] = useState(false);
  const [forwardType, setForwardType] = useState('normal');
  const [paraph, setParaph] = useState('');
  const [openPreParaphList, setOpenPreParaphList] = useState(false);
  const [openPreCreateParaph, setOpenPreCreateParaph] = useState('');
  const [forwardedRecipientList, setForwardedRecipientList] = useState([]);
  const [forwardRecipientSelection, setForwardRecipientSelection] = useState([]);
  const [forwardLetterLoading, setForwardLetterLoading] = useState(false);
  const [forwardLetterTag, setForwardLetterTag] = useState(null);
  const [forwardLetterAttachments, setForwardLetterAttachments] = useState([]);
  const [openForwardListMobileDialog, setOpenForwardListMobileDialog] = useState(false);
  const operationInfo = location.state?.operation || '';
  const [isCheckedAll, setIsCheckedAll] = useState(false);
  const [changedcheck, setChangedcheck] = useState({});
  const [isLetterCreated, setIsLetterCreated] = useState(false);
  const [attachments, setAttachments] = useState([]);
  const [attachmentList, setAttachmentList] = useState([]);
  const [additionalAttachments, setAdditionalAttachments] = useState([]);
  const [anchorElLetterInfoMenu, setAnchorElLetterInfoMenu] = useState(null);
  const [isVisibleLetterHistoryDialog, setIsVisibleLetterHistoryDialog] = useState(false);
  const openLetterInfoMenu = Boolean(anchorElLetterInfoMenu);
  const [letterHistory, setLetterHistory] = useState(null);
  const [letterHistoryLoading, setLetterHistoryLoading] = useState(false);
  const [showDialogTerminate, setShowDialogTerminate] = useState(false);
  const [showDialogDiscard, setShowDialogDiscard] = useState(false);
  const [terminateDescriptionVal, setTerminateDescriptionVal] = useState('');
  const [discardDescriptionVal, setDiscardDescriptionVal] = useState('');
  const [isLoadingTerminate, setIsLoadingTerminate] = useState(false);
  const [isLoadingDiscard, setIsLoadingDiscard] = useState(false);
  const [isTouched, setIsTouched] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const openExtraButton = Boolean(anchorEl);
  const [openDetails, setOpenDetails] = useState(false);
  const [letterLayoutsLoading, setLetterLayoutsLoading] = useState(false);
  const [openPrintDialog, setOpenPrintDialog] = useState(false);
  const [openSendECEDialog, setOpenSendECEDialog] = useState(false);
  const [openExportECEDialog, setOpenExportECEDialog] = useState(false);
  const [letterLayoutsList, setLetterLayoutsList] = useState([]);
  const [selectedLetterLayout, setSelectedLetterLayout] = useState('no-layout');
  const [eceEmailRecipient, setEceEmailRecipient] = useState('');
  const [sendECELoading, setSendECELoading] = useState(false);
  const [exportECELoading, setExportECELoading] = useState(false);
  const [ECEEmailRecipientErrorText, setECEEmailRecipientErrorText] = useState(false);
  const [showDialogSignAndSaveOrCancel, setShowDialogSignAndSaveOrCancel] = useState(false);
  const [ccDescriptionPanel, setCcDescriptionPanel] = useState({ open: false, key: 0, value: '' });
  const [checkedSign, setCheckedSign] = useState(true);
  const [checkedStamps, setCheckedStamps] = useState(true);
  const [forwardAttachmentsLoading, setForwardAttachmentsLoading] = useState(false);
  const [forwardRecipient, setForwardRecipient] = useState([]);
  const [voidLetterDialog, setVoidLetterDialog] = useState(false);
  const [voidLetterNote, setVoidLetterNote] = useState(null);
  const [voidLetterLoading, setVoidLetterLoading] = useState(false);
  const [errorOfVoidOperationText, setErrorOfVoidOperationText] = useState('');
  const [errorVoidLetterDialog, setErrorVoidLetterDialog] = useState(false);
  const [openForCreateLetterSampleDialog, setOpenForCreateLetterSampleDialog] = useState(false);
  const [createLetterSampleState, setCreateLetterSampleState] = useState({
    content: '',
    title: '',
  });
  const [openDocumentPreviewerDialog, setOpenDocumentPreviewerDialog] = useState({
    open: false,
    documents: [],
    currentIndex: 0,
  });

  const [quicklyAddingContactDialog, setQuicklyAddingContactDialog] = useState({
    open: false,
    name: '',
  });
  const [letterSamplesListAnchorElAnchorEl, setLetterSamplesListAnchorEl] = useState(null);
  const [showFieldsReferenceLetter, setShowFieldsReferenceLetter] = useState(false);
  const [referenceLetterNumberList, setReferenceLetterNumberList] = useState();

  const [referenceSearchPopoverOpen, setReferenceSearchPopoverOpen] = useState(false);

  const [newReferencesItems, setNewReferencesItems] = useState({
    referenceDate: null,
    referenceType: null,
    referenceNumber: null,
    letter: null, // { id: '' }
  });
  const [letterAiAnchorEl, setLetterAiAnchorEl] = useState(null);
  const [selectedTabCreateLetterByTopic, setSelectedTabCreateLetterByTopic] = useState('official');
  const [textFieldGenai, setTextFieldGenai] = useState('');
  const [dataGenai, setDataGenai] = useState('');

  const isEceLetter =
    (Array.isArray(state.extraData)
      ? Boolean(state.extraData?.find((item) => item?.domain))
      : false) && typeOfLetter === 'incoming';

  const [showDialogNumberRepetitive, setShowDialogNumberRepetitive] = useState(false);

  //* api and axios ---------------------------------------------------------------
  const [{ error: printError }, downloadLetter] = useAxios(
    {
      url: `${baseUrl}/api/v1/letters/export`,
      method: 'POST',
      responseType: 'blob',
    },
    { manual: true },
  );

  const [{ error: printErrorEce }, downloadLetterEce] = useAxios(
    {
      url: `${baseUrl}/api/v1/letters/export-ece`,
      method: 'POST',
    },
    { manual: true },
  );

  //* useEffect and useListener ---------------------------------------------------
  useEffect(() => {
    const { data } = correspondenceSamples.deleteCorrespondenceSample;
    if (data?.id) {
      correspondenceSamplesListReset();
      correspondenceSamplesListAsync({
        organizationId,
        queryObject: { limit: 1000, offset: 0 },
      });
    }
  }, [correspondenceSamples.deleteCorrespondenceSample.data]);

  useListener(() => {
    if (correspondenceSamples.createCorrespondenceSample?.status === 'success') {
      createCorrespondenceSampleReset();
      handleCloseCreateLetterSampleDialog();
      correspondenceSamplesListReset();
    }
  }, [correspondenceSamples.createCorrespondenceSample]);

  useEffect(() => {
    if (letterId) {
      setTitle('مشاهده نامه');
    } else {
      setTitle(`ایجاد نامه ${letterTypes.find((l) => l.value === typeOfLetter)?.label}`);
    }
    getSecretariatsReset();
    getSecretariatsAsync({ path: organizationId });
    getOrganizationPositionsReset();
    organizationEmployeeContactsReset();
    contactsOrganizationReset();
    setForwardLetterLoading(false);
    setShowDialogNumberedLetter(false);
    getLetterReset();
    letterHistoryReset();
    getLetterLayoutsReset();
    sendECEReset();
    checkingIncomingNumberRepetitiveReset();
    return () => {
      setShowDialogNumberedLetter(false);
      setForwardLetterLoading(false);
      getOrganizationEmployeeContacts();
      getOrganizationPositions();
      getLetterReset();
      updateLetterReset();
      letterHistoryReset();
      getLetterLayoutsReset();
      sendECEReset();
      checkingIncomingNumberRepetitiveReset();
      composeEce({ open: false, data: null });
    };
  }, []);

  useEffect(() => {
    if (letterId || operationId) {
      getLetter();
    }
  }, []);

  useEffect(() => {
    if (letters.letterHistory.data?.length > 0) {
      setLetterHistory(letters.letterHistory.data);
    }
    if (letters.letterHistory.data?.success === false) {
      setLetterHistoryLoading(false);
      letterHistoryReset();
      toggleVisibleLetterHistoryDialog(false);
    }
  }, [letters.letterHistory.data]);

  useEffect(() => {
    setLoading(true);
    if (organizationId && letterId) {
      if (letters.selectedLetter.data) {
        setSelectedLetter(letters.selectedLetter.data || null);
        setLoading(false);
        setTitle(t('secretariats.labels.viewLetter'));
      } else {
        setTitle(
          `${t('commands.letters.LetterComposed')} ${
            letterTypes.find((l) => l.value === typeOfLetter)?.label
          }`,
        );
      }
    } else if (organizationId && !letterId) {
      setLoading(false);
    }
  }, [organizationId, letterId, letters.selectedLetter.data]);

  useEffect(() => {
    const { data } = correspondenceExchangeRegistry.exchangeRegistryPermissions;
    if (data) {
      setForwardRecipient(data);
    }
  }, [correspondenceExchangeRegistry.exchangeRegistryPermissions.data]);

  useEffect(() => {
    setAttachmentList(attachments.filter((a) => a.id));
  }, [attachments]);

  useEffect(() => {
    if (selectedLetter) {
      letters.parseEce.data ? setType('create') : setType('editAndView');
      setInitState(readData(selectedLetter));
      setState(readData(selectedLetter));
      fillData(selectedLetter);
    } else {
      setType('create');
      setInitState(readData(defaultProps ?? null));
      setState(readData(defaultProps ?? null));
      if (defaultProps) {
        fillData(defaultProps);
      }
    }
  }, [selectedLetter]);

  const SetLetterInfoConfig = () => {
    const data = JSON.parse(localStorage.getItem(localStoragePath) || '{}');
    if (Object.keys(data).length) {
      setExtraItems((prev) =>
        prev.map((item) => ({
          ...item,
          checked: data[item.id],
        })),
      );
      setComposeInfoConfig(data);
    }
  };

  useListener(() => {
    secretariatAdminPermissions();
    SetLetterInfoConfig();
  }, [initState]);

  useEffect(() => {
    getOrganizationPositionsReset();
    organizationEmployeeContactsReset();
    getOrganizationEmployeeContacts();
    getOrganizationPositions();
    if (typeOfLetter !== 'internal') {
      getContactsOrganization();
    }
  }, [typeOfLetter]);

  useEffect(() => {
    if (letters.contactsOrganization.data?.length) {
      setContacts(letters.contactsOrganization.data);
    }
  }, [letters.contactsOrganization.data]);

  useEffect(() => {
    if (employees.organizationEmployeeContacts.data?.length) {
      setUsers(employees.organizationEmployeeContacts.data);
    }
  }, [employees.organizationEmployeeContacts.data]);

  useEffect(() => {
    const { data, success } = secretariats.letterLayoutsList;
    if (data) {
      setLetterLayoutsLoading(false);
      if (success === false) {
        setLetterLayoutsList([]);
      }
      setLetterLayoutsList(data);
      setSelectedLetterLayout(
        data.find((letterLayout) => letterLayout?.isDefault)?.layoutId || 'no-layout',
      );
    }
  }, [secretariats.letterLayoutsList.data]);

  useEffect(() => {
    scrollToChecked();
    const exceptShowAllArr = extraItems.filter((e) => e.id !== 'allShow');
    const showAllObj = extraItems.find((e) => e.id === 'allShow');
    if (isCheckedAll) {
      setIsCheckedAll(false);
      setExtraItems((prevState) => [...prevState]);
      exceptShowAllArr.every((i) => i.checked === true)
        ? (showAllObj.checked = true)
        : (showAllObj.checked = false);
    }
  }, [extraItems]);

  useEffect(() => {
    if (letters.selectedLetter.data) {
      const {
        _id,
        type: selectedLetterType,
        operations,
        theOperation,
      } = letters.selectedLetter.data;
      let letterOperationId;
      if (operations?.length) {
        letterOperationId = operations[operations.length - 1].id;
      }
      const theOperationId = theOperation?._id || letterOperationId;

      if (theOperationId && isLetterCreated) {
        return navigate(
          `/organizations/${organizationId}/correspondence/${_id}/?type=${selectedLetterType}&operationId=${theOperationId}&secretariatId=${
            letters.selectedLetter?.data?.secretariat?.id ?? secretariatId
          }`,
          { replace: true, state: { prePath: 'createLetter', operation: theOperation } },
        );
      }
      if (theOperationId) {
        return navigate(
          `?type=${selectedLetterType}&operationId=${theOperationId}&secretariatId=${
            letters.selectedLetter?.data?.secretariat?.id ?? secretariatId
          }`,
          { replace: true, state: { prePath: 'createLetter', operation: theOperation } },
        );
      }
      return navigate(
        `?type=${selectedLetterType}&secretariatId=${
          letters.selectedLetter?.data?.secretariat?.id ?? secretariatId
        }`,
        { replace: true, state: { prePath: 'createLetter', operation: theOperation } },
      );
    }
  }, [letters.selectedLetter.data, isLetterCreated]);

  useEffect(() => {
    if (isLetterCreated) {
      const { data } = letters.createLetter;
      const { id } = data.data;
      getLetter(id);
      createLetterReset();
      letterHistoryReset();
    }
  }, [isLetterCreated]);

  useEffect(() => {
    (async () => {
      if (letters.createLetter.data?.success) {
        setInitState(state);
        parseEceReset();
        setIsLetterCreated(true);
        setIsLetterCreating(false);
        setIsBlocking(false);
        getLetterLayoutsReset();
        secretariatAdminPermissions();
        setIsTouched(false);
        setOpenBackdrop(false);
        lettersNotExchangedReset();
      } else {
        setOpenBackdrop(false);
      }
    })();
  }, [letters.createLetter.data]);

  useEffect(() => {
    if (letters.updateLetter.data?.success) {
      secretariatAdminPermissions();
      setInitState(state);
      setIsLetterUpdating(false);
      setIsBlocking(false);
      getLetterLayoutsReset();
      setIsTouched(false);
      if (isSaveAndSignRef.current) {
        handleSignLetter();
      }
      if (!isCallSetLetterNumber) {
        setOpenBackdrop(false);
        updateLetterReset();
        letterHistoryReset();
        if (!isSaveAndSignRef.current) {
          getLetter();
        }
      } else {
        setLetterNumberAfterSave();
      }
      lettersNotExchangedReset();
      isSaveAndSignRef.current = false;
    } else {
      setOpenBackdrop(false);
    }
  }, [letters.updateLetter.data]);

  useEffect(() => {
    const { data } = eceMails.sendECE;
    if (data?.success === true) {
      snackbarUtils.success(t('correspondence.letters.labels.successSendECE'));
      setSendECELoading(false);
    } else {
      setSendECELoading(false);
      setOpenSendECEDialog(false);
      setEceEmailRecipient('');
    }
  }, [eceMails.sendECE.data]);

  useEffect(() => {
    if (isDocUploaded && (isLetterCreating || isLetterUpdating)) {
      setIsFileUploading(false);
      setIsDocUploaded(false);
      save();
    }
  }, [isLetterCreating, isLetterUpdating, isDocUploaded]);

  useEffect(() => {
    if (isFileUploading) {
      docCorrelationIds.forEach((dc) => {
        if (documents.documents.inserting[dc.docCorrelationId]) {
          attachments.forEach((at) => {
            if (
              at.name === dc.fileName &&
              +at.size === +documents.documents.inserting[dc.docCorrelationId].data.size
            ) {
              setAttachmentList((prev) => [
                ...prev,
                {
                  id: documents.documents.inserting[dc.docCorrelationId].data.id,
                  type: at.isLetterContent || at?.mustBeAsContent ? 'content' : '',
                  name: at.name,
                  size: at.size,
                },
              ]);
            }
          });
          setIsDocUploaded(true);
        }
      });
    }
  }, [attachments, docCorrelationIds, documents.documents.inserting]);

  useEffect(() => {
    (async () => {
      if (letters.signLetter.data?.success) {
        setOpenBackdrop(false);
        signLetterReset();
        letterHistoryReset();
        getLetter();
      } else {
        setOpenBackdrop(false);
      }
      setShowDialogSignAndSaveOrCancel(false);
    })();
  }, [letters.signLetter.data]);

  useEffect(() => {
    if (printError) {
      setOpenBackdrop(false);
      snackbarUtils.error(`${t('common.errors.receiveData')}`);
    }
  }, [printError]);

  useEffect(() => {
    if (
      printErrorEce?.response?.data?.meta?.messages?.find(
        (item) => item?.exception === 'EceRecipientBusinessNotSpecified',
      )
    ) {
      setOpenBackdrop(false);
      snackbarUtils.error(`${t('common.errors.businessNameNotRegistered')}`);
    } else if (printErrorEce) {
      setOpenBackdrop(false);
      snackbarUtils.error(`${t('common.errors.receiveData')}`);
    }
  }, [printErrorEce]);

  useEffect(() => {
    if (letters.forwardLetter?.success) {
      handleCloseForwardLetter();
      setForwardLetterLoading(false);
      lettersNotExchangedReset();
      forwardedLetterToMeReset();
      forwardedLetterByMeReset();
      forwardLetterReset();
      letterHistoryReset();
      getLetterReset();
      getLetterLayoutsReset();
      navigate(`/organizations/${organizationId}/correspondence`);
    } else if (letters.forwardLetter.data?.commandName === 'letters.LetterForwarded') {
      handleCloseForwardLetter();
    } else {
      setForwardLetterLoading(false);
    }
  }, [letters.forwardLetter.data]);

  useEffect(() => {
    (async () => {
      setShowDialogSaveAndSetLetterNumber(false);
      if (letters.letterNumber.data?.success) {
        setLetterNumberReset();
        letterHistoryReset();
        setIsCallSetLetterNumber(false);
        getLetter();
        setIsNumberedNow(true);
        setOpenBackdrop(false);
      } else {
        setOpenBackdrop(false);
        setLoadingLetterNumber(false);
        setShowDialogNumberedLetter(false);
      }
    })();
  }, [letters.letterNumber.data]);

  useListener(() => {
    getLetterLayoutsReset();
  }, [secretariatSelection]);

  useEffect(() => {
    if (letters.terminateLetter.data?.success) {
      setIsLoadingTerminate(false);
      setShowDialogTerminate(false);
      terminateLetterReset();
      letterHistoryReset();
      getLetter();
    } else {
      setIsLoadingTerminate(false);
    }
  }, [letters.terminateLetter.data]);

  useEffect(() => {
    if (letters.discardLetter.data?.success) {
      setIsLoadingDiscard(false);
      setShowDialogDiscard(false);
      discardLetterReset();
      letterHistoryReset();
      getLetterReset();
      getLetter();
    } else {
      setIsLoadingDiscard(false);
    }
  }, [letters.discardLetter.data]);

  // * void letter useEffect
  useListener(() => {
    if (letters.voidLetter.data?.success) {
      setVoidLetterLoading(false);
      setVoidLetterDialog(false);
      getLetter();
    } else if (letters.voidLetter.data?.success === false) {
      voidLetterReset();
      handleErrorOfVoidOperationText();
    }
  }, [letters.voidLetter.data]);

  useListener(() => {
    if (correspondenceExchangeRegistry.exchangeRegistryPermissions.data) {
      selectForwardRecipientsAutomatically();
    }
  }, [correspondenceExchangeRegistry.exchangeRegistryPermissions.data]);

  useEffect(() => {
    const { data } = letters.searchReferencesLetters;

    if (data) {
      setReferenceLetterNumberList(data);
    }
  }, [letters.searchReferencesLetters.data]);

  useEffect(() => {
    const { data } = genai.letterContentByTopic;
    if (data) {
      setLetterAiAnchorEl(null);
      setDataGenai(data.content);
    }
  }, [genai.letterContentByTopic.data]);

  //* create & delete & list forward note sample
  useEffect(() => {
    if (openPreParaphList) {
      forwardNoteSamplesAsync();
    }
  }, [openPreParaphList]);

  useEffect(() => {
    if (correspondenceSamples.deleteForwardNotesSamples.success) {
      forwardNoteSamplesAsync();
    }
  }, [correspondenceSamples.deleteForwardNotesSamples.data]);

  useEffect(() => {
    const { status, data, success } = correspondenceSamples.forwardNotesSamples;
    const isIdleAndSuccess = status === 'idle' && success;

    setIsLoadingForwardNoteSamples(status === 'pending');

    if (isIdleAndSuccess) {
      setForwardNoteSamples(data);
      setIsLoadingForwardNoteSamples(false);
    }
  }, [correspondenceSamples.forwardNotesSamples]);

  //* memorize ----------------------------------------------------------------
  const indicatorsActived = useMemo(
    () => secretariats?.indicatorsList?.data?.filter?.((item) => !item?.isDisabled) ?? [],
    [secretariats?.indicatorsList?.data],
  );

  //* Requester
  const requester = useMemo(
    () => ({
      user: {
        id: signedInUser.id,
      },
      position: {
        id: selectedPosition.position.id,
        slot: selectedPosition.position.slot,
      },
    }),
    [selectedPosition.position, signedInUser],
  );

  const loadingCreateLetterSample = useMemo(
    () => correspondenceSamples.createCorrespondenceSample?.status === 'pending',
    [correspondenceSamples.createCorrespondenceSample],
  );

  const correspondenceSamplesListLoading = useMemo(
    () => correspondenceSamples.correspondenceSamplesList.status === 'pending',
    [correspondenceSamples.correspondenceSamplesList.status],
  );

  const correspondenceSamplesList = useMemo(() => {
    const { data } = correspondenceSamples.correspondenceSamplesList;
    if (data) return data;
  }, [correspondenceSamples.correspondenceSamplesList.data]);

  const deleteCorrespondenceSample = (event, correspondenceSample) => {
    event.stopPropagation();
    deleteCorrespondenceSampleAsync({ id: correspondenceSample.id });
  };

  const deleteCorrespondenceSampleLoading = useMemo(
    () => correspondenceSamples.deleteCorrespondenceSample.status === 'pending',
    [correspondenceSamples.deleteCorrespondenceSample.status],
  );

  //* Secretariats List
  const secretariatListHasAccess = useMemo(() => {
    const organizationSecretariats = secretariats.secretariatsList.data;
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;
    return useSecretariatHasAccess({
      accessibility,
      organizationId,
      organizationSecretariats,
      ownerId,
      secretariatsListSecurityDictionary:
        securityLevelDictionary.secretariatsListSecurityDictionary,
      solutionOrganizationRoles,
      userPermissions,
    });
  }, [
    secretariats.secretariatsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
  ]);

  //* Indicators List
  const indicatorsListHasAccess = useMemo(() => {
    const indicators = secretariats.indicatorsList.data;
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;
    return useIndicatorsHasAccess({
      accessibility,
      organizationId,
      secretariat: state.secretariat,
      indicators,
      ownerId,
      indicatorsListSecurityDictionary: securityLevelDictionary.indicatorsListSecurityDictionary,
      solutionOrganizationRoles,
      userPermissions,
    }).filter((i) => !i?.isDisabled);
  }, [
    secretariats.indicatorsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
  ]);

  //* Secretariats List for export ece
  const secretariatListForExportEce = useMemo(() => {
    const organizationSecretariats = secretariats.secretariatsList.data;
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;
    return useSecretariatHasAccess({
      accessibility,
      organizationId,
      organizationSecretariats,
      ownerId,
      secretariatsListSecurityDictionary: [securityKeys.CORRESPONDENCE.ECE.EXPORT_ECE],
      solutionOrganizationRoles,
      userPermissions,
    });
  }, [
    secretariats.secretariatsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
  ]);

  //* Indicators List
  const indicatorsListForExportEce = useMemo(() => {
    const indicators = secretariats.indicatorsList.data;
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;
    return useIndicatorsHasAccess({
      accessibility,
      organizationId,
      secretariat: state.secretariat,
      indicators,
      ownerId,
      indicatorsListSecurityDictionary: [securityKeys.CORRESPONDENCE.ECE.EXPORT_ECE],
      solutionOrganizationRoles,
      userPermissions,
    }).filter((i) => !i?.isDisabled);
  }, [
    secretariats.indicatorsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
  ]);

  const searchReferencesLettersLoading = useMemo(() => {
    const { status } = letters.searchReferencesLetters;
    return status === 'loading';
  }, [letters.searchReferencesLetters]);

  const genaiLoading = useMemo(() => {
    const { status } = genai.letterContentByTopic;
    return status === 'pending';
  }, [genai.letterContentByTopic]);

  //* filter secretariats based on active indicator
  const activeSecretariats = useMemo(() => {
    const data = secretariats?.secretariatsList.data || [];

    const filteredSecretariats = data.filter((secretariat) => {
      const indicators = secretariat.indicators || [];

      const hasActiveIndicator = indicators.some((indicator) => !indicator.isDisabled);

      return hasActiveIndicator;
    });

    return filteredSecretariats;
  }, [secretariats?.secretariatsList]);

  const currentSecretariat = activeSecretariats.find(
    (secretariatValue) => secretariatValue.id === secretariatSelection?.id,
  );

  //* ////////////////////////////////////////// functions /////////////////////////////////////////////////////
  //* ////////////////////////////Letter Ai Popover //////////////////////////////
  const handleClickLetterAiPopover = (event) => {
    setLetterAiAnchorEl(event.currentTarget);
  };

  const handleCloseLetterAiPopover = () => {
    setLetterAiAnchorEl(null);
    setTextFieldGenai('');
  };

  const handleToggleChangeTabGenai = (event, newValue) => {
    if (newValue !== null) {
      setSelectedTabCreateLetterByTopic(newValue);
    }
  };
  const openLetterAiAnchorElement = Boolean(letterAiAnchorEl);

  const submitCreateLetterByTopic = async (cmds) => {
    const option = {
      organizationId,
      topic: textFieldGenai,
      tone: selectedTabCreateLetterByTopic,
    };
    const result = await createLetterContentByTopicAsync(option);
    if (result.payload.data?.content) {
      cmds.addPlainText(result.payload.data.content);
    }
    result.payload.data.content && setTextFieldGenai('');
  };

  //* //////////////////////////// Letter Samples //////////////////////////////
  const handleCreateLetterSample = () => {
    createCorrespondenceSampleAsync({
      data: {
        id: uuidv4(),
        organization: {
          id: organizationId,
        },
        content: createLetterSampleState.content,
        title: createLetterSampleState.title,
        requester,
      },
    });
  };

  const handleOpenCreateLetterSampleDialog = () => {
    setCreateLetterSampleState({ content: state.body, title: '' });
    setOpenForCreateLetterSampleDialog(true);
  };

  const handleCloseCreateLetterSampleDialog = () => {
    setOpenForCreateLetterSampleDialog(false);
    setCreateLetterSampleState({ content: '', title: '' });
  };

  const handleOpenLetterSamplesListPopover = (event) => {
    setLetterSamplesListAnchorEl(event.currentTarget);
    correspondenceSamplesListAsync({
      organizationId,
      queryObject: { limit: 1000, offset: 0 },
    });
  };

  const handleCloseLetterSamplesPopover = () => {
    setLetterSamplesListAnchorEl(null);
  };

  const { uploadDocument: handleUploadDocument, uploadDocumentLoading } = useUploadDocument({
    documents: forwardLetterAttachments,
    meta: {
      domain: 'correspondence',
      position: JSON.stringify({ id: selectedPosition.position.id }),
      organization: JSON.stringify({ id: organizationId }),
    },
    headers: {
      domain: 'correspondence',
      position: selectedPosition.position.id,
      organization: organizationId,
    },
  });

  const {
    uploadDocument: handleUploadAdditionalAttachments,
    uploadDocumentLoading: handleUploadAdditionalAttachmentsLoading,
  } = useUploadDocument({
    documents: additionalAttachments,
    meta: {
      domain: 'correspondence',
      position: JSON.stringify({ id: selectedPosition.position.id }),
      organization: JSON.stringify({ id: organizationId }),
    },
    headers: {
      domain: 'correspondence',
      position: selectedPosition.position.id,
      organization: organizationId,
    },
  });

  const handleCreatedContact = () => {
    handleCloseContactDialog();
    setContacts([]);
    contactsOrganizationReset();
    getContactsOrganization();
  };

  const handleCloseContactDialog = () => {
    setQuicklyAddingContactDialog(() => ({ open: false, name: '' }));
  };

  const handleOpenContactDialog = (name) => {
    setQuicklyAddingContactDialog(() => ({ open: true, name }));
  };

  const pageLoading = loading || letters.selectedLetter.status === 'loading';

  const handleOpenCcDescription = (key, defaultValue) =>
    setCcDescriptionPanel({ open: true, key, value: defaultValue ?? '' });
  const handleCloseCcDescription = () => setCcDescriptionPanel({ open: false, key: 0, value: '' });
  const handleChangeCcDescription = (value) =>
    setCcDescriptionPanel((prevState) => ({ ...prevState, value }));

  const toggleVisibleLetterHistoryDialog = (isVisible) => {
    if (isVisible) {
      getLetterHistory();
    }
    setIsVisibleLetterHistoryDialog(isVisible);
    setAnchorElLetterInfoMenu(null);
  };
  const handleOpenPositionEmployeeTreeDialogRecipient = () => {
    setOpenPositionEmployeeTreeDialogRecipient(true);
  };
  const handleClosePositionEmployeeTreeDialogRecipient = () => {
    setOpenPositionEmployeeTreeDialogRecipient(false);
  };
  const handleOpenPositionEmployeeTreeDialogSender = () => {
    setOpenPositionEmployeeTreeDialogSender(true);
  };
  const handleClosePositionEmployeeTreeDialogSender = () => {
    setOpenPositionEmployeeTreeDialogSender(false);
  };
  const handleOpenPositionEmployeeTreeDialogCC = () => {
    setOpenPositionEmployeeTreeDialogCC(true);
  };
  const handleClosePositionEmployeeTreeDialogCC = () => {
    setOpenPositionEmployeeTreeDialogCC(false);
  };
  const handleOpenPositionEmployeeTreeDialogBCC = () => {
    setOpenPositionEmployeeTreeDialogBCC(true);
  };
  const handleClosePositionEmployeeTreeDialogBCC = () => {
    setOpenPositionEmployeeTreeDialogBCC(false);
  };
  const handleOpenPositionEmployeeTreeDialogSigner = () => {
    setOpenPositionEmployeeTreeDialogSigner(true);
  };
  const handleClosePositionEmployeeTreeDialogSigner = () => {
    setOpenPositionEmployeeTreeDialogSigner(false);
  };

  const handleOpenLetterInfoMenu = (e) => {
    setAnchorElLetterInfoMenu(e.currentTarget);
  };

  const handleCloseLetterInfoMenu = () => {
    setAnchorElLetterInfoMenu(null);
  };

  const getLetter = (composeLetterId) => {
    if (letterId || composeLetterId) {
      if (operationId) {
        return getLetterAsync({
          path: `${letterId || composeLetterId}`,
          queryObject: { operationId },
        });
      }
      return getLetterAsync({
        path: letterId || composeLetterId,
      });
    }
  };

  const getOrganizationPositions = () => {
    getOrganizationPositionsAsync({
      path: `organization/${organizationId}`,
      queryObject: { limit: 1000, offset: 0 },
    });
  };

  const getOrganizationEmployeeContacts = () => {
    const opts = {
      path: `${organizationId}/employee-contacts`,
      queryObject: { limit: 1000, offset: 0 },
    };
    organizationEmployeeContactsAsync(opts);
  };

  const getLetterHistory = () => {
    const opts = {
      path: `${letterId}/history`,
    };
    if (letters.letterHistory.data === null) {
      setLetterHistoryLoading(true);
    }
    getLetterHistoryAsync(opts);
  };

  const onChangeTab = (event, newValue) => {
    setTab(newValue);
  };

  const getCorrespondenceExchangeRegistryPermissions = () => {
    correspondenceExchangeRegistryPermissionsAsync({
      type: 'forward',
      organizationId,
      positionId: selectedPosition.position.id,
      slot: selectedPosition.position.slot,
    });
  };

  const readData = (data) => ({
    attachments: data ? data.attachments : [],
    bcc: data ? data.bcc : [],
    body: data ? data.body : '',
    cc: data ? data.cc : [],
    confidentiality: data ? data.confidentiality : {},
    date: data ? data.date : null,
    description: data ? data.description : '',
    id: data ? data._id : '',
    incomingNumber: data ? data.incomingNumber : '',
    incomingDate: data ? data.incomingDate : '',
    indicator: data ? { id: data.indicator?.id } : null,
    secretariat: data ? { id: data.secretariat?.id } : null,
    organization: data ? data.organization : {},
    priority: data ? data.priority : {},
    recipient: data ? data.recipient : [],
    referenceDate: data ? data.referenceDate : '',
    referenceNumber: data ? data.referenceNumber : '',
    referenceType: data ? data.type : '',
    references: data ? data.references : [],
    sender: data ? data.sender : [],
    signer: data ? data.signer : [],
    subject: data ? data.subject : '',
    type: data ? data.type : '',
    additionalAttachments: data ? data.additionalAttachments : [],
    extraData: data ? (data?.extraData ?? undefined) : undefined,
  });

  const resetIndicators = (secretariatId) => {
    getIndicatorsReset();
    if (secretariatId) {
      getIndicatorsAsync({ path: secretariatId });
    }
  };

  const fillData = (data) => {
    setSenderSelection(data.sender[0]);
    setRecipientSelection(data.recipient);
    setCcSelection(data?.cc);
    setBccSelection(data?.bcc);
    setSignerSelection(data?.signer);
    setSecretariatSelection(data?.secretariat);
    setIndicatorSelection(data?.indicator);
    setPrioritySelection(priorities.find((p) => p.value === data?.priority) || {});
    setConfidentialitySelection(
      confidentialities.find((s) => s.value === data?.confidentiality) || {},
    );
    setReferencesList(data?.references || []);
    setAttachments(
      data?.attachments.map((a) => ({
        ...a,
        isLetterContent: a.type === 'content',
      })),
    );
    ['recipient', 'cc', 'bcc', 'signer', 'sender'].map((x) => {
      onChange({
        target: { name: x, value: data[x]?.map((s) => s) },
      });
    });
    if (data.number && isNumberedNow) {
      setShowDialogNumberedLetter(true);
      setIsNumberedNow(false);
    }
    setAdditionalAttachments(data?.additionalAttachments);
  };

  const secretariatAdminPermissions = () => {
    if (initState.secretariat?.id) {
      getSecretariatAdminPermissions({
        organizationId,
        userId: signedInUser.id,
        secretariatId: initState.secretariat.id,
      });
    }
  };

  const getContactsOrganization = () => {
    contactsOrganizationAsync({
      path: `${organizationId}`,
      queryObject: {
        offset: 0,
        limit: 1000,
      },
    });
  };

  const onChange = (e) => {
    if (e.target.name === 'attachments') {
      setState((preState) => ({
        ...preState,
        attachments:
          e.target.changed === 'selected'
            ? preState.attachments.map((attach) => ({
                ...attach,
              }))
            : [...preState.attachments, e.target.value],
      }));
    } else {
      setState((preState) => ({ ...preState, [e.target.name]: e.target.value }));
    }
  };

  const scrollToChecked = () => {
    if (changedcheck?.checked) {
      switch (changedcheck.id) {
        case 'attachments':
          attachmentsRef.current?.scrollIntoView({ behavior: 'smooth' });
          break;
        case 'registrationInfo':
          registrationInfoRef.current?.scrollIntoView({ behavior: 'smooth' });
          break;
        case 'reference':
          referenceRef.current?.scrollIntoView({ behavior: 'smooth' });
          break;
        case 'additionalAttachments':
          additionalAttachmentsRef.current?.scrollIntoView({ behavior: 'smooth' });
          break;

        default:
          attachmentsRef.current?.scrollIntoView({ behavior: 'smooth' });
          break;
      }
    }
  };

  const handleChangeChecked = (e, item) => {
    setChangedcheck(item);
    setIsCheckedAll(true);
    if (item.id === 'allShow') {
      // eslint-disable-next-line no-param-reassign
      extraItems.map((x) => (x.checked = e.target.checked));
      setExtraItems((prevState) => [...prevState]);
    } else {
      const x = extraItems.find((y) => y.id === item.id);
      x.checked = e.target.checked;
      setExtraItems((prevState) => [...prevState]);
    }
  };

  const onChangeComposeInfoConfig = (name) => {
    setComposeInfoConfig((prev) => {
      const updatedConfig = { ...prev };
      if (name === 'allShow') {
        const newValue = !updatedConfig.allShow;
        Object.keys(updatedConfig).forEach((key) => {
          updatedConfig[key] = newValue;
        });
      } else {
        updatedConfig[name] = !updatedConfig[name];
        const { allShow, ...exceptAllShow } = updatedConfig;
        const allSelected = Object.values(exceptAllShow).every((value) => value);
        updatedConfig.allShow = !!allSelected;
      }
      return updatedConfig;
    });
  };

  const handleSubmitComposeInfoConfig = () => {
    setExtraItems((prevState) =>
      prevState.map((item) => ({
        ...item,
        checked: composeInfoConfig[item.id],
      })),
    );
    localStorage.setItem(localStoragePath, JSON.stringify(composeInfoConfig));
    handleComposeInfoConfigDialog();
  };

  const handleShowBcc = () => {
    setShowBcc(true);
  };

  const getSender = () => {
    if (state.sender?.length && typeOfLetter === 'incoming') {
      return [{ _id: state.sender[0]?._id }];
    }
    if (senderSelection?.position?.id && typeOfLetter !== 'incoming') {
      return [
        {
          user: senderSelection?.user?.id
            ? {
                id: senderSelection?.user?.id,
              }
            : undefined,
          position: {
            id: senderSelection?.position?.id,
            slot: senderSelection?.position?.slot,
          },
        },
      ];
    }
    return undefined;
  };

  const checkIncomingNumberRepetitive = () => {
    setOpenBackdrop(true);
    checkingIncomingNumberRepetitiveAsync({
      organizationId,
      incomingNumber: state.incomingNumber,
      incomingDate: state.incomingDate,
      senderId: state.sender[0]._id,
    });
  };

  useEffect(() => {
    if (letters.checkingIncomingNumberRepetitive.status === 'success') {
      setOpenBackdrop(false);
      if (letters.checkingIncomingNumberRepetitive.data?._id) {
        setShowDialogNumberRepetitive(() => true);
      } else {
        checkingIncomingNumberRepetitiveReset();
        submitSave();
      }
    }
  }, [letters.checkingIncomingNumberRepetitive]);

  const closeDialogNumberRepetitive = () => {
    setShowDialogNumberRepetitive(false);
  };

  const confirmIncomingNumberRepetitive = () => {
    setShowDialogNumberRepetitive(() => false);
    submitSave();
  };

  //* reference letter ---------------------------------------------------------------------
  const handleDeleteReferenceItem = (referenceItem) => {
    setReferencesList((prevState) => prevState.filter((a) => a !== referenceItem));
  };

  const handleShowReferenceLetter = () => {
    setShowFieldsReferenceLetter(true);
  };

  const handleCloseReferenceSearchPopover = () => {
    setReferenceSearchPopoverOpen(false);
  };

  const handleSelectReferenceSearchPopover = (letterReference) => {
    setReferenceSearchPopoverOpen(false);

    const { id, subject, operation } = letterReference;
    setNewReferencesItems((preState) => ({
      ...preState,
      letter: { id, subject },
    }));
  };

  const handleNavigateToReference = (letterId) => {
    if (letterId) {
      window.open(`/organizations/${organizationId}/correspondence/${letterId}`, '_blank');
    }
  };

  const resetSelectedReferenceLetter = () => {
    setNewReferencesItems((prevState) => ({ ...prevState, letter: null, referenceNumber: '' }));
  };

  const getReferenceLettersList = () => {
    if (newReferencesItems.referenceNumber) {
      setReferenceSearchPopoverOpen(true);

      const options = {
        referenceLetterNumber: newReferencesItems.referenceNumber,
        positionSlotId: `${selectedPosition.position.id}_${selectedPosition.position.slot?.join('-')}`,
      };
      searchReferencesLettersAsync(options);
    }
  };

  const getRequestData = () => {
    const dataRequestForUpdate = {
      id: type === 'create' ? uuidv4() : letterId,
      type: typeOfLetter,
      subject: state.subject,
      sender: getSender(),
      recipient: state.recipient?.length
        ? typeOfLetter !== 'outgoing'
          ? state.recipient.map((r) => ({
              position: {
                id: r.position.id,
                slot: r.position.slot,
              },
              user: r.user?.id
                ? {
                    id: r.user.id,
                  }
                : undefined,
            }))
          : state?.recipient?.map?.((v) => ({ _id: v._id }))
        : undefined,
      incomingNumber: state.incomingNumber,
      incomingDate: state.incomingDate || '',
      cc:
        typeOfLetter !== 'outgoing'
          ? state?.cc.map((c) => ({
              position: {
                id: c.position.id,
                slot: c.position.slot,
              },
              user: c.user?.id
                ? {
                    id: c.user.id,
                  }
                : undefined,
              note: c?.note ?? '',
            }))
          : state?.cc?.length
            ? state?.cc?.map?.((v) => ({
                _id: v?._id,
                note: v?.note ?? '',
              }))
            : [],
      bcc:
        typeOfLetter !== 'outgoing'
          ? state?.bcc?.map?.((bcc) => ({
              position: {
                id: bcc.position.id,
                slot: bcc.position.slot,
              },
              user: bcc.user?.id ? { id: bcc.user.id } : undefined,
            }))
          : state?.bcc?.length
            ? state?.bcc?.map((item) => ({
                _id: item._id,
              }))
            : [],
      date: state.date || '',
      signer: state?.signer?.length
        ? state.signer.map((s) => ({
            position: {
              id: s.position.id,
              slot: s.position.slot,
            },
            user: s.user?.id
              ? {
                  id: s.user.id,
                }
              : undefined,
          }))
        : [],
      body: state.body,
      confidentiality:
        state.confidentiality &&
        Object.keys(state.confidentiality).length === 0 &&
        Object.getPrototypeOf(state.confidentiality) === Object.prototype
          ? ''
          : state.confidentiality,
      priority:
        state.priority &&
        Object.keys(state.priority).length === 0 &&
        Object.getPrototypeOf(state.priority) === Object.prototype
          ? ''
          : state.priority,
      description: state.description,
      attachments: attachmentList?.map((item) => {
        const { isLetterContent, ...other } = item;
        return other;
      }),
      references: referencesList.map((value) => {
        if (value?.letter?.id) {
          const letterData = { id: value.letter?.id };
          return { ...value, letter: letterData };
        }

        const { letter: _, ...other } = value;
        return other;
      }),
      organization:
        type === 'create'
          ? {
              id: organizationId,
            }
          : undefined,
      secretariat: state.secretariat,
      indicator: state.indicator,
      requester,
      extraData: state.extraData ?? undefined,
    };

    const { secretariatId: __, ...dataRequestForSave } = dataRequestForUpdate;

    return type === 'create' ? dataRequestForSave : dataRequestForUpdate;
  };

  const save = async () => {
    const result = await handleUploadAdditionalAttachments(additionalAttachments);

    if (Boolean(additionalAttachments?.length ?? 0) && !result.success) {
      (type === 'create' ? setIsLetterCreating : setIsLetterUpdating)(false);
      setOpenBackdrop(false);
      setLoading(false);
      snackbarUtils.error(`${t('common.errors.uploadFailed')}`);
      return;
    }

    (type === 'create' ? createLetterAsync : updateLetterAsync)({
      data: {
        ...getRequestData(),
        additionalAttachments: result.uploadedList.map((item) => ({
          id: item.id,
          name: item.filename ?? item.name,
          size: item.size,
          uploader: !item.uploader
            ? {
                user: {
                  id: signedInUser.id,
                },
                position: {
                  id: selectedPosition.position.id,
                  slot: selectedPosition.position.slot,
                },
              }
            : {
                user: {
                  id: item.uploader.user?.id,
                },
                position: {
                  id: item?.uploader?.position?.id,
                  slot: item?.uploader?.position?.slot,
                },
              },
        })),
      },
      checkStatusOveredFunction: handleOpenCheckStatusOveredDialog,
    });
  };

  const submitSave = async () => {
    setShowDialogSignAndSaveOrCancel(false);
    setOpenBackdrop(true);

    (type === 'create' ? setIsLetterCreating : setIsLetterUpdating)(true);

    if (attachments.filter((a) => !a?.id)?.length) {
      setIsFileUploading(true);
      const optionsAttach = [];
      for (const attachment of attachments.filter((a) => !a.id)) {
        const data1 = new FormData();
        data1.append('file', attachment);

        data1.append('domain', 'correspondence');
        data1.append('organization', JSON.stringify({ id: organizationId }));
        data1.append('position', JSON.stringify({ id: selectedPosition?.position?.id }));

        const headers = {
          domain: 'correspondence',
          organization: organizationId,
          position: selectedPosition?.position?.id,
        };

        optionsAttach.push({
          correlationId: uuidv4(),
          data: data1,
          fileName: attachment.name,
          isLetterContent: !!attachment?.mustBeAsContent,
          headers,
        });
      }
      setDocCorrelationIds(
        optionsAttach.map((o) => ({
          docCorrelationId: o.correlationId,
          fileName: o.fileName,
          isLetterContent: !!o?.mustBeAsContent,
        })),
      );
      uploadDocumentsAsync(optionsAttach);
    } else {
      save();
    }
  };

  const isSaveAndSignRef = useRef(false);

  const submitSaveAndSign = () => {
    isSaveAndSignRef.current = true;
    submitSave();
  };

  const handleSignLetter = () => {
    setOpenBackdrop(true);
    signLetterAsync({
      data: {
        letter: {
          id: letterId,
        },
        signer: {
          user: {
            id: signedInUser.id,
          },
          position: {
            id: selectedPosition.position.id,
            slot: selectedPosition.position.slot,
          },
        },
      },
      checkStatusOveredFunction: handleOpenCheckStatusOveredDialog,
    });
  };

  const [{}, downloadAttch] = useAxios(
    {
      baseUrl: `${baseUrl}/api/v1/documents/`,
      method: 'Get',
      responseType: 'blob',
    },
    { manual: true },
  );

  const downloadBlob = (blob, filename) => {
    const a = document.createElement('a');
    a.style = 'display: none';
    document.body.appendChild(a);
    const url = window.URL.createObjectURL(blob);
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleDownloadAttachment = async (attachment) => {
    const res = await downloadAttch({
      url: `${baseUrl}/api/v1/documents/${attachment.id}`,
    });
    const blob = new Blob([res.data]);
    downloadBlob(blob, attachment.name);
  };

  const handleOpenDocumentPreviewer = (documents, currentIndex) => {
    setOpenDocumentPreviewerDialog({ open: true, documents, currentIndex });
  };

  const handleCloseDocumentPreviewer = () => {
    setOpenDocumentPreviewerDialog({ open: false, documents: [], currentIndex: null });
  };

  const handlePrint = async () => {
    setOpenBackdrop(true);
    setOpenPrintDialog(false);
    const res = await downloadLetter({
      data: {
        id: letterId,
        layout: selectedLetterLayout === 'no-layout' ? null : { id: selectedLetterLayout },
        secretariatId: secretariatSelection?.id,
        options: {
          noSignature: !checkedSign,
          noStamps: !checkedStamps,
        },
      },
    });
    const blob = new Blob([res.data], { type: 'pdf' });
    downloadBlob(blob, 'letter.pdf');
    setOpenBackdrop(false);
    setSelectedLetterLayout(null);
    getLetterLayoutsReset();
  };

  const handlePrintEce = async () => {
    setOpenBackdrop(true);
    const res = await downloadLetterEce({ data: { id: letterId } });
    const blob = new Blob([res.data], { type: 'application/xml' });
    downloadBlob(blob, 'ECE.xml');
    setOpenBackdrop(false);
  };

  const setLetterNumberAfterSave = () => {
    const options = {
      data: {
        id: letterId,
        secretariatId: secretariatSelection?.id,
        requester,
      },
      checkStatusOveredFunction: handleOpenCheckStatusOveredDialog,
    };
    setLetterNumberAsync(options);
  };

  const handleReceipt = () => {
    navigate(`receipts?type=${typeOfLetter}&secretariatId=${secretariatSelection.id}`);
  };

  const handleCloseNumberedLetter = () => {
    setShowDialogNumberedLetter(false);
  };

  const acceptSaveAndSetLetterNumber = () => {
    setLoadingLetterNumber(true);
    setIsCallSetLetterNumber(true);
    isTouched ? submitSave() : setLetterNumberAfterSave();
  };

  const handleCloseSaveAndSetLetterNumber = () => {
    setIsCallSetLetterNumber(false);
    setShowDialogSaveAndSetLetterNumber(false);
  };

  const onSetLetterNumber = () => {
    handleCloseExtraButton();
    setShowDialogSaveAndSetLetterNumber(true);
  };

  const goBack = () => {
    parseEceReset();
    navigate(-1);
  };

  const handleOpenAddReferenceDrawer = (st) => {
    setOpenAddReferenceDrawer(st);
  };

  const handleOpenExtraButton = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseExtraButton = (event) => {
    setAnchorEl(null);
  };

  const handleCloseDetails = () => {
    setOpenDetails(false);
  };

  const handleOpenDetails = () => {
    setOpenDetails(true);
    setAnchorElLetterInfoMenu(null);
  };

  const isAdditionalAttachmentEditable = () => {
    if (type !== 'create' && !selectedLetter?.isInCartable) {
      return false;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return false;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return true;
  };

  const isLetterEditable = () => {
    if (type !== 'create' && !selectedLetter?.isInCartable) {
      return true;
    }
    if (selectedLetter?.signedBy?.length > 0 || selectedLetter?.number) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const isLetterEditableNote = () => {
    if (type !== 'create' && !selectedLetter?.isInCartable) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const isLetterEditableAttachmentAsContent = () => {
    if (type !== 'create' && !selectedLetter?.isInCartable) {
      return false;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return false;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return true;
  };

  const isLetterDeletableAttachmentAsContent = (file) => {
    const isLetterContent = file?.mustBeAsContent || file?.isLetterContent;
    if (isLetterEditableAttachmentAsContent() && !isLetterContent) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const isLetterEditableIndicator = () => {
    if (type !== 'create' && !selectedLetter?.isInCartable) {
      return true;
    }
    if (selectedLetter?.number) {
      return true;
    }
    if (
      selectedLetter?.operations?.length &&
      selectedLetter?.operations?.some(
        (f) => (f.type === 'discard' || f.type === 'void') && f.status === 'success',
      )
    ) {
      return true;
    }
    if (selectedLetter?.status === 'void') {
      return true;
    }
    return false;
  };

  const isIncomingNumberAndDateDisable = () => isLetterEditable() || isEceLetter;

  const onForward = () => {
    setOpenForwardLetter(true);
    correspondenceExchangeRegistryPermissionsReset();
    getCorrespondenceExchangeRegistryPermissions();
  };

  const selectForwardRecipientsAutomatically = useCallback(() => {
    if (
      selectedLetter.type === 'internal' &&
      !selectedLetter.operations?.filter((operation) => operation.type === 'forward')?.length
    ) {
      const positionSlotsAccess =
        correspondenceExchangeRegistry.exchangeRegistryPermissions.data?.reduce?.(
          (result, currentItem) => {
            const recipientsKeys = currentItem.employees.reduce(
              (result, item) => ({
                ...result,
                [`${currentItem.id}_${item.slot?.join('-')}`]: Boolean(item.allowed),
              }),
              {},
            );

            return { ...result, ...recipientsKeys };
          },
          {},
        ) ?? {};

      const recipients = selectedLetter.recipient
        .filter(
          (currentItem) =>
            positionSlotsAccess[
              `${currentItem.position.id}_${currentItem.position.slot?.join('-')}`
            ],
        )
        .map((item) => {
          const { organization, ...other } = item;
          return other;
        });

      if (selectedLetter.number) {
        handleChangeForwardRecipient(null, recipients);
      } else if (recipients.length === 1) {
        handleChangeForwardRecipient(null, recipients[0]);
      }
    }
  }, [selectedLetter, correspondenceExchangeRegistry.exchangeRegistryPermissions.data]);

  const handleCloseForwardLetter = () => {
    setOpenForwardLetter(false);
    setForwardedRecipientList([]);
    setForwardType('normal');
    setParaph('');
    setForwardRecipientSelection([]);
  };

  const handleChangeForwardTypes = (event) => {
    setForwardType(event.target.value);
  };

  const toggleDialogParaphList = (isOpen) => {
    setOpenPreParaphList(isOpen);
    if (!isOpen) {
      forwardNotesSamplesReset();
      setSelectedRadio({});
      setTabPreParaphValue('forwardNotesSamples');
    }
  };

  const handleChangeForwardLetterTag = (event, tagValue) => {
    setForwardLetterTag(tagValue);
  };

  const getLetterLayouts = () => {
    if (secretariats.letterLayoutsList.data === null) {
      setLetterLayoutsLoading(true);
    }
    const options = {
      secretariatId: secretariatSelection?.id,
    };
    getLetterLayoutsAsync(options);
    setOpenPrintDialog(true);
  };

  const handleClosePrintDialog = () => {
    setOpenPrintDialog(false);
    setSelectedLetterLayout('no-layout');
  };

  const handleChangeSelectedLetterLayout = (e) => {
    setSelectedLetterLayout(e.currentTarget.value);
  };

  const handleOpenSendECEDialog = () => {
    if (secretariats.letterLayoutsList.data === null) {
      setLetterLayoutsLoading(true);
    }
    const options = {
      secretariatId: secretariatSelection?.id,
    };
    getLetterLayoutsAsync(options);
    setOpenSendECEDialog(true);
  };

  const handleCloseSendECEDialog = () => {
    setOpenSendECEDialog(false);
    setSelectedLetterLayout('no-layout');
    setEceEmailRecipient(null);
  };

  const handleSendECE = () => {
    sendECEReset();
    const opts = {
      data: {
        letterId,
        address: eceEmailRecipient,
        layoutId: selectedLetterLayout === 'no-layout' ? undefined : selectedLetterLayout,
        secretariatId: secretariatSelection?.id,
        requester,
      },
      checkStatusOveredFunction: handleOpenCheckStatusOveredDialog,
    };
    setSendECELoading(true);
    sendECEAsync(opts);
  };

  const onChangeEceEmailRecipient = (e) => {
    const { value } = e.currentTarget;
    setEceEmailRecipient(value);
    if (value === '') {
      setECEEmailRecipientErrorText(true);
    } else {
      setECEEmailRecipientErrorText(false);
    }
  };

  const handleOpenExportECEDialog = () => {
    if (secretariats.letterLayoutsList.data === null) {
      setLetterLayoutsLoading(true);
    }
    const options = {
      secretariatId: secretariatSelection?.id,
    };
    getLetterLayoutsAsync(options);
    setOpenExportECEDialog(true);
  };

  const handleCloseExportECEDialog = () => {
    setOpenExportECEDialog(false);
    setSelectedLetterLayout('no-layout');
  };

  const handleExportECE = async () => {
    setExportECELoading(true);

    try {
      const res = await downloadLetterEce({
        data: {
          id: letterId,
          layout: selectedLetterLayout === 'no-layout' ? undefined : { id: selectedLetterLayout },
          secretariatId: secretariatSelection?.id,
        },
      });
      const blob = new Blob([res.data], { type: 'application/xml' });
      downloadBlob(blob, 'ECE.xml');
      setOpenExportECEDialog(false);
    } finally {
      setExportECELoading(false);
    }
  };

  const handleChangeTabPreParaph = (event, newValue) => {
    setTabPreParaphValue(newValue);
  };

  const handleChangePreParaphRadio = (tabKey) => (event) => {
    const id = event.target.value;
    setSelectedRadio({ [tabKey]: id });
  };

  const handleClickPreParaph = () => {
    const tabKey = Object.keys(selectedRadio)[0];
    const selectedId = selectedRadio[tabKey] || '';

    let note = selectedId;

    if (tabKey === 'forwardNotesSamples') {
      const selectedItem = forwardNotesSamples.find((item) => item.id === selectedId);
      if (selectedItem) note = selectedItem.note;
    }

    setParaph((prev) => (prev ? `${prev} ${note}` : note));

    toggleDialogParaphList(false);
  };

  const deleteForwardListItem = (item) => {
    setForwardedRecipientList((prevState) => prevState.filter((a) => a !== item));
  };

  const onSubmitForwardRecipient = (checkboxValue) => {
    setForwardRecipientSelection(checkboxValue);
  };

  const handleChangeForwardRecipient = (e, newValue) => {
    setForwardRecipientSelection(newValue);
  };

  const addToForwardList = async () => {
    setForwardAttachmentsLoading(true);
    setForwardLetterLoading(true);
    const info = {
      forwardRecipientSelection,
      forwardType,
      paraph,
      forwardLetterTag,
      attachments: forwardLetterAttachments,
    };

    const data = {
      data: {
        letter: {
          id: letterId,
        },
        parentId: operationInfo?._id || selectedLetter?.operations[0]?._id,
        sender: [
          {
            user: {
              id: signedInUser.id,
            },
            position: {
              id: selectedPosition.position.id,
              slot: selectedPosition.position.slot,
            },
          },
        ],
        recipients: !selectedLetter?.number
          ? [info.forwardRecipientSelection]
          : info.forwardRecipientSelection,

        forwardType,
        forwardNote: info.paraph,
        subject: info.forwardLetterTag || null,
        attachments: info.attachments,
      },
    };

    setOpenForwardListMobileDialog(true);
    setForwardType('normal');
    setParaph('');
    setForwardRecipientSelection([]);
    setForwardLetterTag('');
    setForwardedRecipientList((prevState) => [...prevState, data]);
    setForwardLetterAttachments([]);
    setForwardAttachmentsLoading(false);
    setForwardLetterLoading(false);
  };

  useEffect(() => {
    if (!selectedLetter?.number && forwardedRecipientList.length) {
      handleForwardLetter();
    }
  }, [forwardedRecipientList]);

  const compareSlots = (a, b) =>
    Array.isArray(a) &&
    Array.isArray(b) &&
    a.length === b.length &&
    a.every((val, index) => val === b[index]);

  const accessibleOperations = (operations) =>
    operations.filter(
      (operation) =>
        operation?.recipient?.position?.id === selectedPosition.position.id &&
        compareSlots(operation?.recipient?.position?.slot, selectedPosition.position.slot),
    );

  const handleForwardLetter = () => {
    setForwardLetterLoading(true);
    forwardedRecipientList.map(async (opts) => {
      try {
        const result = await handleUploadDocument(opts.data.attachments);
        if (opts.data.attachments && !result.success) {
          setForwardLetterLoading(false);
          return;
        }

        const options = {
          data: {
            ...opts.data,
            parentId: selectedLetter.isInCartable
              ? opts?.data?.parentId
              : selectedLetter?.theOperation?.parentId,
            recipients: selectedLetter?.number
              ? opts.data.recipients.map((f) => ({
                  position: {
                    id: f.position.id,
                    slot: f.position.slot,
                  },
                  user: f.user?.id
                    ? {
                        id: f.user.id,
                      }
                    : undefined,
                }))
              : [
                  {
                    position: {
                      id: opts.data.recipients[0].position?.id,
                      slot: opts.data.recipients[0].position.slot,
                    },
                    user: opts.data.recipients[0]?.user?.id
                      ? {
                          id: opts.data.recipients[0].user.id,
                        }
                      : undefined,
                  },
                ],
            attachments: result?.uploadedList?.map((item) => ({
              id: item.id,
              name: item.filename,
              size: item.size,
            })),
          },
          checkStatusOveredFunction: handleOpenCheckStatusOveredDialog,
        };
        forwardLetterAsync(options);
      } catch {}
    });
  };

  //------------------------------------------------------------------
  const handleToggleDialogTerminate = (open) => {
    setShowDialogTerminate(open);
  };

  const handleToggleDialogDiscard = (open) => {
    setShowDialogDiscard(open);
  };

  const handleChangeTerminateDescriptionVal = (e) => {
    setTerminateDescriptionVal(e.target.value);
  };

  const handleChangeDiscardDescriptionVal = (e) => {
    setDiscardDescriptionVal(e.target.value);
  };

  const terminateLetter = () => {
    setIsLoadingTerminate(true);
    terminateLetterAsync({
      data: {
        letterId,
        parentId: operationInfo?._id || selectedLetter?.operations[0]?._id || null,
        terminator: {
          user: {
            id: signedInUser.id,
          },
          position: {
            id: selectedPosition.position.id,
            slot: selectedPosition.position.slot,
          },
        },
        note: terminateDescriptionVal,
      },
      checkStatusOveredFunction: handleOpenCheckStatusOveredDialog,
    });
  };

  const discardLetter = () => {
    setIsLoadingDiscard(true);
    discardLetterAsync({
      data: {
        id: letterId,
        requester,
        description: discardDescriptionVal,
      },
      checkStatusOveredFunction: handleOpenCheckStatusOveredDialog,
    });
  };

  //* ece receipt
  const sendEceReceipt = async () => {
    try {
      setOpenBackdrop(() => true);
      const result = await eceReceiptAsync({ letterId, requester });
      if (result?.payload?.success) {
        snackbarUtils.success(t('letter.eceLetters.post-receipt-ece-success'));
      } else {
        snackbarUtils.error(t('letter.eceLetters.post-receipt-ece-failed'));
      }
    } finally {
      setOpenBackdrop(() => false);
    }
  };

  //* void letter
  const handleOpenVoidLetterDialog = () => {
    setVoidLetterDialog(true);
  };

  const handleCloseVoidLetterDialog = () => {
    setVoidLetterDialog(false);
    setVoidLetterNote(null);
  };

  const handleCloseErrorVoidLetterDialog = () => {
    setErrorVoidLetterDialog(false);
  };

  const submitVoidLetter = () => {
    setVoidLetterLoading(true);
    const data = {
      id: letterId,
      requester,
      note: voidLetterNote,
    };
    voidLetterAsync(data);
  };

  const handleErrorOfVoidOperationText = () => {
    const error = letters?.voidLetter?.data?.meta.messages[0].exception;
    setVoidLetterDialog(false);
    setErrorVoidLetterDialog(true);
    setVoidLetterLoading(false);
    setErrorOfVoidOperationText('');

    switch (error) {
      case 'LetterHasReceiptException':
        return setErrorOfVoidOperationText(
          t('correspondence.letters.void-letter.messages.LetterHasReceiptException'),
        );
      case 'LetterExposedByEceException':
        return setErrorOfVoidOperationText(
          t('correspondence.letters.void-letter.messages.LetterExposedByEceException'),
        );
      case 'LetterAlreadyVoidedException':
        return setErrorOfVoidOperationText(
          t('correspondence.letters.void-letter.messages.LetterAlreadyVoidedException'),
        );
      case 'LetterIsVoidException':
        return setErrorOfVoidOperationText(
          t('correspondence.letters.void-letter.messages.LetterIsVoidException'),
        );
      default:
        return setErrorOfVoidOperationText('');
    }
  };

  const onChangeValueNote = (e) => {
    setVoidLetterNote(e.target.value);
  };

  const handleComposeInfoConfigDialog = () => {
    setIsOpenComposeInfoConfig((prev) => !prev);
  };

  //* create forward letter sample
  const forwardNoteSamplesAsync = () => {
    getForwardNotesSamplesAsync({
      queryObject: { requester, limit: 1000, offset: 0 },
    });
  };

  const handleDeleteForwardNotesSamples = async (id) => {
    setDeleteForwardNoteSampleLoading(id);
    const options = {
      id,
      requester,
    };

    try {
      await deleteForwardNotesSamplesAsync(options);
    } finally {
      setDeleteForwardNoteSampleLoading(null);
    }
  };

  const toggleDialogCreateParaph = (isOpen) => {
    setOpenPreCreateParaph(isOpen);
  };

  const handleCreateForwardNotesSample = async () => {
    setIsSubmittingForwardNoteSample(true);
    const options = {
      id: uuidv4(),
      note: paraph,
      requester,
    };

    try {
      await createForwardNotesSampleAsync(options);
      toggleDialogCreateParaph(false);
    } finally {
      setIsSubmittingForwardNoteSample(false);
    }
  };

  return (
    <LetterValidation
      {...{
        handleOpenCcDescription,
        handleCloseCcDescription,
        handleChangeCcDescription,
        ccDescriptionPanel,
        security,
        letterId,
        senderSelection,
        setSenderSelection,
        recipientSelection,
        setRecipientSelection,
        ccSelection,
        setCcSelection,
        bccSelection,
        setBccSelection,
        signerSelection,
        setSignerSelection,
        secretariatSelection,
        setSecretariatSelection,
        indicatorSelection,
        setIndicatorSelection,
        indicatorsActived,
        prioritySelection,
        setPrioritySelection,
        confidentialitySelection,
        setConfidentialitySelection,
        onSetLetterNumber,
        loading: pageLoading,
        type,
        letters,
        extraItems,
        typeOfLetter,
        users,
        contacts,
        secretariats,
        initialState: initState,
        state,
        onChange,
        openBackdrop,
        handleChangeChecked,
        goBack,
        priorities,
        confidentialities,
        referenceTypes,
        showBcc,
        handleShowBcc,
        handleDeleteReferenceItem,
        isBlocking,
        setIsBlocking,
        referencesList,
        setReferencesList,
        resetIndicators,
        openDetails,
        handleCloseDetails,
        handleOpenDetails,
        selectedLetter,
        submitSave,
        handleSignLetter,
        handleReceipt,
        handlePrint,
        handleDownloadAttachment,
        getRequestData,
        tab,
        onChangeTab,
        letterTypes,
        openAddReferenceDrawer,
        handleOpenAddReferenceDrawer,
        anchorEl,
        openExtraButton,
        handleOpenExtraButton,
        handleCloseExtraButton,
        isLetterEditable,
        isLetterEditableNote,
        isLetterEditableAttachmentAsContent,
        isLetterDeletableAttachmentAsContent,
        isLetterEditableIndicator,
        isAdditionalAttachmentEditable,
        showDialogSaveAndSetLetterNumber: showDialogSaveAndSetLetterNumber && security.number.allow,
        handleCloseSaveAndSetLetterNumber,
        acceptSaveAndSetLetterNumber,
        showDialogNumberedLetter,
        showDialogNumberedErrorForIndicatorStatus,
        setShowDialogNumberedErrorForIndicatorStatus,
        handleCloseNumberedLetter,
        loadingLetterNumber,
        additionalAttachmentsRef,
        referenceRef,
        registrationInfoRef,
        attachmentsRef,
        openPositionEmployeeTreeDialogRecipient,
        handleOpenPositionEmployeeTreeDialogRecipient,
        handleClosePositionEmployeeTreeDialogRecipient,
        openPositionEmployeeTreeDialogSender,
        handleOpenPositionEmployeeTreeDialogSender,
        handleClosePositionEmployeeTreeDialogSender,
        onForward,
        openForwardLetter,
        handleCloseForwardLetter,
        handleChangeForwardTypes,
        forwardType,
        paraph,
        setParaph,
        openPreParaphList,
        toggleDialogParaphList,
        forwardedRecipientList,
        tabPreParaphValue,
        handleChangeTabPreParaph,
        handleClickPreParaph,
        deleteForwardListItem,
        organizationId,
        positions,
        employees,
        openPositionEmployeeTreeDialogCC,
        openPositionEmployeeTreeDialogBCC,
        openPositionEmployeeTreeDialogSigner,
        handleOpenPositionEmployeeTreeDialogCC,
        handleClosePositionEmployeeTreeDialogCC,
        handleOpenPositionEmployeeTreeDialogBCC,
        handleClosePositionEmployeeTreeDialogBCC,
        handleOpenPositionEmployeeTreeDialogSigner,
        handleClosePositionEmployeeTreeDialogSigner,
        handleChangeForwardRecipient,
        onSubmitForwardRecipient,
        forwardRecipientSelection,
        addToForwardList,
        handleForwardLetter,
        forwardLetterLoading,
        forwardLetterTag,
        forwardLetterAttachments,
        setForwardLetterAttachments,
        handleChangeForwardLetterTag,
        setOpenPositionEmployeeTreeDialogRecipient,
        openForwardDialogRecipient,
        setOpenForwardDialogRecipient,
        operationInfo,
        handleToggleDialogTerminate,
        handleToggleDialogDiscard,
        showDialogTerminate,
        terminateDescriptionVal,
        handleChangeTerminateDescriptionVal,
        terminateLetter,
        isLoadingTerminate,
        showDialogDiscard,
        discardDescriptionVal,
        handleChangeDiscardDescriptionVal,
        discardLetter,
        isLoadingDiscard,
        openForwardListMobileDialog,
        setOpenForwardListMobileDialog,
        attachments,
        setAttachments,
        anchorElLetterInfoMenu,
        openLetterInfoMenu,
        handleOpenLetterInfoMenu,
        handleCloseLetterInfoMenu,
        isVisibleLetterHistoryDialog,
        toggleVisibleLetterHistoryDialog,
        letterHistory,
        letterHistoryLoading,
        setLetterHistoryLoading,
        isTouched,
        setIsTouched,
        letterNumber: selectedLetter?.number,
        getLetterLayouts,
        letterLayoutsLoading,
        openPrintDialog,
        handleClosePrintDialog,
        letterLayoutsList,
        selectedLetterLayout,
        handleChangeSelectedLetterLayout,
        openSendECEDialog,
        handleOpenSendECEDialog,
        handleCloseSendECEDialog,
        eceEmailRecipient,
        onChangeEceEmailRecipient,
        handleSendECE,
        sendECELoading,
        openExportECEDialog,
        handleOpenExportECEDialog,
        handleCloseExportECEDialog,
        handleExportECE,
        exportECELoading,
        ECEEmailRecipientErrorText,
        setECEEmailRecipientErrorText,
        submitSaveAndSign,
        showDialogSignAndSaveOrCancel,
        setShowDialogSignAndSaveOrCancel,
        selectedPosition,
        handleCreatedContact,
        handleCloseContactDialog,
        handleOpenContactDialog,
        quicklyAddingContactDialog,
        setContacts,
        checkedSign,
        setCheckedSign,
        handleUploadDocument,
        uploadDocumentLoading,
        forwardAttachmentsLoading,
        additionalAttachments,
        setAdditionalAttachments,
        forwardRecipient,
        voidLetterDialog,
        handleCloseVoidLetterDialog,
        handleOpenVoidLetterDialog,
        onChangeValueNote,
        voidLetterNote,
        submitVoidLetter,
        voidLetterLoading,
        errorOfVoidOperationText,
        errorVoidLetterDialog,
        handleCloseErrorVoidLetterDialog,
        secretariatListHasAccess,
        indicatorsListHasAccess,
        secretariatListForExportEce,
        indicatorsListForExportEce,
        setInitState,
        createLetterSampleState,
        setCreateLetterSampleState,
        handleCreateLetterSample,
        openForCreateLetterSampleDialog,
        handleOpenCreateLetterSampleDialog,
        handleCloseCreateLetterSampleDialog,
        loadingCreateLetterSample,
        letterSamplesListAnchorElAnchorEl,
        handleOpenLetterSamplesListPopover,
        handleCloseLetterSamplesPopover,
        correspondenceSamplesListLoading,
        correspondenceSamplesList,
        deleteCorrespondenceSample,
        setLetterSamplesListAnchorEl,
        deleteCorrespondenceSampleLoading,
        openDocumentPreviewerDialog,
        handleCloseDocumentPreviewer,
        handleOpenDocumentPreviewer,
        showFieldsReferenceLetter,
        handleShowReferenceLetter,
        getReferenceLettersList,
        searchReferencesLettersLoading,
        referenceLetterNumberList,
        newReferencesItems,
        setNewReferencesItems,
        setReferenceSearchPopoverOpen,
        referenceSearchPopoverOpen,
        handleCloseReferenceSearchPopover,
        handleSelectReferenceSearchPopover,
        handleNavigateToReference,
        resetSelectedReferenceLetter,
        checkedStamps,
        setCheckedStamps,
        sendEceReceipt,
        composeInfoConfig,
        isOpenComposeInfoConfig,
        handleComposeInfoConfigDialog,
        handleSubmitComposeInfoConfig,
        onChangeComposeInfoConfig,
        openLetterAiAnchorElement,
        handleCloseLetterAiPopover,
        handleClickLetterAiPopover,
        letterAiAnchorEl,
        selectedTabCreateLetterByTopic,
        handleToggleChangeTabGenai,
        setTextFieldGenai,
        submitCreateLetterByTopic,
        genaiLoading,
        textFieldGenai,
        isEceLetter,
        isIncomingNumberAndDateDisable,
        checkIncomingNumberRepetitive,
        confirmIncomingNumberRepetitive,
        showDialogNumberRepetitive,
        setShowDialogNumberRepetitive,
        closeDialogNumberRepetitive,
        activeSecretariats,
        currentSecretariat,
        forwardNotesSamples,
        handleDeleteForwardNotesSamples,
        handleChangePreParaphRadio,
        selectedRadio,
        toggleDialogCreateParaph,
        openPreCreateParaph,
        handleCreateForwardNotesSample,
        isSubmittingForwardNoteSample,
        deleteForwardNoteSampleLoading,
        isLoadingForwardNoteSamples,
        validForwardNotesSample,
        setValidForwardNotesSample,
      }}
    />
  );
};

const mapStateToProps = (state) => {
  const {
    letters,
    secretariats,
    documents,
    positions,
    employees,
    eceMails,
    correspondenceSamples,
    [correspondenceExchangeRegistryReducerName]: correspondenceExchangeRegistry,
    organization,
    correspondenceAuthz,
    genai,
  } = state;
  return {
    eceMails,
    letters,
    secretariats,
    documents,
    positions,
    employees,
    correspondenceSamples,
    correspondenceExchangeRegistry,
    organization,
    correspondenceAuthz,
    genai,
  };
};

Letter.propTypes = {
  letters: PropTypes.shape({
    contactsOrganization: PropTypes.shape({
      data: PropTypes.arrayOf(PropTypes.shape({})),
    }),
    createLetter: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
        message: PropTypes.string,
        data: PropTypes.shape({
          id: PropTypes.string,
          type: PropTypes.string,
        }),
      }),
    }),
    updateLetter: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
        message: PropTypes.string,
      }),
    }),
    signLetter: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
        message: PropTypes.string,
      }),
    }),
    letterNumber: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
        message: PropTypes.string,
      }),
    }),
    selectedLetter: PropTypes.shape({
      data: PropTypes.shape({}),
    }),
    discardLetter: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
      }),
    }),
    terminateLetter: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
      }),
    }),
    forwardLetter: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
      }),
    }),
    parseEce: PropTypes.shape({
      data: PropTypes.shape({}),
    }),
    letterHistory: {
      data: PropTypes.arrayOf(PropTypes.shape({})),
    },
  }),
  secretariats: PropTypes.shape({}).isRequired,
  documents: PropTypes.shape({
    documents: PropTypes.shape({
      inserting: PropTypes.shape({
        data: PropTypes.shape({
          id: PropTypes.string,
          filename: PropTypes.string,
          size: PropTypes.string,
        }),
      }),
    }),
  }).isRequired,
  getOrganizationPositionsAsync: PropTypes.func.isRequired,
  organizationEmployeeContactsAsync: PropTypes.func.isRequired,
  contactsOrganizationAsync: PropTypes.func.isRequired,
  getSecretariatsAsync: PropTypes.func.isRequired,
  getSecretariatsReset: PropTypes.func.isRequired,
  getIndicatorsAsync: PropTypes.func.isRequired,
  getIndicatorsReset: PropTypes.func.isRequired,
  contactsOrganizationReset: PropTypes.func.isRequired,
  getLetterReset: PropTypes.func.isRequired,
  createLetterAsync: PropTypes.func.isRequired,
  updateLetterAsync: PropTypes.func.isRequired,
  setLetterNumberAsync: PropTypes.func.isRequired,
  signLetterAsync: PropTypes.func.isRequired,
  uploadDocumentsAsync: PropTypes.func.isRequired,
  getLetterAsync: PropTypes.func.isRequired,
  updateLetterReset: PropTypes.func.isRequired,
  signLetterReset: PropTypes.func.isRequired,
  createLetterReset: PropTypes.func.isRequired,
  parseEceReset: PropTypes.func.isRequired,
  setLetterNumberReset: PropTypes.func.isRequired,
  composeEce: PropTypes.func.isRequired,
  positions: PropTypes.shape({
    positionsUser: PropTypes.shape({
      data: PropTypes.arrayOf(PropTypes.shape({})),
    }),
  }).isRequired,
  employees: PropTypes.shape({
    organizationEmployeeContacts: PropTypes.shape({
      data: PropTypes.arrayOf(PropTypes.shape({})),
    }),
  }).isRequired,
  forwardLetterAsync: PropTypes.func.isRequired,
  lettersNotExchangedReset: PropTypes.func.isRequired,
  forwardedLetterToMeReset: PropTypes.func.isRequired,
  forwardedLetterByMeReset: PropTypes.func.isRequired,
  forwardLetterReset: PropTypes.func.isRequired,
  terminateLetterAsync: PropTypes.func.isRequired,
  discardLetterAsync: PropTypes.func.isRequired,
  terminateLetterReset: PropTypes.func.isRequired,
  discardLetterReset: PropTypes.func.isRequired,
  getOrganizationPositionsReset: PropTypes.func.isRequired,
  organizationEmployeeContactsReset: PropTypes.func.isRequired,
  getLetterHistoryAsync: PropTypes.func.isRequired,
  letterHistoryReset: PropTypes.func.isRequired,
  security: propTypesLetterComposeSecurity.isRequired,
  getLetterLayoutsAsync: PropTypes.func.isRequired,
  getLetterLayoutsReset: PropTypes.func.isRequired,
  sendECEAsync: PropTypes.func.isRequired,
  sendECEReset: PropTypes.func.isRequired,
  searchReferencesLettersAsync: PropTypes.func.isRequired,
  eceReceiptAsync: PropTypes.func.isRequired,
  createForwardNotesSampleAsync: PropTypes.func.isRequired,
  getForwardNotesSamplesAsync: PropTypes.func.isRequired,
  deleteForwardNotesSamplesAsync: PropTypes.func.isRequired,
};

Letter.defaultProps = {
  letters: {},
};

const LetterWithRedux = connect(mapStateToProps, {
  getOrganizationPositionsAsync: getOrganizationPositionsAsync_,
  organizationEmployeeContactsAsync: organizationEmployeeContactsAsync_,
  getOrganizationPositionsReset: getOrganizationPositionsReset_,
  organizationEmployeeContactsReset: organizationEmployeeContactsReset_,
  contactsOrganizationAsync: contactsOrganizationAsync_,
  contactsOrganizationReset: contactsOrganizationReset_,
  getSecretariatsAsync: getSecretariatsAsync_,
  getSecretariatsReset: getSecretariatsReset_,
  getIndicatorsAsync: getIndicatorsAsync_,
  getIndicatorsReset: getIndicatorsReset_,
  getLetterReset: getLetterReset_,
  createLetterAsync: createLetterAsync_,
  updateLetterAsync: updateLetterAsync_,
  setLetterNumberAsync: setLetterNumberAsync_,
  signLetterAsync: signLetterAsync_,
  uploadDocumentsAsync: uploadDocumentsAsync_,
  getLetterAsync: getLetterAsync_,
  updateLetterReset: updateLetterReset_,
  signLetterReset: signLetterReset_,
  createLetterReset: createLetterReset_,
  parseEceReset: parseEceReset_,
  setLetterNumberReset: setLetterNumberReset_,
  composeEce: composeEce_,
  forwardLetterAsync: forwardLetterAsync_,
  forwardLetterReset: forwardLetterReset_,
  forwardedLetterByMeReset: forwardedLetterByMeReset_,
  forwardedLetterToMeReset: forwardedLetterToMeReset_,
  lettersNotExchangedReset: lettersNotExchangedReset_,
  terminateLetterReset: terminateLetterReset_,
  discardLetterReset: discardLetterReset_,
  terminateLetterAsync: terminateLetterAsync_,
  discardLetterAsync: discardLetterAsync_,
  getLetterHistoryAsync: getLetterHistoryAsync_,
  letterHistoryReset: letterHistoryReset_,
  getLetterLayoutsAsync: getLetterLayoutsAsync_,
  getLetterLayoutsReset: getLetterLayoutsReset_,
  sendECEAsync: sendECEAsync_,
  sendECEReset: sendECEReset_,
  correspondenceExchangeRegistryPermissionsAsync: correspondenceExchangeRegistryPermissionsAsync_,
  correspondenceExchangeRegistryPermissionsReset: correspondenceExchangeRegistryPermissionsReset_,
  voidLetterAsync: voidLetterAsync_,
  voidLetterReset: voidLetterReset_,
  createCorrespondenceSampleAsync: createCorrespondenceSampleAsync_,
  createCorrespondenceSampleReset: createCorrespondenceSampleReset_,
  correspondenceSamplesListAsync: correspondenceSamplesListAsync_,
  correspondenceSamplesListReset: correspondenceSamplesListReset_,
  deleteCorrespondenceSampleAsync: deleteCorrespondenceSampleAsync_,
  searchReferencesLettersAsync: searchReferencesLettersAsync_,
  createLetterContentByTopicAsync: createLetterContentByTopicAsync_,
  checkingIncomingNumberRepetitiveAsync: checkingIncomingNumberRepetitiveAsync_,
  checkingIncomingNumberRepetitiveReset: checkingIncomingNumberRepetitiveReset_,
  eceReceiptAsync: eceReceiptAsync_,
  createForwardNotesSampleAsync: createForwardNotesSampleAsync_,
  getForwardNotesSamplesAsync: getForwardNotesSamplesAsync_,
  deleteForwardNotesSamplesAsync: deleteForwardNotesSamplesAsync_,
  forwardNotesSamplesReset: forwardNotesSamplesReset_,
})(Letter);

export default withCorrespondenceInitializer(withLetterComposeSecurity(LetterWithRedux));
