import { MouseEventHandler } from 'react';

export type MemberType = {
  position: {
    slot: number[];
    id: string;
    _id: string;
    title: string;
  };
  user: {
    id: string;
    _id: string;
    mobile?: string;
    username?: string;
    signatureId?: string;
    firstName?: string;
    lastName?: string;
    imageId?: string;
  };
};

export type ProjectType = {
  id: string;
  title: string;
  description: string;
  status: string;
  creator: string;
  owner: MemberType;
  members: MemberType[];
  organization: MemberType;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type MediaProjectsListType = {
  dataList: ProjectType[];
  meta: {
    limit: number;
    offset: number;
    total: number;
  };
  loading: boolean;
  handleNextPage: () => void;
  handlePrevPage: () => void;
  changePage: () => void;
  ProjectsDialog: boolean;
  handleOpenProjectsDialog: () => void;
  handleCloseProjectsDialog: () => void;
  handleProjectCollapseInfo: (index: number) => void;
  hasPosition: boolean;
  projectIndexValue: { [index in number]: boolean };
  isPending: boolean;
  reOrderMembers: (arr: MemberType[], owner: MemberType) => MemberType[];
  resetAndGetProjectsList: () => void;
  handleSelectProject: (project: ProjectType) => void;
  handleChangeLimit: (value: number) => void;
};

export type ProjectsListPropsType = {
  projectsListAsync: any;
  projectsList: any;
};

export type ProjectCardPropsType = {
  project: ProjectType;
  handleProjectCollapseInfo: () => void;
  isCollapsed: boolean;
  isPending: boolean;
  reOrderMembers: (arr: MemberType[], owner: MemberType) => MemberType[];
  onSelect?: (project: ProjectType) => void;
};

export type MembersPropsType = {
  members: MemberType[];
  isCollapsed: boolean;
  onClick?: MouseEventHandler<HTMLDivElement>;
  isPending: boolean;
};
