import React, { useEffect, useRef } from 'react';
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined';
import PlaylistAddOutlinedIcon from '@mui/icons-material/PlaylistAddOutlined';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import AWBox from '../../../../../../components/AWComponents/AWBox';
import AWIconButton from '../../../../../../components/AWComponents/AWIconButton';
import CreateLetterSample from '../letter-samples';
import LetterSamplesListPopover from '../letter-samples-list';
import LetterAiDialog from '../../../../../genai/letter-ai-dialog';
import LetterAiPopover from '../../../../../genai/letter-ai-popover';
import AWTooltip from '../../../../../../components/AWComponents/AWTooltip';
import dubi from '../../../../../../assets/images/dubi.svg';
import { useTour } from 'contexts/tourContext/TourContext';
import { TourStep } from 'contexts/tourContext/types';

const useStyles = makeStyles()((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    position: 'relative',
  },
  letterSampleButton: {
    background: theme.palette.secondary['100'],
    borderRadius: theme.spacing(0.75),
  },
  addLetterSampleButton: {
    background: theme.palette.secondary['100'],
    borderRadius: theme.spacing(0.5),
  },
  parentButton: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: theme.spacing(1),
    padding: theme.spacing(1.5),
  },
  letterSampleRoot: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    padding: theme.spacing(1.5),
    position: 'relative',
    gap: theme.spacing(1),
  },
  selectLetterSample: {
    background: theme.palette.secondary['100'],
    borderRadius: theme.spacing(0.75),
  },
  addLetterSample: {
    background: theme.palette.secondary['100'],
    borderRadius: theme.spacing(0.5),
  },
  contentTourGuid: {
    width: 257,
    fontSize: 14,
  },
}));

const LetterSample: React.FC<any> = ({
  disabled,
  value,
  createLetterSampleState,
  setCreateLetterSampleState,
  onCreateLetterSample,
  openForCreateLetterSampleDialog,
  onOpenCreateLetterSampleDialog,
  onCloseCreateLetterSampleDialog,
  loadingCreateLetterSample,
  letterSamplesListAnchorElAnchorEl,
  handleOpenLetterSamplesListPopover,
  handleCloseLetterSamplesPopover,
  security,
  correspondenceSamplesListLoading,
  correspondenceSamplesList,
  deleteCorrespondenceSample,
  applyCorrespondenceSample,
  deleteCorrespondenceSampleLoading,
  commands,
  openLetterAiAnchorElement,
  handleCloseLetterAiPopover,
  handleClickLetterAiPopover,
  letterAiAnchorEl,
  selectedTabCreateLetterByTopic,
  handleToggleChangeTabGenai,
  setTextFieldGenai,
  submitCreateLetterByTopic,
  genaiLoading,
  textFieldGenai,
  isMobile,
  isAddLetterSampleDisabled,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const { startTour } = useTour();

  const buttonRef = useRef(null);

  const stepsGuid: TourStep[] = [
    {
      id: 'btn-letter-ai',
      content: (
        <AWBox className={classes.contentTourGuid}>
          {t('common.messages.startGuidTasks.guideAI')}
        </AWBox>
      ),
      target: 'btn-letter-ai',
      position: {
        anchorOrigin: { vertical: 'center', horizontal: 'left' },
        transformOrigin: { vertical: 'center', horizontal: 'right' },
      },
      targetStyle: { fontWeight: 'bold', borderRadius: '8px', border: '1px solid red' }, // Example styles
      showNextButton: false,
    },
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          startTour(stepsGuid, { key: 'letter-ai-intro', showAgain: false });
          observer.disconnect();
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 0.1,
      },
    );

    if (buttonRef.current) {
      observer.observe(buttonRef.current);
    }

    return () => {
      if (observer) observer.disconnect();
    };
  }, []);

  return (
    <>
      <AWBox className={classes.parentButton}>
        <AWTooltip title={t('tooltip.genai')} arrow>
          <img
            id="btn-letter-ai"
            src={dubi}
            alt="dubi"
            onClick={disabled ? undefined : handleClickLetterAiPopover}
            style={{
              cursor: disabled ? 'not-allowed' : 'pointer',
            }}
            ref={buttonRef}
          />
        </AWTooltip>
        {isMobile ? (
          <LetterAiDialog
            open={openLetterAiAnchorElement}
            onClose={handleCloseLetterAiPopover}
            onClick={() => submitCreateLetterByTopic(commands)}
            selectedTabCreateLetterByTopic={selectedTabCreateLetterByTopic}
            handleToggleChangeTabGenai={handleToggleChangeTabGenai}
            setTextFieldGenai={setTextFieldGenai}
            genaiLoading={genaiLoading}
            textFieldGenai={textFieldGenai}
          />
        ) : (
          <LetterAiPopover
            open={openLetterAiAnchorElement}
            anchorEl={letterAiAnchorEl}
            handleClose={handleCloseLetterAiPopover}
            selectedTabCreateLetterByTopic={selectedTabCreateLetterByTopic}
            handleToggleChangeTabGenai={handleToggleChangeTabGenai}
            setTextFieldGenai={setTextFieldGenai}
            submitCreateLetterByTopic={() => submitCreateLetterByTopic(commands)}
            genaiLoading={genaiLoading}
            textFieldGenai={textFieldGenai}
          />
        )}
        <AWBox className={classes.letterSampleRoot}>
          <AWIconButton
            onClick={handleOpenLetterSamplesListPopover}
            tooltip={t('tooltip.correspondence.samples.select_letter_sample')}
            color="secondary"
            disabled={disabled}
            className={classes.selectLetterSample}
          >
            <TextSnippetOutlinedIcon />
          </AWIconButton>
          <LetterSamplesListPopover
            anchorEl={letterSamplesListAnchorElAnchorEl}
            onClose={handleCloseLetterSamplesPopover}
            correspondenceSamplesListLoading={correspondenceSamplesListLoading}
            correspondenceSamplesList={correspondenceSamplesList}
            deleteCorrespondenceSample={deleteCorrespondenceSample}
            applyCorrespondenceSample={(value) => {
              commands.addContent(value.content);
              applyCorrespondenceSample();
            }}
            deleteCorrespondenceSampleLoading={deleteCorrespondenceSampleLoading}
            security={security}
          />
          <AWIconButton
            tooltip={t('tooltip.correspondence.samples.add_letter_sample')}
            security={security?.createCorrespondenceSample}
            onClick={onOpenCreateLetterSampleDialog}
            disabled={isAddLetterSampleDisabled}
            color="secondary"
            className={classes.addLetterSample}
          >
            <PlaylistAddOutlinedIcon />
          </AWIconButton>
        </AWBox>
      </AWBox>
      <CreateLetterSample
        state={createLetterSampleState}
        setState={setCreateLetterSampleState}
        open={openForCreateLetterSampleDialog}
        onSubmit={onCreateLetterSample}
        onClose={onCloseCreateLetterSampleDialog}
        loading={loadingCreateLetterSample}
      />
    </>
  );
};
export default LetterSample;
