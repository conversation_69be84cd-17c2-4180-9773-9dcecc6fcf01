import { useCallback, useEffect, useMemo, useState, useTransition } from 'react';
import { useLayout } from 'contexts/layoutContext';
import { connect } from 'react-redux';
import { useParams, useSearchParams } from 'react-router-dom';
import useMediaQuery from '@mui/material/useMediaQuery';
import MediaProjectsList from './media';
import { withManagementCenterInitializer } from '../../with-management-center-initializer';
import useAWCache from '../../../../hooks/useAWCache';
import { RootState } from '../../../../store';
import { projectsListAsync as projectsListAsync_ } from '../projectSlice';
import { MemberType, ProjectsListPropsType, ProjectType } from './type';
import useListener from '../../../../hooks/useListener';
import { ProjectsListContext } from './context';

const ProjectsList: React.FC<ProjectsListPropsType> = ({ projectsListAsync, projectsList }) => {
  const { selectedPosition } = useLayout();
  const params = useParams();
  const { organizationId } = params;
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [isPending, startTransition] = useTransition();
  const [searchParams, setSearchParams] = useSearchParams();
  //* /////////////////////// States ////////////////////////////////////
  const [ProjectsDialog, setProjectsDialog] = useState<boolean>(false);
  const [projectIndexValue, setProjectIndexValue] = useState<{ [index in number]: boolean }>({});
  const ProjectId = searchParams.get('projectId');
  //* /////////////////////// useEffects ////////////////////////////////////
  useEffect(() => {
    if (ProjectId) {
      handleOpenProjectsDialog();
    } else {
      handleCloseProjectsDialog();
    }
  }, [ProjectId]);

  const fetcher = (offset: number, limit: number) => {
    if (!selectedPosition?.position) return;
    const { id: positionId, slot } = selectedPosition.position;
    if (positionId && slot?.length) {
      projectsListAsync({
        organizationId,
        positionSlotId: `${positionId}_${slot.join('-')}`,
        limit,
        offset,
      });
    }
  };

  const {
    dataList,
    changePage,
    meta,
    tableReset,
    loading,
    handleNextPage,
    handlePrevPage,
    refetch,
    changeLimit,
  } = useAWCache<ProjectType>({
    fetcher,
    payload: projectsList,
    infinity: mobile,
    usingCache: mobile,
    isStorePagination: !mobile,
  });

  const handleChangeLimit = useCallback(
    (value: number) => {
      changeLimit(value);
      changePage(undefined, value);
    },
    [changeLimit, changePage],
  );

  const hasPosition = useMemo(
    () => Boolean(selectedPosition?.position?.id),
    [selectedPosition?.position],
  );

  const resetAndGetProjectsList = () => {
    tableReset();
    refetch();
  };

  useListener(() => {
    resetAndGetProjectsList();
  }, [mobile, selectedPosition?.position]);

  //* /////////////////////// Functions ////////////////////////////////////
  const handleOpenProjectsDialog = () => {
    setProjectsDialog(true);
  };

  const handleCloseProjectsDialog = () => {
    setProjectsDialog(false);
  };

  const handleProjectCollapseInfo = (index: number) => {
    startTransition(() => {
      setProjectIndexValue((prev) => {
        const cp = { ...prev };
        if (prev?.[index]) {
          cp[index] = !prev[index];
        } else {
          cp[index] = true;
        }
        return cp;
      });
    });
  };

  const handleSearchParams = (key: string, value: string) => {
    searchParams.set(key, value);
    setSearchParams(searchParams, { replace: true });
  };

  const handleSelectProject = (project: ProjectType) => {
    handleSearchParams('projectId', project.id);
  };

  const reOrderMembers = (arr: MemberType[], owner: MemberType) => {
    const filter = arr.filter((item) => item.position?.id !== owner.position?.id);
    return [...filter, owner];
  };

  return (
    <ProjectsListContext.Provider
      value={{
        ProjectsDialog,
        handleOpenProjectsDialog,
        handleCloseProjectsDialog,
        hasPosition,
        dataList,
        changePage,
        meta,
        loading,
        handleNextPage,
        handlePrevPage,
        handleProjectCollapseInfo,
        projectIndexValue,
        isPending,
        reOrderMembers,
        resetAndGetProjectsList,
        handleSelectProject,
        handleChangeLimit,
      }}
    >
      <MediaProjectsList />
    </ProjectsListContext.Provider>
  );
};

const mapStateToProps = ({ projects: { projectsList } }: RootState) => ({ projectsList });

export default withManagementCenterInitializer(
  connect(mapStateToProps, {
    projectsListAsync: projectsListAsync_,
  })(ProjectsList),
);
