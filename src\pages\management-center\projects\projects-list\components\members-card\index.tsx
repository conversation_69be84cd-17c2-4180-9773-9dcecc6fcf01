import { makeStyles } from 'tss-react/mui';
import Grid from '@mui/material/Grid';
import { useTranslation } from 'react-i18next';
import AWTypography from '../../../../../../components/AWComponents/AWTypography';
import AWDivider from '../../../../../../components/AWComponents/AWDivider';
import AWBox from '../../../../../../components/AWComponents/AWBox';
import Members from '../members';
import { ProjectCardPropsType } from '../../type';
import { userInfo } from '../../../../../../utils';

const useStyles = makeStyles()((theme) => ({
  container: {
    borderRadius: 6,
    boxShadow: '0px 1px 8px 0px #0000001F',
    backgroundColor: '#fff',
    padding: theme.spacing(2, 1),
  },
  label: {
    color: theme.palette.text.gray,
  },
  details: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ellipsisText: {
    maxWidth: theme.spacing(20),
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    display: 'block',
  },
}));

const ProjectCard: React.FC<ProjectCardPropsType> = ({
  handleProjectCollapseInfo,
  isCollapsed,
  isPending,
  project,
  reOrderMembers,
  onSelect,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslation();
  const { owner, members, title } = project;
  const data = {
    'project-owner': owner,
    'project-member': members,
  };

  return (
    <Grid
      container
      direction="column"
      className={classes.container}
      gap={1}
      onClick={() => onSelect?.(project)}
    >
      <AWTypography className={classes.ellipsisText}>{title}</AWTypography>
      <AWDivider />
      {Object.entries(data).map(([key, value]) => (
        <AWBox className={classes.details} key={key}>
          <AWTypography className={classes.label}>{t(`projects.labels.${key}`)}</AWTypography>
          {Array.isArray(value) ? (
            <Members
              isPending={isPending}
              members={reOrderMembers(value, owner)}
              isCollapsed={isCollapsed}
              onClick={(e) => {
                e.stopPropagation();
                handleProjectCollapseInfo();
              }}
            />
          ) : (
            <AWTypography className={classes.ellipsisText}>
              {userInfo(value?.user, value?.position)}
            </AWTypography>
          )}
        </AWBox>
      ))}
    </Grid>
  );
};
export default ProjectCard;
