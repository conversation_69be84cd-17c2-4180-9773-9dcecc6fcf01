import { useState, useEffect, useCallback, startTransition, useMemo } from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import { connect } from 'react-redux';
import { useParams } from 'react-router';

import { useLayout } from 'contexts/layoutContext';
import MobileECELetters from './ece-letters-mobile';
import DesktopECELetters from './ece-letters-desktop';
import { ECELettersContext } from '../../../../../contexts/pageContext/letters/cartable-components/cartable/ece-letters-context';
import {
  incomingEceMailsAsync as incomingEceMailsAsync_,
  incomingEceMailsReset as incomingEceMailsReset_,
  outgoingEceMailsAsync as outgoingEceMailsAsync_,
  outgoingEceMailsReset as outgoingEceMailsReset_,
  eceMailAsync as eceMailAsync_,
  convertMailToLetterAsync as convertMailToLetterAsync_,
  getEceReceiptMailsListAsync as getEceReceiptMailsListAsync_,
  retryEceReceiptAsync as retryEceReceiptAsync_,
} from '../../../secretariats/ece-mails-slice';
import {
  getEceLettersRefreshAsync as getEceLettersRefreshAsync_,
  getSecretariatsAsync as getSecretariatsAsync_,
  getSecretariatsReset as getSecretariatsReset_,
  eceLettersRefreshReset as eceLettersRefreshReset_,
} from '../../../secretariats/secretariatsSlice';
import EceLetterDialogs from './components/ece-letter-dialogs';
import useListener from '../../../../../hooks/useListener';
import { correspondenceRolesAsync as correspondenceRolesAsync_ } from '../../../correspondence-authz-slice';
import { useUser } from '../../../../../contexts/userContext';
import { roles } from '../../../../admins/utility';
import { checkIsOwnerOrganization } from '../../../../../utils';
import { withCorrespondenceInitializer } from '../../../withCorresponseInitializer';
import withCartableSecurity from '../../withCartableSecurity';
import useAWCache from '../../../../../hooks/useAWCache';

const REFRESHTIME = 5 * 60;

const ECELetters = ({
  convertMailToLetterAsync,
  eceMailAsync,
  incomingEceMailsAsync,
  incomingEceMailsReset,
  outgoingEceMailsAsync,
  outgoingEceMailsReset,
  getSecretariatsAsync,
  correspondenceRolesAsync,
  correspondenceRolesReset,
  correspondenceAuthz,
  eceMails,
  secretariats,
  getSecretariatsReset,
  positions,
  profile,
  getEceLettersRefreshAsync,
  eceLettersRefreshReset,
  organization,
  notification,
  getEceReceiptMailsListAsync,
  retryEceReceiptAsync,
}) => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));

  const params = useParams();
  const { selectedPosition } = useLayout();
  const { signedInUser } = useUser();
  const { organizationId } = params;
  const ownerId = organization.selectedOrganization?.owner?.id;

  //* states ----------------------------------------------------------------------
  const [showDialogRefreshEce, setShowDialogRefreshEce] = useState(false);
  const [isInited, setIsInited] = useState(false);
  const [selectedSecretariat, setSelectedSecretariat] = useState({ id: '', name: '' });
  const [openSecretariatsDrawer, setOpenSecretariatsDrawer] = useState(false);
  const [organizationSecretariats, setOrganizationSecretariats] = useState([]);
  const [type, setType] = useState('incoming');

  //* hooks ----------------------------------------------------------------------
  const showRefreshEceLetter = useMemo(() => type === 'incoming', [type]);

  //* Requester
  const requester = useMemo(
    () => ({
      user: {
        id: signedInUser.id,
      },
      position: {
        id: selectedPosition.position.id,
        slot: selectedPosition.position.slot,
      },
    }),
    [selectedPosition.position, signedInUser],
  );

  useListener(() => {
    if (eceMails.retryEceReceipt.success) {
      tableReset();
      changePage();
    }
  }, [eceMails.retryEceReceipt]);

  useListener(() => {
    tableReset();
    changePage(1);
  }, [selectedSecretariat, type, selectedPosition?.position]);

  //* on mount
  useEffect(() => {
    initializePage();
    return () => {
      getSecretariatsReset();
      correspondenceRolesAsync({ organizationId, userId: signedInUser.id });
      incomingEceMailsReset();
      outgoingEceMailsReset();
    };
  }, []);

  //* on change secretariats
  useEffect(() => {
    calcOrganizationSecretariat();
  }, [correspondenceAuthz.solutionOrganizationRoles.data, secretariats.secretariatsList.data]);

  useListener(() => {
    if (
      notification?.newNotifications?.data.find(
        (item) => item?.properties?.context === 'ECE_MAIL_FORCE_FETCHED',
      )
    ) {
      tableReset();
      changePage(1);
    }
  }, [notification?.newNotifications?.data]);

  //* functions ----------------------------------------------------------------------
  const getECELetters = (secretariatId, offset, limit) => {
    const opts = { secretariatId, offset, limit };
    incomingEceMailsAsync(opts);
  };

  const getSentECELetters = (secretariatId, offset, limit) => {
    const opts = { secretariatId, offset, limit };
    outgoingEceMailsAsync(opts);
  };

  const fetcher = (offset, limit) => {
    const secretariatId = selectedSecretariat?.id;
    if (!secretariatId) return null;

    const commonParams = {
      secretariatId,
      offset,
      limit,
    };
    switch (type) {
      case 'incoming':
        return getECELetters(secretariatId, offset, limit);
      case 'outgoing':
        return getSentECELetters(secretariatId, offset, limit);
      case 'received':
      case 'sent':
        return getEceReceiptMailsListAsync({ ...commonParams, type });
      default:
        return null;
    }
  };

  const {
    dataList,
    changePage,
    tableReset,
    loading: eceMailsLoading,
    handleNextPage,
    handlePrevPage,
    meta,
    changeLimit,
  } = useAWCache({
    fetcher,
    infinity: mobile,
    usingCache: mobile,
    payload: (() => {
      switch (type) {
        case 'incoming':
          return eceMails.incomingEceMailsList;
        case 'outgoing':
          return eceMails.outgoingEceMailsList;
        case 'received':
        case 'sent':
          return eceMails.eceReceiptMailsList;
        default:
          return [];
      }
    })(),
  });

  const handleChangeLimit = useCallback(
    (value) => {
      changeLimit(value);
      changePage(undefined, value);
    },
    [changeLimit, changePage],
  );

  const calcOrganizationSecretariat = useCallback(() => {
    const { data } = secretariats.secretariatsList;
    if (data) {
      const secretariatsList = secretariats?.secretariatsList?.data;
      const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
      if (secretariatsList) {
        //* check the organization ownership
        const isOrganizationOwner = checkIsOwnerOrganization(ownerId);
        const isAdmin =
          solutionOrganizationRoles?.find?.(
            (item) => item.id === roles.correspondence_admin || item.id === roles.super_admin,
          ) ?? false;
        if (isOrganizationOwner || isAdmin) {
          setOrganizationSecretariats(secretariats.secretariatsList.data);
          setSelectedSecretariat({ id: data[0].id, name: data[0].name });
        } else if (solutionOrganizationRoles) {
          //* check user roles
          const availableSecretariats = solutionOrganizationRoles?.find?.(
            (item) => item?.id === roles?.secretariat_admin,
          );
          if (availableSecretariats) {
            const filtered = secretariatsList?.filter?.((secretariat) =>
              availableSecretariats?.secretariats?.find?.((item) => item.id === secretariat.id),
            );
            setOrganizationSecretariats(filtered);
            setSelectedSecretariat({ id: filtered[0].id, name: filtered[0].name });
            getECELetters(filtered[0].id);
          }
        }
      }
    }
  }, [correspondenceAuthz.solutionOrganizationRoles.data, secretariats.secretariatsList]);

  //* open mail dialog
  const [mailDialog, setMailDialog] = useState({ open: false, data: null });
  const openMailDialog = (data) => {
    setMailDialog((prevState) => ({
      ...prevState,
      open: true,
      data: { ...data, secretariatId: selectedSecretariat?.id },
    }));
  };
  const closeMailDialog = () =>
    setMailDialog((prevState) => ({ ...prevState, open: false, data: null }));

  const handleRenderLayout = useMemo(() => {
    if (mobile) return <MobileECELetters />;
    if (desktop || tablet) return <DesktopECELetters />;
    return <></>;
  }, [mobile, desktop, tablet]);

  const tableLoading = eceMailsLoading;
  const secretariatsLoading = secretariats.secretariatsList.status === 'loading';
  const pageLoading = secretariatsLoading && !isInited;

  const remainingCalculate = () => {
    const now = Date.now();
    const refreshTime =
      secretariats?.eceLettersRefresh?.timeRefresh ||
      Number(localStorage.getItem('ece-letters-refresh'));
    if (!refreshTime) {
      return 0;
    }
    const timeRefresh = REFRESHTIME - (now - refreshTime) / 1000;
    return timeRefresh > 0 ? timeRefresh : 0;
  };

  const initializePage = async () => {
    await getOrganizationSecretariats();
    setIsInited(true);
  };

  const getOrganizationSecretariats = async () => {
    const opts = { path: organizationId };
    await getSecretariatsAsync(opts);
  };

  const handleChangeSecretariat = (e) => {
    const value = JSON.parse(e.target.value);
    setSelectedSecretariat(value);
    setOpenSecretariatsDrawer(false);
  };

  const handleResetEceLettersRefresh = useCallback(() => {
    eceLettersRefreshReset();
  }, []);

  const handleRowClick = useCallback(
    (row) => {
      openMailDialog(row);
    },
    [openMailDialog],
  );

  const handleEceLettersRefresh = useCallback(() => {
    startTransition(() => {
      const remainingNow = remainingCalculate();
      if (remainingNow) {
        setShowDialogRefreshEce(true);
      } else {
        getEceLettersRefreshAsync({ secretariatId: selectedSecretariat.id });
      }
    });
  }, [remainingCalculate, getEceLettersRefreshAsync]);

  const handleChangeTab = useCallback(
    (e, newValue) => {
      tableReset();
      setType(() => newValue);
      changePage(1, 10);
    },
    [setType],
  );

  const handleFailedSentReceiptsEce = (row) => {
    const receiptId = row.id;
    const eceId = row.eceMail.id;
    retryEceReceiptAsync({
      id: eceId,
      receipt: {
        id: receiptId,
      },
      requester,
    });
  };

  return (
    <ECELettersContext.Provider
      value={{
        dataList,
        handleNextPage,
        handlePrevPage,
        changePage,
        meta,
        mobile,
        type,
        handleChangeTab,
        organizationSecretariats,
        selectedSecretariat,
        handleChangeSecretariat,
        eceMails,
        pageLoading,
        tableLoading,
        limit: meta.limit,
        offset: meta.offset,
        positions,
        profile,
        params,
        openSecretariatsDrawer,
        setOpenSecretariatsDrawer,
        handleRowClick,
        showDialogRefreshEce,
        setShowDialogRefreshEce,
        handleEceLettersRefresh,
        showRefreshEceLetter,
        remainingCalculate,
        handleResetEceLettersRefresh,
        handleFailedSentReceiptsEce,
        handleChangeLimit,
      }}
    >
      {handleRenderLayout}
      {mailDialog.open && (
        <EceLetterDialogs
          onClose={closeMailDialog}
          open={mailDialog.open}
          mailInfoData={mailDialog.data}
          type={type}
        />
      )}
    </ECELettersContext.Provider>
  );
};

const mapStateToProps = (state) => {
  const {
    eceMails,
    secretariats,
    positions,
    profile,
    correspondenceAuthz,
    organization,
    notification,
  } = state;
  return {
    eceMails,
    secretariats,
    positions,
    profile,
    correspondenceAuthz,
    organization,
    notification,
  };
};

export default withCorrespondenceInitializer(
  withCartableSecurity(
    connect(mapStateToProps, {
      convertMailToLetterAsync: convertMailToLetterAsync_,
      incomingEceMailsAsync: incomingEceMailsAsync_,
      incomingEceMailsReset: incomingEceMailsReset_,
      outgoingEceMailsAsync: outgoingEceMailsAsync_,
      outgoingEceMailsReset: outgoingEceMailsReset_,
      eceMailAsync: eceMailAsync_,
      getSecretariatsAsync: getSecretariatsAsync_,
      getSecretariatsReset: getSecretariatsReset_,
      correspondenceRolesAsync: correspondenceRolesAsync_,
      getEceLettersRefreshAsync: getEceLettersRefreshAsync_,
      eceLettersRefreshReset: eceLettersRefreshReset_,
      getEceReceiptMailsListAsync: getEceReceiptMailsListAsync_,
      retryEceReceiptAsync: retryEceReceiptAsync_,
    })(ECELetters),
  ),
);
