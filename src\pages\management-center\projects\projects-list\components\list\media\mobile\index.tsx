import Grid from '@mui/material/Grid';
import { makeStyles } from 'tss-react/mui';
import { useProjectsListContext } from '../../../../context';
import InfiniteScroll from '../../../../../../../../components/infiniteScroll';
import ProjectCard from '../../../members-card';
import AWBox from '../../../../../../../../components/AWComponents/AWBox';

const useStyles = makeStyles()({
  card: {
    width: '100%',
    minHeight: 115,
  },
});

const ProjectListMobile: React.FC = () => {
  const { classes } = useStyles();

  const {
    dataList,
    handleNextPage,
    meta,
    loading,
    handleProjectCollapseInfo,
    projectIndexValue,
    isPending,
    reOrderMembers,
    handleSelectProject,
  } = useProjectsListContext();
  return (
    <>
      <InfiniteScroll
        next={handleNextPage}
        height={`calc(100vh - ${128}px)`}
        hasMore={meta.total !== (dataList?.length ?? 0)}
        data={dataList}
        showLoader={loading}
      >
        <Grid item container gap={1}>
          {dataList.map((project, index) => (
            <AWBox key={project.id} className={classes.card}>
              <ProjectCard
                {...{
                  project,
                  isPending,
                  isCollapsed: projectIndexValue[index] ?? false,
                  reOrderMembers,
                  handleProjectCollapseInfo: () => handleProjectCollapseInfo(index),
                  onSelect: handleSelectProject,
                }}
              />
            </AWBox>
          ))}
        </Grid>
      </InfiniteScroll>
    </>
  );
};

export default ProjectListMobile;
