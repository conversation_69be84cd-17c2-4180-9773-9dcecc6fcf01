import React from 'react';
import { makeStyles } from 'tss-react/mui';

import { t } from 'i18next';
import AWBox from '../../../../../../components/AWComponents/AWBox';
import AWTypography from '../../../../../../components/AWComponents/AWTypography';
import { formatLetterNumber } from '../../../../../../utils/letterNumberUtils';

const useStyles = makeStyles()((theme) => ({
  box: {
    backgroundColor: '#F4F8FB',
    minHeight: 42,
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'top',
    color: '#1F67D3',
    padding: theme.spacing(1),
    borderRadius: theme.spacing(1),
    gap: theme.spacing(1),
    [theme.breakpoints.down('sm')]: {
      minHeight: 34,
      padding: theme.spacing(0.5, 1),
    },
  },
  number: {
    fontWeight: 700,
    flex: '1 0',
    lineBreak: 'anywhere',
    direction: 'ltr',
  },
  label: {
    fontSize: 12,
    lineHeight: '24px',
    flex: '0 1',
  },
}));

const NumberLetterPreview = ({ value }: { value: string }) => {
  const { classes } = useStyles();

  return (
    <AWBox className={classes.box}>
      <AWTypography className={classes.label}>
        {t('secretariats.labels.letterPreview')}:
      </AWTypography>
      <AWTypography className={classes.number}>{formatLetterNumber(value) || '-'}</AWTypography>
    </AWBox>
  );
};

export default NumberLetterPreview;
