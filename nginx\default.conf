server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;
    client_max_body_size 30M;

    underscores_in_headers on;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;

    location /api/v1/genai/ {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_pass http://com-chargoon-cloud-svc-gw:3000/api/v1/genai/;
        proxy_redirect off;
        proxy_buffering off;

        proxy_connect_timeout 300s;  
        proxy_send_timeout 300s;     
        proxy_read_timeout 300s;     
    }

    location /api/v1/ {
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_pass http://com-chargoon-cloud-svc-gw:3000/api/v1/;
        proxy_redirect off;
        proxy_buffering off;
    }

    location / {
       root   /usr/share/nginx/html;
       index  index.html index.htm;
       try_files $uri $uri/ /index.html;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    #error_page   500 502 503 504  /50x.html;
    #location = /50x.html {
    #    root   /usr/share/nginx/html;
    #}

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}
