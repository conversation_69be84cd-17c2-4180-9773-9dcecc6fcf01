import { useCallback } from 'react';
import useAWCache from 'hooks/useAWCache';
import { withCorrespondenceInitializer } from 'pages/correspondence/withCorresponseInitializer';
import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { timeUtils } from 'utils/time-utils';
import withSecretariatsSecurity from '../with-secretariats-security';
import { connect } from 'react-redux';
import { allForwardedLettersAsync as allForwardedLettersAsync_ } from 'pages/correspondence/letters/lettersSlice';
import {
  organizationEmployeesListAsync as organizationEmployeesListAsync_,
  organizationEmployeeListReset as organizationEmployeeListReset_,
  organizationEmployeeContactsAsync as organizationEmployeeContactsAsync_,
  organizationEmployeeContactsReset as organizationEmployeeContactsReset_,
} from 'pages/management-center/employees/employeesSlice';
import { getOrganizationPositionsAsync as getOrganizationPositionsAsync_ } from '../../../management-center/positions/positionsSlice';
import { useMediaQuery } from '@mui/material';
import AllForwarderLetterMedia from './media';
import { SecretariatContext } from 'contexts/pageContext/secretariat/secretariatContext';
import { CartableForwardedLettersContext } from 'contexts/pageContext/letters/cartable-components/forwarded-letters-context';

const AllForwardedLetter = ({
  allForwardedLettersAsync,
  organizationEmployeesListAsync,
  organizationEmployeeContactsAsync,
  getOrganizationPositionsAsync,
  letters,
}: any) => {
  const [searchParams] = useSearchParams();
  const params = useParams();
  const { organizationId, secretariatId } = params;
  const mobile = useMediaQuery((theme: any) => theme.breakpoints.down('sm'));

  const [senderValue, setSenderValue] = useState<any>(null);
  const [recipientValue, setRecipientValue] = useState<any>(null);
  const [fromValue, setFromValue] = useState(null);
  const [toValue, setToValue] = useState(null);
  const [showDialogMobileFilter, setShowDialogMobileFilter] = useState(false);
  const [openPositionEmployeeTreeDialogRecipient, setOpenPositionEmployeeTreeDialogRecipient] =
    useState(false);
  const [openPositionEmployeeTreeDialogSender, setOpenPositionEmployeeTreeDialogSender] =
    useState(false);

  useEffect(() => {
    setFromValue(null);
    setToValue(null);
    setSenderValue(null);
    setRecipientValue(null);
    getOrganizationEmployees();
  }, []);

  useEffect(() => {
    const senderParam = searchParams.get('sender');
    const recipientParam = searchParams.get('recipient');
    if (senderParam !== null) {
      setSenderValue(JSON.parse(senderParam));
    }
    if (recipientParam !== null) {
      setRecipientValue(JSON.parse(recipientParam));
    }
  }, []);

  const fetcher = (offset: any, limit: any) => {
    const from = fromValue
      ? timeUtils.startOfTimeRangeFromSelectedDate(fromValue, 'day').toISOString()
      : null;
    const to = toValue
      ? timeUtils.startOfTimeRangeFromSelectedDate(toValue, 'day').add(1, 'day').toISOString()
      : null;
    const sender = senderValue
      ? `${senderValue.position.id}_${senderValue.position.slot.join('-')}`
      : null;
    const recipient = recipientValue
      ? `${recipientValue.position.id}_${recipientValue.position.slot.join('-')}`
      : null;

    const opts = {
      path: organizationId,
      queryObject: {
        offset,
        limit,
        from,
        to,
        sender,
        recipient,
      },
    };
    allForwardedLettersAsync(opts);
  };

  const {
    dataList,
    changePage,
    meta,
    tableReset,
    loading,
    handleNextPage,
    handlePrevPage,
    changeLimit,
  } = useAWCache({
    payload: letters.allForwardedLetters,
    fetcher,
    infinity: mobile,
    usingCache: mobile,
  });

  const handleChangeLimit = useCallback(
    (value: number) => {
      changeLimit(value);
      changePage(undefined, value);
    },
    [changeLimit, changePage],
  );

  // handle all forward letter filter

  const handleOpenPositionEmployeeTreeDialogRecipient = () => {
    setOpenPositionEmployeeTreeDialogRecipient(true);
  };

  const handleClosePositionEmployeeTreeDialogRecipient = () => {
    setOpenPositionEmployeeTreeDialogRecipient(false);
  };

  const handleOpenPositionEmployeeTreeDialogSender = () => {
    setOpenPositionEmployeeTreeDialogSender(true);
  };

  const handleClosePositionEmployeeTreeDialogSender = () => {
    setOpenPositionEmployeeTreeDialogSender(false);
  };

  const submitAllForwardedLettersFilter = () => {
    tableReset();
    changePage(1);
    setShowDialogMobileFilter(false);
  };

  const getOrganizationEmployees = () => {
    organizationEmployeesListAsync({
      path: `${organizationId}/employees`,
      queryObject: { limit: 1000, offset: 0 },
    });
    organizationEmployeeContactsAsync({
      path: `${organizationId}/employee-contacts`,
      queryObject: { limit: 1000, offset: 0 },
    });
    getOrganizationPositionsAsync({
      path: `organization/${organizationId}`,
      queryObject: { limit: 1000, offset: 0 },
    });
  };

  const handleOpenDialogMobileFilter = () => {
    setShowDialogMobileFilter(true);
  };

  const handleCloseDialogMobileFilter = () => {
    setShowDialogMobileFilter(false);
  };

  return (
    <CartableForwardedLettersContext.Provider
      value={{
        organizationId,
        openPositionEmployeeTreeDialogRecipient,
        openPositionEmployeeTreeDialogSender,
        handleOpenPositionEmployeeTreeDialogRecipient,
        handleClosePositionEmployeeTreeDialogRecipient,
        handleClosePositionEmployeeTreeDialogSender,
        handleOpenPositionEmployeeTreeDialogSender,
        submitAllForwardedLettersFilter,
        allForwardedLettersFilter: {
          senderValue,
          setSenderValue,
          recipientValue,
          setRecipientValue,
          toValue,
          setToValue,
          fromValue,
          setFromValue,
        },
        allForwardedLettersData: dataList,
        allForwardedLettersMeta: {
          limit: meta.limit,
          offset: meta.offset,
          total: meta.total,
          loading,
        },
        handleNextPage,
        handlePrevPage,
        changePage,
        isMobile: { mobile },
        showDialogMobileFilter,
        handleOpenDialogMobileFilter,
        handleCloseDialogMobileFilter,
        handleChangeLimit,
      }}
    >
      <AllForwarderLetterMedia />
    </CartableForwardedLettersContext.Provider>
  );
};

const mapStateToProps = (state: any) => {
  const { letters } = state;
  return {
    letters,
  };
};

export default withCorrespondenceInitializer(
  withSecretariatsSecurity(
    connect(mapStateToProps, {
      allForwardedLettersAsync: allForwardedLettersAsync_,
      organizationEmployeesListAsync: organizationEmployeesListAsync_,
      organizationEmployeeListReset: organizationEmployeeListReset_,
      organizationEmployeeContactsAsync: organizationEmployeeContactsAsync_,
      organizationEmployeeContactsReset: organizationEmployeeContactsReset_,
      getOrganizationPositionsAsync: getOrganizationPositionsAsync_,
    })(AllForwardedLetter),
  ),
);
