import { Suspense, useCallback, useEffect, useState } from 'react';

import useMediaQuery from '@mui/material/useMediaQuery';
import Grow from '@mui/material/Grow';
import { Outlet, useParams, useNavigate } from 'react-router';
import { MaterialDesignContent, SnackbarProvider, useSnackbar } from 'notistack';
import Box from '@mui/material/Box';
import { makeStyles, withStyles } from 'tss-react/mui';
import { connect } from 'react-redux';
import { ToastContainer, Flip } from 'react-toastify';
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CancelIcon from '@mui/icons-material/Cancel';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';

import { SnackbarUtilsConfigurator } from 'utils/snackbarUtils';
import { useLayout } from 'contexts/layoutContext';
import { MainLayoutStylesContext } from 'contexts/mainLayoutStylesContext';
import { organizationEmployeeContactsReset as organizationEmployeeContactsReset_ } from 'pages/management-center/employees/employeesSlice';
import { getOrganizationPositionsReset as getOrganizationPositionsReset_ } from 'pages/management-center/positions/positionsSlice';
import {
  contactGroupContactsReset as contactGroupContactsReset_,
  createContactGroupReset as createContactGroupReset_,
  searchContactsReset as searchContactsReset_,
  organizationContactGroupsReset as organizationContactGroupsReset_,
} from 'pages/management-center/manageContacts/contactGroupsSlice';
import {
  lettersNotExchangedReset as lettersNotExchangedReset_,
  forwardedLetterToMeReset as forwardedLetterToMeReset_,
  forwardedLetterByMeReset as forwardedLetterByMeReset_,
  contactsOrganizationReset as contactsOrganizationReset_,
} from 'pages/correspondence/letters/lettersSlice';
import {
  getIndicatorsReset as getIndicatorsReset_,
  getSecretariatsReset as getSecretariatsReset_,
} from 'pages/correspondence/secretariats/secretariatsSlice';
import { organizationSolutionsListReset as organizationSolutionsListReset_ } from 'pages/solutions/orders/ordersSlice';
import { useUser } from 'contexts/userContext';
import withDiscoverPath from 'hoc/with-discover-path';
import { correspondenceRolesReset as correspondenceRolesReset_ } from 'pages/correspondence/correspondence-authz-slice';
import AWIconButton from 'components/AWComponents/AWIconButton';
import Task from 'pages/tasks-solution/task';
import DialogCheckStatusOvered from './dialog-checkstatus-overed';
import BottomNavigationComponent from './bottom-navigation';
import AppBarMobile from './app-bar-mobile';
import SideBar from './side-bar';
import AppBarDesktop from './app-bar-desktop';
import 'react-toastify/dist/ReactToastify.css';
import ErrorBoundary from './error-boundary';
import BackPaperDesktop from '../BackPaperDesktop';

const stylesGenerator = (theme) => ({
  root: {
    flexGrow: 1,
    zIndex: 1,
    overflow: 'hidden',
    position: 'relative',
    display: 'flex',
    height: '100%',
  },
  desktopRootPage: {
    display: 'flex',
    alignItems: 'start',
    width: '100%',
    maxWidth: '1280px',
    margin: 'auto',
    height: '100%',
  },
  mobileRootPage: {
    height: '100%',
    display: 'block',
  },
  rootMobile: {
    height: '100%',
    display: 'flex',
    position: 'relative',
    flexDirection: 'column',
    justifyContent: 'space-between',
    overflow: 'hidden',
  },
  content2: {
    flexGrow: 1,
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    textAlign: 'center',
    flexDirection: 'column',
    overflowY: 'auto',
    // backgroundImage: `url(${bg})`,
    // backgroundRepeatY: 'no-repeat',
    // backgroundSize: 'contain',
  },
  content: {
    flexGrow: 1,
    padding: '0 16px',
    //! background: theme.palette.background.innerPage,
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginTop: 65,
    height: 'calc(100vh - 64px)',
    overflowY: 'auto',
    alignItems: 'center',
    position: 'relative',
  },
  contentShift: {
    paddingTop: '21px',
    paddingBottom: '21px',
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
    marginLeft: 0,
  },
  mobileContent: {
    flexGrow: 1,
    marginTop: 64,
    height: 'calc(100vh - 123px)',
    background: theme.palette.background.default,
    overflow: 'auto',
  },
  mobileContentWithTopBar: {
    marginTop: 64,
    overflow: 'auto',
    height: 'calc(100vh - 64px)',
  },
  mobileContentWithOutBottomBarTopBar: {
    display: 'flex',
    flexDirection: 'column',
    overflow: 'auto',
    flex: 1,
  },
  goBack: {
    backgroundColor: '#f3f3f3',
    padding: theme.spacing(0.4),
    borderRadius: '50%',
    width: '1.35em',
    height: '1.35em',
    fontSize: '1.3rem',
  },
  breadcrumbs: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    maxWidth: 1080,
    margin: `0 ${theme.spacing(3)}`,
    '& a': {
      color: theme.palette.text.link,
      fontSize: '0.875rem',
    },
    '& p': {
      color: theme.palette.text.secondary,
      fontSize: '0.875rem',
      fontWeight: 'bold',
    },
  },
  breadcrumbsIcon: {
    color: theme.palette.text.gray,
  },
  bold: {
    fontWeight: 'bold',
  },
  middlePaper: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '24px 32px',
    borderRadius: 16,
    boxShadow: '0 3px 10px 0 rgba(0, 0, 0, 0.1)',
    backgroundColor: theme.palette.background.default,
    margin: 'auto',
    // marginTop: '12px',
    minHeight: 545,
    width: 374,
  },
  mainPaper: {
    position: 'relative',
    paddingBottom: 40,
    width: '100%',
    maxWidth: 1280,
    alignItems: 'center',
    top: 80,
  },
  rootPage: {
    // maxWidth: (props) => (props ? 1280 : 'none'),
    // background: 'red',
    // padding: '100px',

    borderRadius: 12,
    // minHeight: ' calc(100vh - 108px)',
    height: '100%',
    [theme.breakpoints.down('md')]: {
      minHeight: 'unset',
    },

    //! padding: '0 10%',

    //! background: 'red',

    //* ///
    // minHeight: 'calc( 100vh - 86px)',
    // position: 'relative',
    width: '100%',
    alignItems: 'center',
    // top: 21,
    // paddingBottom: 21,
    margin: 'auto',
    [theme.breakpoints.down('md')]: {
      top: 10,
      overflowY: 'auto',
      margin: '1px 0px',
    },
  },
  paper: {
    borderRadius: 12,
    minHeight: ' calc(100vh - 108px)',
    [theme.breakpoints.down('md')]: {
      minHeight: 'unset',
    },
  },
  textCenter: {
    textAlign: 'center',
  },
  marginTop1: {
    marginTop: theme.spacing(1),
  },
  marginTop2: {
    marginTop: theme.spacing(2),
  },
  marginTop3: {
    marginTop: theme.spacing(3),
  },
  marginTop4: {
    marginTop: theme.spacing(4),
  },
  marginTop5: {
    marginTop: theme.spacing(5),
  },
  marginBottom1: {
    marginBottom: theme.spacing(1),
  },
  marginBottom2: {
    marginBottom: theme.spacing(2),
  },
  ButtonsForm: {
    [theme.breakpoints.down('lg')]: {
      display: 'flex',
      flexDirection: 'row-reverse',
      width: '100%',
      margin: 0,
      marginTop: 24,
    },
    [theme.breakpoints.up('md')]: {
      display: 'flex',
      width: '100%',
      justifyContent: 'flex-start',
      marginTop: theme.spacing(4),
      flexFlow: 'row-reverse',
      '& .MuiButton-text': {
        marginRight: theme.spacing(2),
      },
      '& .MuiButton-root': {
        paddingRight: theme.spacing(4),
        paddingLeft: theme.spacing(4),
        height: 40,
        position: 'relative',
        minWidth: 145,
      },
    },
  },
  validationPassword: {
    padding: 0,
    listStyle: 'none',
  },
  itemValidationPassword: {
    width: '50%',
    float: 'right',
    color: theme.palette.text.gray,
    display: 'flex',
    alignItems: 'center',
    '& svg': {
      fill: '#00c89c',
      fontSize: '.9rem',
      marginLeft: '2px',
    },
    '& svg.dash': {
      fill: theme.palette.text.gray,
    },
  },
  contentInnerMobile: {
    padding: `${theme.spacing(4)} ${theme.spacing(3)}`,
  },
  ListItem: {
    height: 40,
    padding: theme.spacing(2),
    margin: [theme.spacing(1, 0, 0), '!important'],
    borderRadius: theme.spacing(1),
    width: '-webkit-fill-available',
    '& svg': {
      transition: 'transform 0.3s ease-in-out',
      color: '#49454F',
    },
    '& .arrowIcon': {
      fill: theme.palette.text.lightGray,
    },
    '& .MuiListItemText-root': {
      transition: 'transform 0.3s ease-in-out',
      marginRight: 8,
    },

    '& .MuiTypography-body1': {
      fontSize: 15,
      fontWeight: 'bold',
    },
    '& .MuiListItemIcon-root': {
      minWidth: 'unset',
    },
    '&:hover': {
      '& svg': {
        transform: `translate(${theme.spacing(-1.5)} , 0px)`,
      },
      '& .MuiListItemText-root': {
        transform: `translate(${theme.spacing(-1.5)} , 0px)`,
      },
    },
  },
  CollapseItem: {
    marginBottom: 10,
    margin: theme.spacing(0, 3, 0, 1),
  },
  SubListItem: {
    paddingRight: '40px',
  },
  ListItemText2: {
    '& .MuiListItemText-root': {
      marginRight: 8,
    },
    '& .MuiTypography-body1': {
      fontSize: 14,
    },
  },
  miniSidebarItem: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    color: theme.palette.text.third,
    '& p': {
      fontSize: 8,
      marginTop: 3,
    },
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'row',
      '& span': {
        fontSize: '0.875rem',
        marginRight: 4,
        fontWeight: 500,
      },
    },
  },
  miniListItem: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    [theme.breakpoints.down('sm')]: {
      justifyContent: 'right',
      margin: '8px 0',
    },
  },
  listItemSecretariats: {
    bottom: 0,
    // position: 'absolute',
  },
  selectedSidebarItem: {
    color: theme.palette.primary.main,
    '& span': {
      color: theme.palette.primary.main,
    },
    '& svg': {
      transform: `unset !important`,
      fill: theme.palette.primary.main,
    },
    '& .MuiListItemText-root': {
      transform: `unset !important`,
    },
    background: '#faf6ff',
    // margin: '10px 0',
    /* width: 100%; */
    borderRadius: 8,
    [theme.breakpoints.down('sm')]: {
      width: 'unset',
      // margin: [[theme.spacing(1.25, 0)], '!important'],
    },
  },
  menuListItemIcon: {
    minWidth: 'unset',
    paddingLeft: 4,
  },
  circleButtonLabel: {
    display: 'flex',
    flexDirection: 'column',
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'row',
      justifyContent: 'left',
      alignItems: 'center',
    },
  },
  circleButtonRoot: {
    color: theme.palette.background.secondColor,
    '&:hover': {
      background: 'unset',
    },
  },
  circleButtonDone: {
    color: theme.palette.text.success,
    '&:hover': {
      background: 'unset',
    },
  },
  circleButtonDisabled: {
    color: `${theme.palette.text.success}!important`,
    '&:hover': {
      background: 'unset',
    },
  },
  desktopSnackbarContainer: {
    top: '75px !important',
    right: '250px !important',
  },
  mobileSnackbarContainer: {
    zIndex: '9999 !important',
  },
  guidePage: {
    flexGrow: 1,
    padding: '0',
    // background: theme.palette.background.innerPage,
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    marginTop: 65,
    height: 'calc(100vh - 64px)',
    overflowY: 'auto',
    alignItems: 'center',
    position: 'relative',
  },

  unReadLetter: {
    fontWeight: 'bold',
    color: theme.palette.text.dark,
  },
  toastifyTheme: {
    padding: theme.spacing(0),
    boxShadow: 'unset !important',
    background: 'unset !important',
    '&.Toastify__toast-theme--light': {
      boxShadow: 'unset !important',
      background: 'unset !important',
    },
  },
  notification: {
    width: 'inherit',
  },
  notificationBody: {
    padding: theme.spacing(0, 1, 1),
  },
  notificationProgress: {
    background: theme.palette.primary.main,
    bottom: [theme.spacing(0.5), '!important'],
    right: [theme.spacing(1), '!important'],
    width: 'calc(100% - 16px)',
    borderRadius: theme.spacing(0.5),
  },
  errorBoundaryBox: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    textAlign: 'center',
    height: '100%',
    width: '100%',
    '& p': {
      fontSize: 22,
      fontWeight: 'bold',
      [theme.breakpoints.down('sm')]: {
        fontSize: 18,
      },
    },
    '& img': {
      width: 380,
    },
  },
});

const StyledMaterialDesignContent = withStyles(MaterialDesignContent, {
  root: {
    '&.notistack-MuiContent-success': {
      fontSize: '14px !important',
      fontWeight: '500 !important',
      borderRadius: '0 !important',
      borderRight: '8px solid #3fc063 !important',
      background: '#edf7ed !important',
      color: '#000000 !important',
      justifyContent: 'space-between',
      gap: 32,
      '& div:last-child': {
        margin: 0,
        padding: 0,
      },
      '& svg': {
        '&:first-child': {
          color: 'unset !important',
        },
        '&:last-child': {
          color: '#3fc063 !important',
          marginLeft: 8,
        },
      },
    },
    '&.notistack-MuiContent-error': {
      fontSize: '14px !important',
      fontWeight: '500 !important',
      borderRadius: '0 !important',
      borderRight: '8px solid #d31f22 !important',
      background: '#fdeded !important',
      color: '#000000 !important',
      justifyContent: 'space-between ',
      gap: 32,
      '& div:last-child': {
        margin: 0,
        padding: 0,
      },
      '& svg': {
        '&:first-child': {
          color: 'unset !important',
        },
        '&:last-child': {
          color: '#d31f22 !important',
          marginLeft: 8,
        },
      },
    },
    '&.notistack-MuiContent-warning': {
      fontSize: '14px !important',
      fontWeight: '500 !important',
      borderRadius: '0 !important',
      borderLeft: '8px solid orange !important',
      background: '#fff4e5 !important',
      justifyContent: 'space-between ',
      color: '#000000 !important',
      gap: 32,
      '& div:last-child': {
        margin: 0,
        padding: 0,
      },
      '& svg': {
        '&:first-child': {
          color: 'unset !important',
        },
        '&:last-child': {
          color: 'orange !important',
          marginLeft: 8,
        },
      },
    },
    '&.notistack-MuiContent-info': {
      fontSize: '14px !important',
      fontWeight: '500 !important',
      borderRadius: '0 !important',
      borderRight: '8px solid #1f67d3 !important',
      background: '#e5efff !important',
      color: '#000000 !important',
      justifyContent: 'space-between ',
      gap: 32,
      '& div:last-child': {
        margin: 0,
        padding: 0,
      },
      '& svg': {
        '&:first-child': {
          color: 'unset !important',
        },
        '&:last-child': {
          color: '#1f67d3 !important',
          marginLeft: 8,
        },
      },
    },
  },
});

const useStyles = makeStyles()(stylesGenerator);

const SnackbarCloseButton = ({ snackbarKey }) => {
  const { closeSnackbar } = useSnackbar();
  return (
    <AWIconButton onClick={() => closeSnackbar(snackbarKey)}>
      <CloseIcon />
    </AWIconButton>
  );
};

const BaseLayout = ({ translatedPathSegments, urlPathSegments }) => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));
  const [open, setOpen] = useState(true);
  const {
    showAppBar,
    showSideBar,
    isGuidePage,
    showBottomBar,
    isReadOnlyPositionSelection,
    showPositionSelection,
    showBackButton,
    backURL,
    openCheckStatusOveredDialog,
    handleCloseCheckStatusOveredDialog,
    commandName,
  } = useLayout();
  const [selectedItem, setSelectedItem] = useState();
  const [menuInfo, setMenuInfo] = useState({ type: '', id: '' });
  const navigate = useNavigate();
  const { classes, cx } = useStyles();

  const selectClassNameMobile = () => {
    if (!showBottomBar && !showAppBar) {
      return classes.mobileContentWithOutBottomBarTopBar;
    }
    if (!showBottomBar && showAppBar) {
      return classes.mobileContentWithTopBar;
    }
    return classes.mobileContent;
  };

  const selectClassNameDesktop = () => {
    if (isGuidePage) {
      return classes.guidePage;
    }
    if ((showSideBar && showAppBar) || (!showSideBar && showAppBar)) {
      return cx(classes.content, {
        [classes.contentShift]: open,
      });
    }
    return cx(classes.content2);
  };

  const handleDrawerOpen = () => {
    setOpen(!open);
  };

  const errorBoundaryProps = {
    translatedPathSegments,
    handleDrawerOpen,
    urlPathSegments,
    showPositionSelection,
    isReadOnlyPositionSelection,
  };

  const goBack = () => {
    navigate(-1);
  };

  const closeSnackbarActions = useCallback(
    (snackbarKey) => <SnackbarCloseButton snackbarKey={snackbarKey} />,
    [],
  );

  return (
    <SnackbarProvider
      maxSnack={3}
      anchorOrigin={{
        vertical: mobile ? 'bottom' : 'top',
        horizontal: 'right',
      }}
      action={closeSnackbarActions}
      TransitionComponent={Grow}
      iconVariant={{
        success: <CheckCircleIcon fontSize="small" />,
        error: <CancelIcon fontSize="small" />,
        info: <InfoIcon fontSize="small" />,
        warning: <ErrorIcon fontSize="small" />,
      }}
      Components={{
        success: StyledMaterialDesignContent,
        error: StyledMaterialDesignContent,
        info: StyledMaterialDesignContent,
        warning: StyledMaterialDesignContent,
      }}
      classes={{
        containerRoot: mobile && classes.mobileSnackbarContainer,
        containerAnchorOriginTopLeft: showSideBar && classes.desktopSnackbarContainer,
      }}
    >
      <ErrorBoundary {...errorBoundaryProps} showAppBar={showAppBar} mobile={mobile}>
        <Box dir={process.env.REACT_APP_SETTINGS_DIRECTION} id="dir">
          <SnackbarUtilsConfigurator />
          <Box className={mobile ? classes.rootMobile : classes.root}>
            {showAppBar &&
              (mobile ? (
                <AppBarMobile {...{ isReadOnlyPositionSelection, showPositionSelection }} />
              ) : (
                <AppBarDesktop {...errorBoundaryProps} />
              ))}
            {!mobile && showSideBar && <SideBar backURL={backURL} open={open} />}
            <main className={mobile ? selectClassNameMobile() : selectClassNameDesktop()}>
              <Box
                className={cx({
                  [classes.desktopRootPage]: desktop || tablet,
                  [classes.mobileRootPage]: mobile,
                })}
              >
                {!mobile && showBackButton && <BackPaperDesktop {...{ goBack }} />}
                <Suspense fallback={<></>}>
                  {/* <Suspense fallback={<Loading />}> */}
                  <Box
                    className={cx({
                      [classes.rootPage]: desktop || tablet,
                      [classes.mobileRootPage]: mobile,
                    })}
                  >
                    <Outlet />
                    <DialogCheckStatusOvered
                      open={openCheckStatusOveredDialog}
                      onClose={handleCloseCheckStatusOveredDialog}
                      commandName={commandName}
                    />
                  </Box>
                </Suspense>
              </Box>
            </main>
            {mobile && showBottomBar && (
              <BottomNavigationComponent
                {...{
                  selectedItem,
                  setSelectedItem,
                  menuInfo,
                  setMenuInfo,
                }}
              />
            )}
          </Box>
          {!mobile && (
            <ToastContainer
              position="bottom-right"
              autoClose={10000}
              hideProgressBar={false}
              newestOnTop={false}
              closeOnClick
              pauseOnFocusLoss
              pauseOnHover
              transition={Flip}
              limit={1}
              rtl
              // pauseOnHover
              // pauseOnFocusLoss
              closeButton={false}
              draggable={false}
              className={classes.notification}
              toastClassName={classes.toastifyTheme}
              bodyClassName={classes.notificationBody}
              progressClassName={classes.notificationProgress}
            />
          )}
        </Box>
      </ErrorBoundary>
    </SnackbarProvider>
  );
};

const MainLayout = (props) => {
  const { organizationId } = useParams();
  const { signedInUser } = useUser();
  const { showSideBar, selectedPosition, setSelectedPosition } = useLayout();
  const {
    organizationEmployeeContactsReset,
    createContactGroupReset,
    searchContactsReset,
    getOrganizationPositionsReset,
    lettersNotExchangedReset,
    forwardedLetterToMeReset,
    forwardedLetterByMeReset,
    contactGroupContactsReset,
    contactsOrganizationReset,
    organizationContactGroupsReset,
    getIndicatorsReset,
    getSecretariatsReset,
    organizationSolutionsListReset,
    correspondenceRolesReset,
    translatedPathSegments,
    urlPathSegments,
  } = props;

  useEffect(
    () => () => {
      if (organizationId) {
        organizationSolutionsListReset();
        organizationEmployeeContactsReset();
        createContactGroupReset();
        searchContactsReset();
        getOrganizationPositionsReset();
        lettersNotExchangedReset();
        forwardedLetterToMeReset();
        forwardedLetterByMeReset();
        contactGroupContactsReset();
        contactsOrganizationReset();
        organizationContactGroupsReset();
        getIndicatorsReset();
        getSecretariatsReset();
        correspondenceRolesReset();
      }
    },
    [organizationId],
  );

  return (
    <MainLayoutStylesContext.Provider value={{ stylesGenerator }}>
      <BaseLayout {...{ translatedPathSegments, urlPathSegments }} />
      <Task />
    </MainLayoutStylesContext.Provider>
  );
};

export default connect(null, {
  organizationEmployeeContactsReset: organizationEmployeeContactsReset_,
  createContactGroupReset: createContactGroupReset_,
  searchContactsReset: searchContactsReset_,
  getOrganizationPositionsReset: getOrganizationPositionsReset_,
  forwardedLetterToMeReset: forwardedLetterToMeReset_,
  lettersNotExchangedReset: lettersNotExchangedReset_,
  forwardedLetterByMeReset: forwardedLetterByMeReset_,
  contactGroupContactsReset: contactGroupContactsReset_,
  contactsOrganizationReset: contactsOrganizationReset_,
  organizationContactGroupsReset: organizationContactGroupsReset_,
  getIndicatorsReset: getIndicatorsReset_,
  getSecretariatsReset: getSecretariatsReset_,
  organizationSolutionsListReset: organizationSolutionsListReset_,
  correspondenceRolesReset: correspondenceRolesReset_,
})(withDiscoverPath(MainLayout));
