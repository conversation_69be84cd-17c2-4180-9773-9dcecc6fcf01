import React, { useEffect, useState, useRef } from 'react';

import PropTypes from 'prop-types';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useNavigate, useParams } from 'react-router-dom';
import { connect } from 'react-redux';
import { v4 as uuidv4 } from 'uuid';

import { t } from 'i18next';
import { A4, A5 } from 'components/LayoutEditor/defaultLayout';
import { LayoutEditorPageContext } from '../../../../contexts/pageContext/secretariat/layout-editor-page/layout-editor-page-context';
import DesktopLayoutEditorPage from './layout-editor-page-desktop';
import {
  editLetterLayoutAsync as editLetterLayoutAsync_,
  editLetterLayoutReset as editLetterLayoutReset_,
  getLetterLayoutsAsync as getLetterLayoutsAsync_,
  getLetterLayoutsReset as getLetterLayoutsReset_,
  getSecretariatsAsync as getSecretariatsAsync_,
} from '../secretariatsSlice';
import { organizationInfoAsync as organizationInfoAsync_ } from '../../../management-center/organization/organizationSlice';
import snackbarUtils from '../../../../utils/snackbarUtils';
import { p2c } from '../../../../utils';
import { withCorrespondenceInitializer } from '../../withCorresponseInitializer';
import {
  documentsAsync as documentsAsync_,
  uploadDocumentsAsync as uploadDocumentsAsync_,
} from '../../../documents/documentsSlice';
import { useLayout } from '../../../../contexts/layoutContext';
import { getAllTokenFromSections } from './utils';

const LayoutEditorPage = ({
  documents,
  secretariats,
  getLetterLayoutsAsync,
  editLetterLayoutAsync,
  getLetterLayoutsReset,
  editLetterLayoutReset,
  getSecretariatsAsync,
  organizationInfoAsync,
  organization,
  documentsAsync,
  uploadDocumentsAsync,
}) => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));
  const params = useParams();
  const navigate = useNavigate();
  const { secretariatId, layoutId, organizationId } = params;
  const { selectedPosition } = useLayout();
  const [layout, setLayout] = useState(null);
  const [layoutLoading, setLayoutLoading] = useState(false);
  const [layoutState, setLayoutState] = useState({
    name: '',
    size: 'A4',
    sizeInput: 'A4',
    description: '',
    isDefault: false,
    layoutId: '',
  });
  const [saveLayoutButtonLoading, setSaveLayoutButtonLoading] = useState(false);
  const [openBackdrop, setOpenBackdrop] = useState(false);
  const [openLetterLayoutPreview, setOpenLetterLayoutPreview] = useState(false);
  const [tokenSelected, setTokenSelected] = useState({});
  const [fontFamilySelect, setFontFamilySelect] = useState(layout?.paper?.fontFamily || 'vazir');
  const [maxStaticText] = useState(3);
  const [staticTextCount, setStaticTextCount] = useState(0);
  const [backgrundFormData, setBackgrundFormData] = useState(null);
  const [backgroundBase64, setBackgroundBase64] = useState(null);

  const [undoBuffer, setUndoBuffer] = useState([]); // Previous Layout
  const [redoBuffer, setRedoBuffer] = useState([]); // Next Layout

  const layoutRef = useRef(null);

  const [scale, setScale] = useState({
    stageScale: 1,
    stageX: null,
    stageY: null,
    fitScale: 1,
  });

  useEffect(() => {
    if (layout?.paper?.fontFamily) {
      setFontFamilySelect(layout.paper.fontFamily);
    }
  }, [layout?.paper?.fontFamily]);

  const handleRenderLayout = () => {
    if (mobile) return <></>;
    if (desktop || tablet) return <DesktopLayoutEditorPage />;
    return <></>;
  };

  useEffect(() => {
    getLetterLayouts();
    getOrganizationInfo();
    getSecretariatsAsync({ path: organizationId });
  }, []);

  useEffect(() => {
    if (secretariats.letterLayoutsList.data) {
      const selectedLayout = secretariats.letterLayoutsList.data?.find(
        (item) => item.layoutId === layoutId,
      );

      const layoutData = JSON.parse(JSON.stringify(selectedLayout.layout));

      const allTokens = getAllTokenFromSections(layoutData.sections);

      const allTokensDefault = getAllTokenFromSections({ A4, A5 }[selectedLayout.size].sections);
      const defaultReserved = Object.keys(allTokensDefault);

      const notExistedToken = defaultReserved.filter((item) => !allTokens[item]);
      const notExistedTokenObject = notExistedToken?.reduce(
        (result, item) => ({ ...result, [item]: allTokensDefault[item] }),
        {},
      );

      layoutData.sections.reserved.children = {
        ...layoutData.sections.reserved.children,
        ...notExistedTokenObject,
      };

      // layoutData
      const backgroundId = layoutData.paper.background?.id;
      setLayout(layoutData);
      if (!backgroundId) {
        setLayoutLoading(false);
      } else {
        getLayoutBackground(backgroundId);
      }
      setLayoutState({
        name: selectedLayout.name,
        size: selectedLayout.size,
        sizeInput: selectedLayout.size,
        description: selectedLayout.description,
        isDefault: selectedLayout.isDefault,
        layoutId: selectedLayout.layoutId,
      });
    }
  }, [secretariats.letterLayoutsList.data]);

  useEffect(() => {
    if (secretariats.editLetterLayout?.success) {
      getLetterLayoutsReset();
      getLetterLayouts();
      setSaveLayoutButtonLoading(false);
      editLetterLayoutReset();
      setOpenBackdrop(false);
      handleCancelEditLayout();
    }
    if (secretariats.editLetterLayout?.success === false) {
      setSaveLayoutButtonLoading(false);
      setOpenBackdrop(false);
    }
  }, [secretariats.editLetterLayout.data]);

  useEffect(() => {
    if (layout?.sections) {
      staticTextCounter(layout.sections);
      setUndoBuffer((prevState) => [...prevState, layout]);
    }
  }, [layout]);

  const handleUndo = () => {
    const lastChange = undoBuffer.slice(undoBuffer.length - 2, undoBuffer.length - 1)[0];
    const newUndoBuffer = undoBuffer.slice(0, undoBuffer.length - 2);
    setUndoBuffer(newUndoBuffer);
    setLayout(lastChange);
  };

  const handleRedo = () => {};

  const getLetterLayouts = () => {
    setLayoutLoading(true);
    const options = {
      secretariatId,
    };
    getLetterLayoutsAsync(options);
  };

  const getLayoutBackground = (backgroundId) => {
    const options = {
      path: backgroundId,
      responseType: 'arraybuffer',
    };
    documentsAsync(options);
  };

  useEffect(() => {
    const document = documents.documents.fetched;
    if (document && layout) {
      const backgroundId = layout.paper.background.id;
      const background = document[backgroundId]?.data;
      setBackgroundBase64(background);
      setLayoutLoading(false);
      setLayout((prevLayout) => ({
        ...prevLayout,
        paper: {
          ...prevLayout.paper,
          background: {
            ...prevLayout.paper.background,
            imageBase64: background,
          },
        },
      }));
    }
  }, [documents.documents.fetched]);

  const getOrganizationInfo = () => {
    const options = {
      organizationId,
    };
    organizationInfoAsync(options);
  };

  const staticTextCounter = (data) => {
    let count = 0;
    const staticTextFinder = (sectionData) => {
      const staticTextTokens = Object.keys(sectionData).filter(
        (token) => token.split('#')[0] === 'staticText',
      );
      count += staticTextTokens.length;
    };

    for (const section of Object.keys(data)) {
      if (section !== 'reserved' && section !== 'content') {
        for (const subsection of Object.keys(data[section])) {
          staticTextFinder(data[section][subsection].children);
        }
      }
      if (section === 'reserved') {
        staticTextFinder(data[section].children);
      }
    }
    setStaticTextCount(count);
  };

  const handleSaveLayoutData = () => {
    const name = layoutState?.name;
    const description = layoutState?.description;
    const size = layoutState?.size;
    const isDefault = layoutState?.isDefault;
    editLetterLayoutAsync({ data: getRequestData(name, description, size, isDefault) });
  };

  const handleSaveEditLayout = () => {
    setSaveLayoutButtonLoading(true);
    setOpenBackdrop(true);
    if (backgrundFormData) {
      const correlationId = uuidv4();
      const headers = {
        domain: 'correspondence',
        organization: organizationId,
        position: selectedPosition.position.id,
      };
      uploadDocumentsAsync({ correlationId, data: backgrundFormData, headers }).then((result) => {
        const backgroundId = result.payload[0].id;
        changeState(['paper', 'background'], 'id', backgroundId);
        handleSaveLayoutData();
      });
    } else {
      handleSaveLayoutData();
    }
  };

  const getRequestData = (name, description, size, isDefault) => ({
    description,
    id: secretariatId,
    layout,
    layoutId,
    name,
    organization: { id: organizationId },
    secretariat: { id: secretariatId },
    size,
    isDefault,
  });

  const handleCancelEditLayout = () => {
    navigate(
      `/organizations/${organizationId}/correspondence/correspondence-management/secretariats/${secretariatId}`,
    );
  };

  const handleAddBackground = (e) => {
    e.preventDefault();
    const reader = new FileReader();
    const file = e.target.files[0];
    if (file.size > 512 * 10 ** 3) {
      return snackbarUtils.error(t('behaviorToken.labels.sizeFile'));
    }
    if (reader !== undefined && file !== undefined) {
      reader.onloadend = () => {
        setLayout({
          ...layout,
          paper: {
            ...layout.paper,
            background: {
              imageBase64: reader.result,
            },
          },
        });
        setBackgroundBase64(reader.result);
      };
      reader.readAsDataURL(file);

      const form = new FormData();
      form.append('file', file);
      form.append('domain', 'correspondence');
      form.append('organization', JSON.stringify({ id: organizationId }));
      form.append('position', JSON.stringify({ id: selectedPosition.position.id }));
      return setBackgrundFormData(form);
    }
  };

  const handleRemoveBackground = (e) => {
    e.preventDefault();
    setBackgroundBase64(null);
    setBackgrundFormData(null);
    setLayout({
      ...layout,
      paper: { ...layout.paper, background: {} },
    });
  };

  const handleShowLetterPreview = () => {
    setOpenLetterLayoutPreview(true);
  };

  const handleCloseLetterLayoutPreview = () => {
    setOpenLetterLayoutPreview(false);
  };

  const handleChangeFontFamily = (e) => {
    setFontFamilySelect(e.target.value);
    setLayout({ ...layout, paper: { ...layout.paper, fontFamily: e.target.value } });
  };

  const fontSizeRange = (value) => {
    if (value <= 0) return 1;
    if (value > 40) return 40;
    return value;
  };

  const handleChangeFontSize = (e) => {
    const { value } = e.currentTarget;
    setLayout({
      ...layout,
      paper: { ...layout.paper, letterFontSize: fontSizeRange(value) },
    });
  };

  const changeState = (path, key, value) => {
    const newData = { ...layout };
    path.reduce((obj, item, index) => {
      if (index === path.length - 1) {
        return (obj[item] = { ...obj[item], [key]: value });
      }
      return obj[item];
    }, newData);

    setLayout(newData);
  };

  const handleChangeTokenFontSize = (e, path) => {
    const { value } = e.target;
    changeState(path, 'fontSize', fontSizeRange(value));
  };

  const handleChangeTokenLabel = (e, path) => {
    const { value } = e.target;
    changeState(path, 'label', value);
  };

  const handleChangeTokenFontWeight = (e, path) => {
    const { checked } = e.currentTarget;
    if (checked) {
      changeState(path, 'fontWeight', 'bold');
    } else {
      changeState(path, 'fontWeight', 'normal');
    }
  };

  const handleChangeIsSingleLine = (e, path) => {
    const { checked } = e.target;
    changeState(path, 'isSingleLine', !checked);
  };

  const handleChangeFullNameOnly = (e, path) => {
    const { checked } = e.target;
    changeState(path, 'isFullNameOnly', !checked);
  };

  const handleChangeIsTitleFullNameFirst = (e, path) => {
    const { value } = e.target;
    changeState(path, 'isTitleFullNameFirst', Number(value));
  };

  const handleChangeSignerName = (e, path) => {
    const { checked } = e.target;
    changeState(path, 'isSignerName', checked);
  };

  const handleChangeSignerNameDirection = (e, path) => {
    const { value } = e.target;
    changeState(path, 'signerNameDirection', value);
  };
  const handleChangeSignerNameOffset = (e, path) => {
    const { value } = e.target;
    changeState(path, 'signerNameOffset', +value);
  };
  const handleChangeSignerNameOnTop = (e, path) => {
    const { checked } = e.target;
    changeState(path, 'signerNameOnTop', checked);
  };

  const handleChangeUseEnglishDigits = (e, path) => {
    const { checked } = e.target;
    changeState(path, 'useEnglishDigits', checked);
  };

  const handleChangeMaxSize = (e, path) => {
    const { value } = e.target;
    changeState(path, 'maxSize', value < 0 ? 100 : value);
  };

  const handleChangeTextAlignment = (e, path) => {
    const { value } = e.target;
    changeState(path, 'align', value);
  };

  const handleChangeTokenProperties = (path, propertyName, value) => {
    changeState(path, propertyName, value);
  };

  const handleAddStaticTextToken = () => {
    const paperWidth = layout.paper.width;
    const id = uuidv4();
    const value = {
      label: t('secretariats.labels.Text'),
      right: paperWidth + p2c(70),
      top: Math.floor((Math.random() * layout.paper.height) / 1.2) + 2,
    };

    const newData = {
      ...layout,
      sections: {
        ...layout.sections,
        reserved: {
          ...layout.sections.reserved,
          children: {
            ...layout.sections.reserved.children,
            [`staticText#${id}`]: value,
          },
        },
      },
    };
    setLayout(newData);
  };

  const handleDeleteToken = (e, path) => {
    const newData = { ...layout };
    path.reduce((obj, item, index) => {
      if (index === path.length - 1) {
        delete obj[item];
      }
      return obj[item];
    }, newData);

    setLayout(newData);
    setTokenSelected({});
  };

  const handleResetScale = () => {
    layoutRef.current?.resetScale();
  };

  const handleZoomIn = () => {
    layoutRef.current?.zoom(scale.stageScale + 0.25 > 4 ? 4 : scale.stageScale + 0.25);
  };

  const handleZoomOut = () => {
    layoutRef.current?.zoom(scale.stageScale - 0.25 < 0.5 ? 0.5 : scale.stageScale - 0.25);
  };

  return (
    <LayoutEditorPageContext.Provider
      value={{
        layout,
        setLayout,
        layoutState,
        layoutLoading,
        handleSaveEditLayout,
        openBackdrop,
        saveLayoutButtonLoading,
        handleCancelEditLayout,
        handleShowLetterPreview,
        openLetterLayoutPreview,
        handleCloseLetterLayoutPreview,
        userOrganization: organization.organizationInfo.data,
        fontFamilySelect,
        handleChangeFontFamily,
        handleChangeFontSize,
        handleAddBackground,
        handleRemoveBackground,
        tokenSelected,
        setTokenSelected,
        handleChangeTokenFontSize,
        handleChangeTokenLabel,
        handleChangeIsSingleLine,
        handleChangeFullNameOnly,
        handleChangeIsTitleFullNameFirst,
        handleChangeSignerName,
        handleChangeSignerNameDirection,
        handleChangeSignerNameOffset,
        handleChangeSignerNameOnTop,
        handleAddStaticTextToken,
        handleChangeUseEnglishDigits,
        handleChangeTokenProperties,
        handleDeleteToken,
        maxStaticText,
        staticTextCount,
        redoBuffer,
        undoBuffer,
        handleRedo,
        handleUndo,
        scale,
        setScale,
        handleZoomIn,
        handleZoomOut,
        handleResetScale,
        layoutRef,
        handleChangeTextAlignment,
        handleChangeMaxSize,
        handleChangeTokenFontWeight,
        backgroundBase64,
      }}
    >
      {handleRenderLayout()}
    </LayoutEditorPageContext.Provider>
  );
};

LayoutEditorPage.propTypes = {
  secretariats: PropTypes.shape({
    letterLayoutsList: PropTypes.shape({
      data: PropTypes.arrayOf(PropTypes.shape({})),
    }),
    editLetterLayout: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
      }),
    }),
  }),
  getLetterLayoutsAsync: PropTypes.func.isRequired,
  editLetterLayoutAsync: PropTypes.func.isRequired,
  getLetterLayoutsReset: PropTypes.func.isRequired,
  editLetterLayoutReset: PropTypes.func.isRequired,
  getSecretariatsAsync: PropTypes.func.isRequired,
  organizationInfoAsync: PropTypes.func.isRequired,
};

LayoutEditorPage.defaultProps = {
  secretariats: {
    letterLayoutsList: {
      data: [],
      success: false,
    },
    editLetterLayout: {
      data: null,
      success: false,
    },
  },
};

const mapStateToProps = (state) => {
  const { secretariats, organization, documents } = state;
  return { secretariats, organization, documents };
};

export default withCorrespondenceInitializer(
  connect(mapStateToProps, {
    getLetterLayoutsAsync: getLetterLayoutsAsync_,
    editLetterLayoutAsync: editLetterLayoutAsync_,
    getLetterLayoutsReset: getLetterLayoutsReset_,
    editLetterLayoutReset: editLetterLayoutReset_,
    getSecretariatsAsync: getSecretariatsAsync_,
    organizationInfoAsync: organizationInfoAsync_,
    documentsAsync: documentsAsync_,
    uploadDocumentsAsync: uploadDocumentsAsync_,
  })(LayoutEditorPage),
);
