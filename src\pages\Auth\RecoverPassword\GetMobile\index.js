import React from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import MobileGetMobile from './get-mobile-mobile';
import TabletGetMobile from './get-mobile-tablet';
import DesktopGetMobile from './get-mobile-desktop';

const GetMobile = () => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));

  if (mobile) {
    return <MobileGetMobile isMobile />;
  }
  if (tablet) {
    return <TabletGetMobile />;
  }
  if (desktop) {
    return <DesktopGetMobile />;
  }
  return <></>;
};

export default GetMobile;
