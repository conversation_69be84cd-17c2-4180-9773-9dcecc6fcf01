import React from 'react';
import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import loadingPrimary from '../../assets/images/gif/3Dot-Loading-Primary.gif';
import loadinglightGray from '../../assets/images/gif/3Dot-Loading-Light-Gray.gif';
import loadingWhite from '../../assets/images/gif/3Dot-Loading-WHITE.gif';

const useStyles = makeStyles()((theme) => ({
  loadingBox: {
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    '& img': {
      width: 40,
      [theme.breakpoints.down('sm')]: {
        width: 25,
      },
    },
  },
  loadingTable: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    left: 0,
    right: 0,
    '& img': {
      width: 40,
      [theme.breakpoints.down('sm')]: {
        width: 30,
      },
    },
  },
  infinitLoading: {
    textAlign: 'center',
    '& img': {
      width: 28,
      [theme.breakpoints.down('sm')]: {
        width: 40,
      },
    },
  },
  loadingBoxSidebar: {
    display: 'block',
    position: 'relative',
    textAlign: 'center',
    width: 45,
    '& img': {
      width: '100%',
    },
  },
}));

const Loading = ({ color, type, inline, className }) => {
  const { classes, cx } = useStyles();

  const selectClassName = (_type) => {
    if (_type === 'loadingBoxSidebar') return cx(classes.loadingBoxSidebar);
    if (_type === 'infiniteLoading') return cx(classes.infinitLoading);
    if (_type === 'table') return cx(classes.loadingTable);
    if (inline) return '';
    return cx(classes.loadingBox);
  };

  const selectSrc = (_color) => {
    if (_color === 'lightGray') return loadinglightGray;
    if (_color === 'white') return loadingWhite;
    return loadingPrimary;
  };
  const combinedClassName = selectClassName(type) || className || undefined;

  return (
    <div className={combinedClassName}>
      <img src={selectSrc(color)} alt="loading" />
    </div>
  );
};

Loading.propTypes = {
  color: PropTypes.string,
  type: PropTypes.string,
  inline: PropTypes.bool,
  className: PropTypes.string,
};
Loading.defaultProps = {
  type: '',
  color: '',
  inline: false,
  className: '',
};
export default Loading;
