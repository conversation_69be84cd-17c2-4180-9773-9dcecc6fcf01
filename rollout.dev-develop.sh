docker build -t com.chargoon.cloud.front.spa:0.0.90 --build-arg GOOGLE_TAG_MANAGER_ID=GTM-KPLS52TC .
docker tag com.chargoon.cloud.front.spa:0.0.90 devbuild-srv.chargoon.net:32001/com.chargoon.cloud.front.spa:0.0.90
docker push devbuild-srv.chargoon.net:32001/com.chargoon.cloud.front.spa:0.0.90
cat deployment.dev-develop.yaml | ssh -p 22443 -i ~/devbuild <EMAIL> kubectl apply -f -
ssh -p 22443 -i ~/devbuild <EMAIL> kubectl rollout restart deployment com-chargoon-cloud-front-spa -n develop
