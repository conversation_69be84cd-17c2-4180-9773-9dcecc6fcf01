import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import Tab from '@mui/material/Tab';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import RadioGroup from '@mui/material/RadioGroup';
import Radio from '@mui/material/Radio';
import DeleteForeverOutlinedIcon from '@mui/icons-material/DeleteForeverOutlined';

import AWBox from 'components/AWComponents/AWBox';
import { useLetter } from 'contexts/pageContext/letters/letterContext';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import AWIconButton from 'components/AWComponents/AWIconButton';
import { NoData } from 'components/no-data';
import Loading from 'components/Loading';

type TabDataProps = {
  label: string;
  value: string;
  list: string[];
  selectedValue: string;
  onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isRemovable: boolean;
  isLoading?: boolean;
};

const requestList = [
  'جهت بررسی و اقدام لازم با هماهنگی',
  'سلام، خواهشمندم با این درخواست موافقت کنید.',
  'سلام، با دستور شما این کار انجام خواهد شد.',
  'سلام، خواهشمندم اطلاعات بیشتری را در مورد این نامه برای من بفرستید.',
  'سلام، خواهشمندم من را در پیشبرد این نامه راهنمایی کنید.',
  'سلام، خواهشمندم محتوای این نامه را بررسی کنید و نتیجه را به من اطلاع دهید.',
  'سلام، خواهشمندم پاسخ این نامه را تهیه کنید.',
  'سلام، خواهشمندم با برگزاری جلسه در این مورد موافقت کنید.',
];

const answerList = [
  'سلام، بر اساس آیین‌نامه با ایشان همکاری کنید.',
  'سلام، بی‌درنگ اقدام کنید.',
  'سلام، اقدام کنید.',
  'سلام، در جریان باشید.',
  'سلام، اطلاعاتی که درخواست کرده بودید را به همین نامه پیوست کردم.',
];

const useStyles = makeStyles()((theme) => ({
  root: {
    width: '100%',
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  tabList: {
    flexShrink: 0,
  },
  tabPanelContainer: {
    flexGrow: 1,
    overflowY: 'auto',
  },
  formControlLabel: {
    flex: 1,
    marginRight: theme.spacing(1),
    minWidth: 0,
  },
  labelWrapper: {
    display: 'flex',
    alignItems: 'center',
    minWidth: 0,
    flex: 1,
  },
  labelText: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    minWidth: 0,
    display: 'block',
  },
  button: {
    borderRadius: theme.spacing(0.5),
    flexShrink: 0,
  },
  fieldset: {
    display: 'flex',
    flexDirection: 'column',
    marginTop: theme.spacing(1),
  },
  noDataCustom: {
    minHeight: 200,
  },
  listItemBox: {
    display: 'flex',
    alignItems: 'center',
    borderBottom: '1px solid ',
    borderColor: theme.palette.background.light,
    padding: theme.spacing(1, 0),
    width: '100%',
  },
  deleteIcon: {
    color: theme.palette.text.error,
  },
  deleteIconDisabled: {
    color: theme.palette.text.disablesText,
  },
}));

const PreParaphContent = () => {
  const { classes } = useStyles();
  const { t } = useTranslation();

  const {
    tabPreParaphValue,
    handleChangeTabPreParaph,
    selectedRadio,
    forwardNotesSamples,
    handleDeleteForwardNotesSamples,
    handleChangePreParaphRadio,
    deleteForwardNoteSampleLoading,
    isLoadingForwardNoteSamples,
    security,
  } = useLetter();

  const tabData: TabDataProps[] = [
    {
      label: t('correspondence.letters.labels.create-forward-letter'),
      value: 'forwardNotesSamples',
      list: forwardNotesSamples,
      selectedValue: selectedRadio.forwardNotesSamples || '',
      onChange: handleChangePreParaphRadio('forwardNotesSamples'),
      isRemovable: true,
      isLoading: isLoadingForwardNoteSamples,
    },
    {
      label: t('correspondence.letters.labels.request'),
      value: 'request',
      list: requestList,
      selectedValue: selectedRadio.request || '',
      onChange: handleChangePreParaphRadio('request'),
      isRemovable: false,
    },
    {
      label: t('correspondence.letters.labels.answer'),
      value: 'answer',
      list: answerList,
      selectedValue: selectedRadio.answer || '',
      onChange: handleChangePreParaphRadio('answer'),
      isRemovable: false,
    },
  ];

  const renderItem = (tab: TabDataProps, item: any) => {
    const note = typeof item === 'string' ? item : item.note;
    const id = typeof item === 'string' ? item : item.id;
    const isCurrentDeleting = deleteForwardNoteSampleLoading === id;
    const isAnyDeleting = Boolean(deleteForwardNoteSampleLoading);

    return (
      <AWBox key={id} className={classes.listItemBox}>
        <FormControlLabel
          value={id}
          control={<Radio size="small" />}
          disabled={isAnyDeleting}
          classes={{
            root: classes.formControlLabel,
            label: classes.labelWrapper,
          }}
          label={<span className={classes.labelText}>{note}</span>}
        />
        {tab.isRemovable && (
          <AWIconButton
            className={classes.button}
            onClick={() => handleDeleteForwardNotesSamples(id)}
            size="small"
            tooltip={t('tooltip.delete-from-list')}
            disabled={isAnyDeleting}
            security={security.deleteForwardNote}
          >
            {isCurrentDeleting ? (
              <Loading color="primary" type="infiniteLoading" />
            ) : (
              <DeleteForeverOutlinedIcon
                fontSize="small"
                className={isAnyDeleting ? classes.deleteIconDisabled : classes.deleteIcon}
              />
            )}
          </AWIconButton>
        )}
      </AWBox>
    );
  };

  const renderTabPanelContent = (tab: TabDataProps) => {
    if (tab.isLoading) {
      return (
        <AWBox>
          <Loading />
        </AWBox>
      );
    }

    if (tab.list.length === 0) {
      return (
        <AWBox>
          <NoData className={classes.noDataCustom} />
        </AWBox>
      );
    }

    return tab.list.map((item) => renderItem(tab, item));
  };

  return (
    <AWBox className={classes.root}>
      <TabContext value={tabPreParaphValue}>
        <TabList onChange={handleChangeTabPreParaph} className={classes.tabList}>
          {tabData.map((tab) => (
            <Tab key={tab.value} label={tab.label} value={tab.value} />
          ))}
        </TabList>

        <div className={classes.tabPanelContainer}>
          {tabData.map((tab) => (
            <TabPanel key={tab.value} value={tab.value}>
              <FormControl component="fieldset" className={classes.fieldset}>
                <RadioGroup
                  name={`${tab.value}-radio-group`}
                  value={tab.selectedValue}
                  onChange={tab.onChange}
                >
                  {renderTabPanelContent(tab)}
                </RadioGroup>
              </FormControl>
            </TabPanel>
          ))}
        </div>
      </TabContext>
    </AWBox>
  );
};

export default PreParaphContent;
