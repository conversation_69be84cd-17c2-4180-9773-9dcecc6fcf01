import React from 'react';

import { makeStyles } from 'tss-react/mui';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Box from '@mui/material/Box';
import Divider from '@mui/material/Divider';
import InfoIcon from '@mui/icons-material/InfoOutlined';

import { t } from 'i18next';
import AWTextField from '../../../../../components/AWComponents/AWTextField';
import { IOrganizationLegalInfo } from '../Organization.model';

const useStyles = makeStyles()((theme) => ({
  headerContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing(1),
    [theme.breakpoints.down('sm')]: {
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
  },
  title: {
    marginTop: theme.spacing(3),
    color: theme.palette.text.gray,
    fontSize: theme.spacing(1.75),
    fontWeight: 700,
  },
  head: {
    // color: theme.palette.text.black,
    fontSize: theme.spacing(2),
    fontWeight: 700,
  },
  description: {
    display: 'flex',
    flexDirection: 'row',
    gap: theme.spacing(0.5),
    alignItems: 'center',
    flex: 1,
    color: theme.palette.text.gray,
    fontSize: theme.spacing(1.5),
    fontWeight: 500,
  },
  paddingTop: {
    paddingTop: '12px !important',
  },
  textArea: {
    height: 'unset',
  },
  titleBox: {
    '& p': {
      display: 'flex',
      alignItems: 'center',
      fontWeight: 'bold',
    },
    '& p:after': {
      content: '""',
      flex: 1,
      height: '1px',
      backgroundColor: theme.palette.text.lightGray,
      marginRight: 8,
    },
    '& .infoIcon': {
      fontSize: 14,
      margin: theme.spacing(0, 1, 0, 0.5),
    },
    '& span': {
      color: theme.palette.text.gray,
      fontSize: '0.75rem',
      fontWeight: 100,
    },
  },
}));

export interface RegistryInfoProps {
  showTitle: boolean;
  state: IOrganizationLegalInfo;
  onChange: () => {};
  isInvalid: (value: string) => boolean;
  getValidationError: (value: string) => string | null | undefined;
}

const RegistryInfo = ({
  showTitle,
  state,
  onChange,
  isInvalid,
  getValidationError,
}: RegistryInfoProps) => {
  const { classes } = useStyles();

  const fields = [
    { name: 'businessName', value: state.businessName, label: t('invoice.label.business-name') },
    {
      name: 'registeredCode',
      value: state.registeredCode,
      label: t('invoice.label.registeredCode'),
    },
    { name: 'nationalId', value: state.nationalId, label: t('contacts.labels.nationalId') },
    {
      name: 'commercialCode',
      value: state.commercialCode,
      label: t('invoice.label.economic-code'),
    },
    {
      name: 'legalRepresentativeName',
      value: state.legalRepresentativeName,
      label: t('invoice.label.legalRepresentativeName'),
    },
    {
      name: 'legalRepresentativePosition',
      value: state.legalRepresentativePosition,
      label: t('invoice.label.legalRepresentativePosition'),
    },
    {
      name: 'legalRepresentativeNationalId',
      value: state.legalRepresentativeNationalId,
      label: t('invoice.label.legalRepresentativeNationalId'),
    },
  ];

  return (
    <>
      {showTitle && (
        <>
          <Grid item xs={12} className={classes.titleBox}>
            <Box className={classes.headerContainer}>
              <Typography className={classes.head}>
                {t('contacts.labels.legalInformation')}
              </Typography>
              <Typography className={classes.description}>
                <InfoIcon fontSize="small" />
                {t('invoice.label.completeLegalInformation')}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <Typography className={classes.title}>
              - {t('secretariats.labels.registerInformation')}
            </Typography>
          </Grid>
        </>
      )}
      {fields.map((item) => (
        <Grid item xs={12}>
          <AWTextField
            name={item.name}
            fullWidth
            label={item.label}
            variant="outlined"
            value={item.value}
            onChange={onChange}
            error={isInvalid(item.name)}
            helperText={getValidationError(item.name) || ''}
          />
        </Grid>
      ))}
    </>
  );
};

export default RegistryInfo;
