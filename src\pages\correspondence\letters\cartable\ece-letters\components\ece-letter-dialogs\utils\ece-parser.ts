// import * as xml2js from 'xml2js';
// import { Ece } from './ece.interface';
// import { XmlField } from './xml-field.interface';

// // eslint-disable-next-line no-shadow
// enum ReferenceType {
//   FOLLOWING = 'Following',
//   RETURNING = 'Returning',
//   REFERRING = 'Referring',
//   ATTACHING = 'Attaching',
//   REPLYING = 'Replying',
//   RELATING = 'Relating',
// }

// export class ReferenceDto {
//   referenceType: ReferenceType;

//   referenceNumber: string;

//   referenceDate: string;
// }

// export const parseEce = async (file: ArrayBuffer) => {
//   const parser = new xml2js.Parser();
//   // parse xml
//   let sourceObject: Ece;
//   try {
//     sourceObject = (await parser.parseStringPromise(file)).Letter;
//   } catch (e) {
//     const msg = 'failed to parse xml file';
//     return {
//       isError: true,
//       error: msg,
//       data: null,
//     };
//   }
//   // destructure parsed ece
//   let title: string;
//   let mainReceivers: { Name: string; Position: string }[];
//   let otherReceivers: { Name: string; Position: string }[];
//   let receiversDetails: { Name: string; Position: string }[];
//   let attachmentsDetails: IAttachment[];
//   let originsDetails: any[];
//   let receiversFullNames: string[];
//   let receiversPositionsTitles: string[];
//   let senderFullName: string;
//   let incomingNumber: string;
//   let senderPositionTitle: string;
//   let priority: string;
//   let confidentiality: string;
//   let references: ReferenceDto[];
//   let date: XmlField | string;

//   // console.log(sourceObject.LetterDateTime[0]);

//   try {
//     title = sourceObject.Subject[0] as string;

//     attachmentsDetails = sourceObject.Attachments.map((o) => o.Attachment)[0].map((value: any) => ({
//       contentType: value.$?.ContentType,
//       description: value.$?.Description,
//       extension: value.$?.Extension,
//       data: value._,
//     }));
//     [originsDetails] = sourceObject.Origins.map((o) => o.Origin);
//     mainReceivers = sourceObject.Receiver.map((rec) => rec.$);
//     const mainReceiversFullNames = mainReceivers.map((rec) => rec.Name);
//     const mainReceiversPositions = mainReceivers.map((rec) => rec.Position);
//     otherReceivers =
//       Array.isArray(sourceObject.OtherReceivers) &&
//       sourceObject?.OtherReceivers?.[0]?.OtherReceiver?.[0]?.$
//         ? sourceObject.OtherReceivers[0].OtherReceiver.map((o) => o.$)
//         : [];
//     const otherReceiversFullNames = otherReceivers.map((rec) => rec.Name);
//     const otherReceiversPositions = otherReceivers.map((rec) => rec.Position);
//     receiversDetails = [...mainReceivers, ...otherReceivers];
//     receiversFullNames = [...mainReceiversFullNames, ...otherReceiversFullNames];
//     receiversPositionsTitles = [...mainReceiversPositions, ...otherReceiversPositions];
//     senderFullName = sourceObject.Sender[0].$.Name;
//     senderPositionTitle = sourceObject.Sender[0].$.Position;
//     priority = sourceObject.Priority[0].$.Name;
//     confidentiality = sourceObject.Classification[0].$.Name;
//     const numberDetails = sourceObject.LetterNo[0];
//     incomingNumber = typeof numberDetails === 'string' ? numberDetails : numberDetails._;
//     const relatedLetters = sourceObject.RelatedLetters?.filter((r) => r);
//     references = relatedLetters?.[0]?.RelatedLetter?.map(
//       ({ RelationType, RelatedLetterNo, RelatedLetterDateTime }): ReferenceDto => ({
//         referenceType: RelationType[0].$.Name,
//         referenceNumber:
//           typeof RelatedLetterNo[0] === 'string' ? RelatedLetterNo[0] : RelatedLetterNo[0]._,
//         referenceDate:
//           typeof RelatedLetterDateTime[0] === 'string'
//             ? RelatedLetterDateTime[0]
//             : RelatedLetterDateTime[0]._,
//       }),
//     );

//     const dateDetails = sourceObject.LetterDateTime[0];
//     date = typeof dateDetails === 'string' ? dateDetails : dateDetails._;
//   } catch (e) {
//     const msg = `bad ece structure in organization`;
//     return {
//       isError: true,
//       error: msg,
//       data: null,
//     };
//   }

//   return {
//     isError: false,
//     error: '',
//     data: {
//       title,
//       mainReceivers,
//       otherReceivers,
//       receiversDetails,
//       attachmentsDetails,
//       originsDetails,
//       receiversFullNames,
//       receiversPositionsTitles,
//       senderFullName,
//       incomingNumber,
//       senderPositionTitle,
//       priority,
//       confidentiality,
//       references,
//       date,
//     },
//   };
// };

// export interface IEceInfo {
//   title: string;
//   mainReceivers: {
//     Name: string;
//     Position: string;
//   }[];
//   otherReceivers: {
//     Name: string;
//     Position: string;
//   }[];
//   receiversDetails: {
//     Name: string;
//     Position: string;
//   }[];
//   attachmentsDetails: IAttachment[];
//   originsDetails: any[];
//   receiversFullNames: string[];
//   receiversPositionsTitles: string[];
//   senderFullName: string;
//   incomingNumber: string;
//   senderPositionTitle: string;
//   priority: string;
//   confidentiality: string;
//   references: ReferenceDto[];
//   date: XmlField | string;
// }

// export interface IAttachment {
//   contentType: string;
//   description: string;
//   extension: string;
//   data: string;
// }

export interface IEceInfo {
  type: string;
  sender: {
    _id: string;
    firstName: string;
    lastName: string;
    business: string;
    jobTitle: string;
    nationalId: string;
    email: string;
    phone: string;
    mobile: string;
    fax: string;
    province: string;
    city: string;
    address: string;
    description: string;
    organization: {
      id: string;
    };
    contactGroup: {
      id: string;
    };
    createdAt: string;
    updatedAt: string;
    __v: 0;
  }[];
  recipient: {
    user: {
      _id: string;
      mobile: string;
      username: string;
      firstName: string;
      imageId: string;
      lastName: string;
      id: string;
    };
    position: {
      id: string | null;
      title: string;
      slot: any[];
    };
  }[];
  cc: any[];
  subject: string;
  attachments: {
    id: string;
    name: string;
    size: number;
    type: string;
  }[];
  incomingNumber: string;
  incomingDate: string;
  date: string;
  organization: {
    id: string;
  };
  priority: string;
  confidentiality: string;
}

export interface IAttachment {
  id: string;
  name: string;
  size: number;
  type: string;
}
