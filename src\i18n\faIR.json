{"translation": {"sidebar": {"correspondence": {"forward-access-registry": "دسترسی ارجاعات", "organization-reference": "مراجع سازمان", "forwarded-letter": "ارجاعات سازمان", "reports": "گزارش ها"}}, "tooltip": {"back": "بازگشت", "minimize": "کوچک کردن", "help": "راهنما", "close": "بستن", "notifications": "اعلان ها", "solutions": "راهکارها", "genai": "دستیار هوش مصنوعی", "select-forward-letter": "انتخاب از لیست متن دستور آماده", "create-forward-letter-paraph": "اضافه کردن به لیست متن دستور آماده", "delete-from-list": "حذ<PERSON> از لیست", "letter-history-print": "چاپ سوابق", "notification": {"readAll": "خوانده شدن همه اعلان ها", "read": "علامت گذاری به عنوان خوانده شده", "close": "بستن اعلان", "more": "نمایش متن کامل اعلان", "less": "عدم نمایش متن کامل اعلان", "update": "به روزرسانی"}, "secretariat": {"list": {"delete": "<PERSON><PERSON><PERSON> قالب"}}, "position": {"editItem": "ویرایش سمت", "addSubPosition": "اضافه کردن سمت زیرمجموعه"}, "contact": {"addgroup": "ایجاد گروه مخاطب جدید", "moreOptions": "نمایش گزینه های بیشتر جستجو", "hideMoreOptions": "عدم نمایش گزینه های بیشتر جستجو", "editGroup": "ویرایش گروه مخاطب"}, "employee": {"positionTree": "درخت سمت ها", "removePosition": "گرفتن این سمت از کارمند", "changeStatusToInActive": "غیر فعال کردن کارمند", "changeStatusToActive": "فعال کردن کارمند"}, "letter": {"list": {"setting": "مدیریت ستون‌های جدول", "receiptedList": "لیست رسیدهای ثبت شده", "moreOptions": "جستجو روی فیلدهای بیشتر", "hideMoreOptions": "نمایش فیلدهای کمتر"}}, "correspondence": {"samples": {"select_letter_sample": "انتخاب از لیست نمونه‌های متن نامه", "add_letter_sample": "اضافه کردن به عنوان نمونه متن جدید"}}, "ece": {"outgoing": "ارسال شده", "sending-again": "تلاش مجدد برای ارسال", "sending": "درحال ارسال"}}, "genai": {"labels": {"help-genai": "سلام:) من می‌تونم کمک کنم نامه‌ مورد نظرت رو بنویسی.", "official": "رسمی", "friendly": "دوستانه", "content": "موضوع نامه و توضیحات لازم را اینجا بنویسید.", "create-letter": "ایجاد متن نامه", "create-letter-loading": "در حال ایجاد متن نامه"}}, "letter": {"incoming": {"labels": {"incomingNumberIsRepetitive": "تکراری بودن شماره نامه وارده", "incomingNumberIsSubmittedBefore": "این شماره نامه قبلاً ثبت شده است.  آیا می‌خواهید آن را با همین شماره ذخیره کنید؟", "backToForm": "بازگشت به فرم", "confirm": "تایید و ادامه"}}, "list": {"setting": "مدیریت ستون‌های جدول", "receiptedList": "لیست رسیدهای ثبت شده", "moreOptions": "جستجو روی فیلدهای بیشتر", "hideMoreOptions": "نمایش فیلدهای کمتر"}, "secretariat": {"list": {"delete": "<PERSON><PERSON><PERSON> قالب"}}, "eceLetters": {"subject": "موضوع", "sender": "فرستنده", "recipient": "گیرنده", "letter-original-recipient": "گیرنده اصلی نامه", "registered-letter-recipient": "گیرنده ثبتی نامه", "incomingNumber": "شماره نامه", "dateLetter": "تاریخ نامه", "attachments": "ضمیمه ها", "letterSubject": "موضوع نامه", "detailsAttachments": "فایل متن نامه با رنگ سبز مشخص شده است", "loadingEce": "در حال دریافت اطلاعات", "eceFileDate": "اطلاعات فایل ECE :", "isConvertingLetter": "تبدیل به نامه", "dontEceEmail": "این ایمیل نامه ECE ندارد.", "alertEce": "مشکلی در دریافت و یا پردازش اطلاعات فایل ECE به وجود آمد.", "refresh": " تلاش مجدد", "tabIncoming": "دریا<PERSON>تی", "tabOutgoing": "ارسال شده", "tab-receipts-received": "رسیدهای دریافتی", "tab-sent-receipts": "رسیدهای ارسالی", "settingReceiveEce": "تنظیمات دریافت ECE", "settingSendEce": "تنظیمات ارسال ECE", "connectTest": "تست اتصال", "successEceReceive": "اتصال برای دریافت ECE با موفقیت انجام شد.", "successEceSend": "اتصال برای ارسال ECE با موفقیت انجام شد.", "post-receipt-ece-success": "درخواست ارسال رسید ثبت گردید", "post-receipt-ece-failed": "درخواست شما با خطا مواجه شد", "security-SSL/TLS": "امنیت اتصال : SSl/TLS", "code": "ک<PERSON> ارسالی", "number-registry": "شماره ثبت نامه", "date-registry": "تاریخ ثبت نامه", "information-ece-receipt": "اطلاعات رسید ECE", "messages": {"auto-ece-receipt": "ارسال خودکار رسید دریافت ECE (درصورت فعال‌سازی، رسید دریافت پس از شماره‌شدن نامه، به‌صورت خودکار ارسال می‌شود.)"}}}, "behaviorToken": {"labels": {"changeName": "تغییر اسم فیلد", "changeStaticName": "تغییر متن ثابت", "deleteToken": "حذ<PERSON> توکن", "fontSize": "اندازه فونت", "fontWeight": "توکن به صورت بولد نمایش داده شود", "fullName": "نمایش کامل اطلاعات در کنار نام و نام خانوادگی", "positionSignAndSigner": "چیدمان تصویر امضا و نام امضاکننده:", "signerNameDirection": "محل قرارگرفتن نام امضاکننده", "downSign": "پایین تصویر امضا", "upSign": "بالای تصویر امضا", "rightSign": "سمت راست تصویر امضا", "leftSign": "سمت چپ تصویر امضا", "signerNameOffset": "فاصله نام امضاکننده تا تصویر امضا", "ChangeSignerNameOnTop": "نام امضاکننده روی تصویر امضا قرار بگیرد.", "signerName": "نمایش نام امضاکننده", "textDirectionSelect": "<PERSON><PERSON><PERSON> متن", "textDirectionSelectStart": "راست چین", "textDirectionSelectCenter": "وسط چین", "textDirectionSelectEnd": "چپ چین", "twoLineCheckbox": "نمایش اطلاعات به صورت دو خطی", "fontFamilySelect": "انتخاب فونت برای چاپ نامه", "addLine": "افزو<PERSON><PERSON> خط", "line": "خط", "settingFiled": "تنظیمات فیلد", "sizeFile": "حجم عکس پس زمینه باید کمتر از 512 کیلوبایت باشد.", "nameFieldOrder": "ترتیب چاپ اسم فیلد", "firstNameLastField": "ابتدا اسم فیلد، سپس مقدار فیلد", "firstFieldLastName": "ابتدا مقدار فیلد، سپس اسم فیلد", "from-left": "چپ", "from-right": "راست", "choose-print-From": "َشروع چاپ از", "information-order-settings": "ترتیب نمایش اطلاعات", "settingsInformation": " تنظیمات اطلاعات", "information-display-order-setting": "تنظیمات ترتیب نمایش اطلاعات", "information-setting": "تنظیمات اطلاعات", "preview-information-setting": "پیش نمایش و تنظیمات اطلاعات", "preview-information": "پیش نمایش اطلاعات", "location-information": "م<PERSON><PERSON> قرارگیری اطلاعات", "remove-line": "<PERSON><PERSON><PERSON>"}}, "fonts": {"labels": {"vazir": "وزیر", "lotus": "لوتوس", "sahel": "ساحل", "myriad-pro": "Myriad-Pro", "yaghot": "یاقوت", "yaghotBold": "یاقوت بولد", "mitra": "بی‌ میترا", "nazanin": "بی‌ نازنین", "yekan": "بی یکان", "b-traffic": "بی ترافیک", "b-titr": "بی تیتر", "b-zar": "بی زر"}}, "dates": {"labels": {"n-next-days": "{{daysCount}} روز دیگر", "yesterday": "دی<PERSON><PERSON><PERSON>", "today": "امروز", "tomorrow": "فردا", "n-last-days": "{{daysCount}} روز قبل"}}, "awat": {"common": {"tel-number": "021 - 84203861"}, "getStart": {"start": "شروع به کار با آوات", "employee": "شروع به کار به عنوان کارمند سازمان", "settingUpTheOrganization": "راه‌ اندازی سازمان", "createOrganizationInAwat": "ساخت سازمان در آوات", "inviteEmployee": "دعوت کارمندان", "positionChart": "ساختار سازمانی و سمت‌ها", "positionToEmployee": "اختصاص سمت به کارمند", "addEmployeeToCorrespondence": "افزودن کارمندان به مکاتبات", "activeCorrespondence": "فعال کردن مکاتبات"}, "labels": {"startOrganizations": "از سازمان شروع می‌کنیم", "organizationInAwat": "«سازمان» در آوات معادل مجموعه‌‌ایست که در آنجا کار می‌کنید.", "createOrganizationAndAddUsers": "یک نفر باید به نمایندگی از این مجموعه، سازمان را ایجاد کند و بقیه کارمندان را به آن اضافه کند.", "selectOption": "حالا بر اساس توضیحات بالا، یکی از گزینه ها را انتخاب کنید:", "createOrganization": "سازمان را ایجاد می‌کنم", "someoneCreateOrganization": "شخص دیگری سازمان را ایجاد می‌کند", "moreInformation": "نیاز به اطلاعات بیشتری دارم", "structure": "ساختار سازمانی و سمت‌ها", "assignPosition": "اختصاص سمت به کارمندان", "bankAccountInformation": "اطلاعات حساب بانکی آوات", "accountNumber": "شماره حساب", "shabaNumber": "شماره شبا", "name": "به نام", "awatNewSolutions": "راهکارهای نوین چارگون", "to": "نزد", "pasargadBank": "بانک پاسارگاد"}, "message": {"createOrganization": "در صفحۀ «سازمان‌ها» گزینۀ «ایجاد سازمان» را انتخاب کنید.", "idAndCreateOrganization": "در صفحۀ «ایجاد سازمان» نام و شناسه مورد نظرتان را وارد کنید؛ سپس به پایین صفحه بروید و روی گزینه «ایجاد سازمان» بزنید.", "myOrganizationList": "سازمانی که ایجاد کردید را می‌توانید در لیست «سازمان‌های من» در صفحۀ «سازمان‌ها» ببینید.", "viewOrganizationInList": "به صفحۀ «سازمان‌ها» بروید و سازمان مورد نظرتان را از لیست «سازمان‌های من» انتخاب کنید.", "employeeOption": "پس از ورود به سازمان خود، گزینۀ «کارمندان» را از منوی سمت راست انتخاب کنید.", "addEmployee": "در صفحۀ «کارمندان» روی گزینۀ «افزودن کارمند» بزنید.", "addEmployeeWithMobileOrUserName": "نام کاربری یا شماره موبایلی که کارمند موردنظر با آن در آوات ثبت نام کرده را وارد کنید و دکمه “ثبت” را بزنید.", "pendingOrActiveEmployee": "کارمند مورد نظر، با وضعیت «در انتظار» به لیست کارمندان اضافه می‌شود. زمانی که دعوت شما را بپذیرد، وارد سازمان شما خواهد شد و وضعیت او به «فعال» تغییر خواهد کرد.", "structureOptions": "پس از ورود به سازمان خود، گزینۀ «ساختار سازمانی» را از منوی سمت راست انتخاب کنید.", "chartOrganizationDefault": "در تب «چارت سازمانی» دو سمت «مدیر» با ظرفیت 1 و «کارشناس» با ظرفیت بی‌نهایت، به صورت پیش‌فرض ایجاد شده‌اند.", "editPosition": "با کلیک روی آیکون ویرایش، می‌توانید هر سمت را ویرایش کنید.", "addPosition": "مقابل هر سمت، یک گزینۀ + قرار دارد؛ با زدن روی + می‌توانید یک سمت جدید به عنوان زیرمجموعه اضافه کنید.", "choiceEmployee": "در سازمان موردنظر از منوی سمت راست وارد بخش کارمندان شده و کارمند موردنظر را انتخاب کنید.", "addPositionToEmployee": "در صفحه مربوط به آن کارمند، دکمه «اختصاص سمت» را انتخاب کنید.", "treePositions": "در درخت سمت‌ها، جایگاه مورد نظر را برای آن کارمند انتخاب کنید؛ سپس روی گزینۀ «ثبت» بزنید.", "recordChangesForAddPositionToEmployee": "روی گزینۀ «ثبت تغییرات» بزنید تا فرآیند اختصاص سمت به کارمند، تکمیل شود.", "solutions": "در سازمان موردنظر از منوی سمت راست «راهکارها» را انتخاب کنید.", "freeTrial": "برای شروع می‌توانید از «طرح رایگان یک ماهه» استفاده کنید.", "invoice": "اگر نخواستید از طرح رایگان استفاده کنید، می‌توانید گزینۀ «مشاهده تعرفه و خرید» را برای راهکار مکاتبات انتخاب کنید.", "tariff": "در صفحۀ «تعرفه مکاتبات» مقادیر مورد نظرتان را برای تعداد کاربر و فضای در اختیار، وارد کنید و روی گزینۀ «ثبت سفارش» بزنید.", "settingSolutions": "گزینۀ «تنظیمات راهکار» را برای راهکار مکاتبات انتخاب کنید. (اگر این گزینه را نمی‌بینید، به راهنمای فعال کردن مکاتبات مراجعه کنید!)", "settingUsers": "در قسمت «کاربران» نام کارمند مورد نظرتان را وارد کنید و گزینۀ «افزودن» را بزنید.", "youDontOwnerAndCreateOrganization": "شما مالک هیچ سازمانی در آوات نیستید. با «ایجاد سازمان» میتوانید سازمان خود را به آوات اضافه کنید", "copyNumber": "شماره با موفقیت کپی شد"}}, "guides": {"labels": {"register": "ثبت نام", "recoverPassword": "فراموشی رمز عبور", "userInfo": "اطلاعات کاربری", "editUserInfo": "مشاهده و ویرایش اطلاعات کاربری", "notification": "اعلان ها", "organizationConfiguration": "پیکربندی سازمان", "createOrganization": "ایجاد سازمان", "organizationPosition": "ساختار سازمانی", "personnelChart": "چارت پرسنلی", "addEmployees": "افزودن کارمندان", "organizationMembershipAcceptance": "پذیرش عضویت در سازمان", "assignPosition": "اختصاص سمت به کارمند", "AssignPositionManagerOrganization": "اختصاص سمت به مالک سازمان", "releaseSlot": "آزاد کردن جایگاه", "createContacts": "ایج<PERSON> مخاطبان", "groupContacts": "گروه مخاطبان", "selectSuperAdmin": "تعیین راهبران ارشد", "tariffAndTrial": "تعرفه و خرید اشتراک", "trialAccount": "اکانت رایگان یک ماهه", "tariffView": "مشاهده تعرفه و ثبت سفارش", "secretariat": "دب<PERSON><PERSON>خانه", "manageSecretariats": "مدیریت دبیرخانه‌ها", "createIndicator": "ایجاد اندیکاتور", "createLayoutEditor": "ایجاد قالب چاپی نامه", "editLayoutEditor": "ویرایش قالب چاپی نامه", "settingEceLetterGuide": "تنظیمات ECE", "secretariatAdmin": "راهبران دبیرخانه‌ها", "accessibilitySecretariat": "دسترسی‌های مکاتبات", "correspondenceUsers": "افزودن کاربر به مکاتبات", "correspondenceAdmin": "راهبران مکاتبات", "letters": "مکاتبات", "cartable": "کارتابل", "internalLetter": "نامه داخلی", "outgoingLetter": "نامه ارسالی", "incomingLetter": "نامه وارده", "createEceLetterGuide": "ثبت نامه ECE", "forwardLetter": "ارجاع نامه", "readLetterNotice": "اطلاع از زمان باز شدن نامه", "requestToSign": "درخواست امضا", "signLetter": "امضا کردن نامه", "cancelLetter": "لغو نامه", "registrationOutgoingLetter": "ثبت رسید نامه ارسالی", "convertToXML": "تبدیل نامه ارسالی به فایل XML", "sendWithEceFormat": "ارسال با فرمت ECE", "historyLetter": "سوابق نامه", "terminateLetter": "اختتام نامه", "meetingsAccesses": "دسترسی‌های جلسات", "meetingsUsers": "کاربران جلسات", "meetingsAdmins": "راهبران جلسات", "meetings": "جلسات", "meetingsCartable": "کارتابل جلسات", "createMeetingLocation": "ایج<PERSON> محل جلسه", "createMeeting": "ایج<PERSON> جلسه جدید", "minutesMeeting": "ثبت صورتجلسه", "cancelMeeting": "لغو جلسه", "additionalAttachments": "اطلاعات تکمیلی", "addAttachment": "افزودن پیوست", "revertFroward": "بازگشت ارجاع", "deleteContact": "<PERSON><PERSON><PERSON>", "accessGroups": "گروه دسترسی"}}, "calculate": {"messages": {"userDiscountPerDay": "تخفیف تعد<PERSON> کاربر (به ازای هر روز)", "usersDiscountedPricePerDay": "مبلغ کاربران بعد از تخفیف (به ازای هر روز)", "storagePricePerDayBeforeDiscount": "مبلغ فضای ذخیره‌سازی مکاتبات (به ازای هر روز)", "itemsPriceSummationPerDay": "جمع موارد انتخابی (کاربر و فضای ذخیره‌سازی) (به ازای هر روز)", "noDiscountTotalPriceBeforeTax": "جمع کل بدون تخفیف", "itemsPriceSummation": "جمع کل", "discount365days": "تخ<PERSON><PERSON><PERSON> خرید 365 روزه", "totalPriceBeforeTax": "م<PERSON><PERSON><PERSON> کل بعد از تخفیف 365 روزه", "awatSpecialDiscount": "تخفیف ویژه آوات (روی کل مبلغ)", "totalPriceAfterDiscounts": "جمع کل بعد از اعمال تمام تخفیف‌ها", "payableMount": "م<PERSON><PERSON><PERSON> قابل پرداخت", "tax": "مالیات بر ارزش افزوده", "usersPricePerDayBeforeDiscount": "مب<PERSON>غ کاربران (به ازای هر روز)"}}, "breadcrumbs": {"segments": {"": "ورود به سیستم", "login": "ورود به سیستم", "register": "ثبت نام", "recover-password": "بازیابی رمز عبور", "organizations": "سازمان‌ها", "create-organization": "ایجاد سازمان", "solutions": "راهکارها", "profile": "پروفایل", "employees": "کارمندان", "structure": "ساختار سازمانی", "contacts": "مخاطبان", "organization-admins": "راهبران سازمان", "correspondence": "مکاتبات", "internal": "نامه داخلی", "incoming": "نامه وارده", "outgoing": "نامه ارسالی", "new-internal": "ایجاد نامه داخلی", "new-incoming": "ایجاد نامه وارده", "new-outgoing": "ایجاد نامه ارسالی", "receipts": "رسیدها", "active": "نامه‌های جاری", "ended": "نامه‌های بسته شده", "forwardedBy": "نامه‌های ارجاع شده", "receipted": "نامه‌های رسید شده", "ece-letters": "نامه‌های ECE", "secretariats": "دبیرخانه‌ها", "new-indicator": "ایجاد اندیکاتور", "letter-layout": "قالب چاپی", "meetings": "جلسات", "correspondence-search-results": "نتایج جستجو", "correspondence-settings": "تنظیمات مکاتبات", "correspondence-users": "کاربران", "correspondence-admins": "راهبران", "secretariat-admins": "راهبران دبیرخانه", "meetings-settings": "تنظیمات جلسات", "meetings-users": "کاربران", "meetings-admins": "راهبران", "all-meetings-organization": "همه جلسات سازمان", "meetings-management": "مدیریت راهکار جلسات", "correspondence-tariff": "تعرفه مکاتبات", "meetings-tariff": "تعرفه جلسات", "correspondence-upgrade-tariff": "ارتقای مکاتبات", "meetings-upgrade-tariff": "ارتقای جلسات", "orderId": "پیش فاکتور", "invoiceId": "پیش فاکتور", "payment-result": "نتیجه پرداخت", "payment": " پرداخت", "numberedLetters": "اندیس‌های indicatorId", "administration": "پنل مدیریت", "orders": "سفارش‌ها", "assignment-of-discounts-management": "مدیریت کدهای تخفیف", "new-contact": "ایج<PERSON> مخاطب", "employeeId": "اطلاعات کارمند", "contactId": "اطلاعات مخاطب", "organization-settings": "تنظیمات دسترسی سازمان", "organization-groups": "دسترسی‌ها", "organization-documents-list": "فایل‌های سازمان", "groupId": "اطلاعات گروه", "forbidden": "عدم دسترسی", "correspondence-management": "مدیریت مکاتبات", "all-forwarded-letter": "همه ارجاعات سازمان", "tasks": "کارها", "forward-access": "دسترسی ارجاعات", "tasksList": "لیست کارها", "reports": "گزارش ها", "references-reports": "گزارش مراجع", "projects": "پروژه‌ها"}}, "permissions": {"set_letter_number": "شماره کردن نامه", "add_receipt": "<PERSON><PERSON><PERSON> رسید", "edit_receipt": "ویرایش رسید", "export_ece": "تبدیل نامه ارسالی به ECE", "parse_ece": "تبدیل ECE به نامه ارسالی", "get_ece_mail": "مشاهده ایمیل", "get_secretariat_ece_mails": "مشاهده لیست ایمیل ها", "convert_ece_to_letter": "تبدیل ایمیل به نامه ECE", "print_letter": "پرینت نامه", "get_numbered_letters_by_indicator": "مشاهده نامه‌های شماره شده ی یک اندیکاتور", "export_numbered_letters_by_indicator": "دانلود نامه ی شماره شده ی یک اندیکاتور", "add_correspondence_admin": "اضافه کردن راهبر مکاتبات", "remove_correspondence_admin": "حذف راهبر مکاتبات", "grant_solution": "افزودن کاربر مکاتبات", "revoke_solution_access": "حذف کاربر مکاتبات", "get_granted_users_ids": "مشاهده کاربران دارای مجوز مکاتبات", "create_secretariat": "ساخت دبیرخانه", "update_secretariat": "ویرایش دبیرخانه", "get_indicator": "مشاهده اندیکاتور", "create_indicator": "ساخت اندیکاتور", "update_indicator": "ویرایش اندیکاتور", "update_indicator_status": "ویرایش وضعیت اندیکاتور", "create_letter_layout": "ساخت قالب چاپی", "update_letter_layout": "ویرایش قالب چاپی", "add_super_admin": "اضافه کردن راهبر ارشد", "remove_super_admin": "حذف را<PERSON><PERSON><PERSON> ارشد", "add_management_center_admin": "اضافه کردن راهبر سازمان", "remove_management_center_admin": "حذف کردن راهبر سازمان", "get_organization": "گرفتن اطلاعات سازمان", "update_organization_profile": "ویرایش پروفایل سازمان", "add_employee_by_username": "افزودن کارمند با نام کاربری", "add_employee_by_mobile": "افزودن کارمند با شماره موبایل", "invite_employee_by_username": "دعوت کارمند با نام کاربری", "invite_employee_by_mobile": "دعوت کارمند با شماره موبایل", "update_employee_status": "ویرایش وضعیت کارمند", "update_employee_code": "ویرایش شناسه کارمند", "update_employee_profile": "ویرایش پروفایل کارمند", "create_position": "ساخت سمت", "assign_position": "افرودن سمت", "update_position": "آپدیت سمت", "update_position_code": "آپدیت شناسه سمت", "update_position_status": "آپدیت وضعیت سمت", "update_position_parent": "آپدیت جایگاه سمت", "revoke_position": "گرفتن سمت از کارمند", "set_primary_position": "تغییر سمت اصلی کارمند", "verify_release_slot": "آزاد کردن جایگاه", "delete_letter_layout": "حذ<PERSON> قالب چاپی", "set_ece_mail_config": "تنظیم ایمیل برای ECE", "get_ece_mail_config": "مشاهده تنظیمات ایمیل برای ECE", "update_contact_info": "آپدیت اطلاعات مخاطبان", "update_legal_info": "آپدیت اطلاعات حقوقی", "update_indicator_disable": "غیر فعال کردن اندیکاتور", "get_solution_usages": "مشاهده اعتبار(کاربر، روز، فضا) استفاده شده در مکاتبات", "add_secretariat_admin": "افزودن راهبر دبیرخانه", "remove_secretariat_admin": "حذف راهبر دبیرخانه", "get_letters_forwards": "گرفتن ارجاعات یک نامه", "get_letter": "دریافت نامه", "force_fetch_ecemails": "به روزرسانی دستی نامه‌های ECE", "update_permanent_letter": "به روزرسانی نامه ی شماره شده", "get_last_serial_number": "گرفتن آخرین شماره سریال اندیکاتور", "create_meeting_location": "ساخت محل جلسه", "get_meeting_locations": "مشاهده لیست محل‌های جلسه", "enable_meeting_location": "فعال کردن محل جلسه", "disable_meeting_location": "غیر فعال کردن محل جلسه", "update_meeting_location": "ویرایش محل جلسه", "cancel_meeting": "لغو جلسه", "get_organization_meetings": "مشاهده لیست جلسات سازمان", "update_meeting": "ویرایش جلسه", "get_meeting": "نمایش جلسه", "create_meeting_minutes": "ایجاد صورت جلسه", "update_meeting_minutes": "ویرایش صورت جلسه", "add_employee_participants": "افزودن کارمند به مدعوین جلسه", "remove_employee_participants": "حذف کارمند از مدعوین جلسه", "add_contact_participants": "افزودن مخاطب به مدعوین جلسه", "remove_contact_participants": "حذ<PERSON> مخا<PERSON><PERSON> از مدعوین جلسه", "finish_meeting": "اخت<PERSON>ا<PERSON> جلسه", "group-members": "افزودن کارمند به گروه ", "access-group-members": "دسترسی‌های گروه ", "create_order": "ایج<PERSON> سفارش", "get_organization_access_groups": "مشاهده لیست گروه‌های دسترسی سازمان", "get_access_group_members": "مشاهده اعضای گروه دسترسی", "add_access_group_members": "افزودن عضو به گروه دسترسی", "remove_access_group_members": "حذف عضو از گروه دسترسی", "delete_contact": "<PERSON><PERSON><PERSON>", "update_exchange_registry": "تعیین دسترسی ارجاعات", "void_letter": "ابطال نامه", "create_sample": "ایجاد و حذف نمونه متن نامه", "get_reference_usage_report": "مشاهده گزارش مراجع سازمان", "meetings": {"revoke_solution_access": "حذ<PERSON> کار<PERSON>ر جلسات", "grant_solution": "افزودن کاربر جلسات", "get_solution_usages": "مشاهده اعتبار(کاربر، روز، فضا) استفاده شده در جلسات", "create_meeting_location": "ساخت محل جلسه", "get_meeting_locations": "مشاهده لیست محل‌های جلسه", "enable_meeting_location": "فعال کردن محل جلسه", "disable_meeting_location": "غیر فعال کردن محل جلسه", "update_meeting_location": "ویرایش محل جلسه", "cancel_meeting": "لغو جلسه", "get_organization_meetings": "مشاهده لیست جلسات سازمان", "update_meeting": "ویرایش جلسه", "get_meeting": "نمایش جلسه", "create_meeting_minutes": "ایجاد صورت جلسه", "update_meeting_minutes": "ویرایش صورت جلسه", "add_employee_participants": "افزودن کارمند به مدعوین جلسه", "remove_employee_participants": "حذف کارمند از مدعوین جلسه", "add_contact_participants": "افزودن مخاطب به مدعوین جلسه", "remove_contact_participants": "حذ<PERSON> مخا<PERSON><PERSON> از مدعوین جلسه", "finish_meeting": "اخت<PERSON>ا<PERSON> جلسه"}}, "table": {"correspondence": {"subject": "موضوع", "sender": "فرستنده", "recipient": "گیرندگان", "type": "نوع نامه", "number": "شماره نامه", "numberingDate": "زم<PERSON> ثبت", "signerStatus": "وضعیت امضاها", "positions": "سمت", "forwardSender": "فرستنده ارجاع", "forwardedToMeDate": "زمان ارجاع به من", "createdAt": "زمان ایجاد", "terminatedDate": "زمان اختتام", "discardedDate": "زمان لغو", "forwardRecipient": "گیرنده ارجاع", "forwardDate": "زمان ارجاع", "sentVia": "نحوه ارسال", "receiptedBy": "نام تحویل گیرنده", "receiptedAtDate": "تاریخ و ساعت تحویل", "receiptRegistrar": "ثبت کننده رسید", "trackingCode": "کد پیگیری", "revertForwardLetter": "بازگرداندن ارجاع", "referenceCount": "تعداد مرجع", "letterDate": "تاریخ نامه"}}, "document-type": {"signature": "امضا"}, "common": {"labels": {"apply-filter": "اعمال فیلتر", "capacity": "ظر<PERSON><PERSON>ت", "awat": "آوات", "GB": "گیگابایت", "MB": "مگابایت", "KB": "کیلوبایت", "User": "کاربر", "person": "نفر", "monthly": "ماهیانه", "yearly": "سالانه", "noneReset": "بدون ریست", "preview": "پیش نمایش", "upload-file": "بارگذاری فایل", "user-information": "اطلاعات کاربری", "filter": "فیلتر", "firstName": "نام", "username": "نام کاربری  ", "mobile": "شماره موبایل", "usernameOrMobile": "نام کاربری / شماره موبایل", "password": "<PERSON><PERSON><PERSON> عبور", "enter": "ورود", "rules": "قوانین", "acceptanceRules": " را مطالعه کرده ام و می پذیرم.", "register": "ثبت نام", "continue": "ادامه", "mr": "آقای", "mr_": "جناب آقای", "ms": "خانم", "ms_": "سرکار خانم", "dr": "د<PERSON><PERSON>ر", "eng": "مهندس", "mr_dr": "آقای دکتر", "mr_dr_": "جناب آقای دکتر", "ms_dr": "<PERSON><PERSON><PERSON>تر", "ms_dr_": "سرکار خانم دکتر", "mr_eng": "آقای مهندس", "mr_eng_": "جناب آقای مهندس", "ms_eng": "<PERSON>ان<PERSON> مهندس", "ms_eng_": "سرکار خانم مهندس", "submit": "ثبت", "submitChanges": "ثبت تغییرات", "active": "فعال", "inactive": "غیر فعال", "save": "ذخیره", "hasSave": "ذخ<PERSON>ره شده", "location-name": "نام محل", "address": "آدرس", "description": "توضیحات", "link": "لینک (موقعیت)", "understand": "متوجه شدم", "next": "بعدی", "goToPage": "برو به صفحه", "pageLimit": "تعداد نمایش در هر صفحه", "confirm": "تا<PERSON><PERSON>د", "submit-and-edit": "تایید و ویرایش", "correspondence": "مکاتبات", "meetings": "جلسات", "correspondence-cartable": "کارتابل نامه‌ها", "meetings-cartable": "کارتابل جلسات", "upload": "آپلود", "choose-new-file": "انتخاب فایل جدید", "remove": "<PERSON><PERSON><PERSON>", "cancel": "انصراف", "edit": "ویرایش", "date": "تاریخ", "hour": "ساعت", "expiration-date": "تاریخ انقضا", "approved": "تا<PERSON><PERSON>د شده", "declined": "ر<PERSON> ش<PERSON>ه", "pending": "تعیین وضعیت", "confirm-finalize": "تایید و نهایی کردن", "user-count": "تعداد کاربر", "awatRole": "قوانین آوات", "fax": "فکس", "email": "ایمیل", "physical": "حضو<PERSON>ی", "social": "شبکه‌های مجازی", "typeSend": " نحوه ارسال:", "more": "بیشتر", "hasNumber": "شماره شده", "number": "شماره", "sizeFile30": "حجم فایل نباید بیشتر از 30 مگابایت باشد.", "submitSuccess": "با موفقیت برای این نامه ثبت شد", "saveAndNumber": "ذخیره و شماره کردن", "saveAndSign": "ذخیره تغییرات و امضا", "allShow": "نمایش همه", "receiveTime": "ساعت تحویل", "textLetter": "متن نامه", "searchIn": "جستجو در", "searchInLetters": "جستجو در نامه‌ها", "searchLetter": "جستجوی نامه", "search": "جستجو", "clearSearch": "پاک کردن جستجو", "lists": "همه لیست‌ها", "secretariats": "دبیرخانه‌ها", "all-secretariats": "همه دبیرخانه‌ها", "all-indicator": "همه اندیکاتورها", "currentList": "همین لیست", "protocol": "پروتکل", "server": "سرور", "port": "پورت", "acceptDelete": "بله حذف شود", "employeePositions": "سمت (های) کارمند", "empty-positions": "بدون سمت", "awaiting": "در انتظار پذیرش", "newGroup": "گروه جدید", "searchContent": "جستجو در مخاطبان", "selectGroup": "انتخاب گروه مخاطب", "allGroup": "همه گروه‌ها", "accept": "می‌<PERSON><PERSON><PERSON><PERSON>م", "organization": "سازمان", "notification": "اعلان ها", "enteringOrganization": "ورود به سازمان", "reception": "پذیرش", "justViewActivePositions": "فقط نمایش سمت‌های فعال", "upperPosition": "سمت بالادستی", "searchEmployees": "جستجوی کارمندان", "chooseFrom": "انتخاب از", "contactsList": "لیست مخاطبان", "viewAll": "مشاهده همه", "ViewLess": "مشا<PERSON><PERSON>ه کمتر", "minute": "دق<PERSON><PERSON>ه", "costDetails": "جزئیات هزینه‌ها", "attachments": "پیوست‌ها", "no_data": "داده ای برای نمایش وجود ندارد.", "download": "د<PERSON><PERSON><PERSON>د", "start-date": "تاریخ", "start-time": "ساعت", "title": "عنوان", "letters": "نامه‌ها", "scheduling": "زمان‌<PERSON><PERSON><PERSON>ی", "noAccess": "عدم دسترسی به نامه", "noResultsFound": "نتیجه‌ای یافت نشد", "persian": "فار<PERSON>ی", "english": "انگلیسی", "select": "انتخاب"}, "messages": {"dateReceiveLetter": "تاریخ دریافت نامه وارده", "addDateReceiveLetter": "تاریخ دریافت نامه وارده را وارد نمایید", "requestSent": "درخواست با موفقیت ارسال شد", "successOperation": "عملیات با موفقیت انجام شد", "pinInvalid": "کد وارد شده صحیح نمی باشد", "imageDescriptionFormat": "فقط فرمت های png و jpg مجاز می باشد.", "noOptions": "مقداری یافت نشد", "afterNumberLetter": "پس از ثبت شماره نامه، اطلاعات نامه ذخیره شده و دیگر قابل ویرایش نخواهد بود", "editLetterAndNumbering": "شما تغییراتی در نامه ایجاد کرده اید و با امضای نامه تغییرات شما ذخیره خواهد شد.", "MandatoryField": "فیلدهای اجباری را تکمیل نمایید", "notMessagesSearch": "نامه ای مطابق با جستجوی شما پیدا نشد", "removeEmailAwat": "ایمیل‌های دریافت شده در آوات، از صندوق ورودی ایمیل شما حذف خواهند شد.", "emptyText": "متن خالی می باشد ...", "selectText": "متن مورد نظر را وارد کنید", "startGuide": "«راهنمای شروع به کار» را دوباره به من نشان نده.", "deprivationPosition": "برای غیرفعال کردن کارمند ابتدا سمت های کارمند را بگیرید.", "deactivatePositions": "گرفتن همه سمت‌ها و غیرفعال کردن", "addNewEmployee": "اضافه کردن کارمند جدید", "successfulMessage": "عملیات با موفقیت انجام شد", "complete-all-required-fields": "همه فیلدهای اجباری را تکمیل کنید.", "startGuidTasks": {"couldThrough": "شما می‌توانید از طریق", "createFirstTask": "اولین کار خود را ایجاد کنید.", "getListOfTasks": "لیست تمام کار‌های ایجادشده را مشاهده کنید.", "guideForCalendar": "شما می‌توانید در این تقویم، تمام کار‌های روزانه‌تان را به‌راحتی مشاهده و مدیریت کنید.", "guideAI": "در اینجا هوش مصنوعی آوات در کنار شماست تا فرآیند نوشتن نامه‌ها را سریع‌‌تر و دقیق‌تر کند.", "creatingTaskEasilyAndQuickly": "ایجاد سریع و راحت کار", "createTaskEasily": "کا<PERSON> خود را به راحتی ایجاد کنید", "viewTasksInCalendar": "مشاهده کار‌ها در تقویم روزانه", "seeTasksInCalendar": "کار‌ها را در تقویم روزانه خود مشاهده کنید", "tasksManagement": "مدیریت کار‌های تعریف شده", "afterCompleteTask": "پس از تکمیل هر کار، آن را با یک تیک به پایان برسانید", "managementAll": "از امروز، همه کارها رو در آوات مدیریت کن", "tasksManagementIsFree": "مدیریت کارها برای شما به‌صورت رایگان فعال شد!", "letsGo": "بزن بریم"}}, "errors": {"requestFailed": "درخواست با موفقیت ارسال نگردید", "uploadFailed": "بارگذاری فایل‌ها با مشکل مواجه شد.", "oparationError": "عملیات با خطا مواجه شد", "checkstatus-overed": "زمان اجرای درخواست command-name از حد معمول بیشتر شده است و نتیجه آن از طریق یک اعلان به شما اطلاع داده خواهد شد. پیش از تکرار دوباره،‌ منتظر دریافت نتیجه بمانید.", "receiveData": "دریافت اطلاعات با خطا مواجه شد", "businessNameNotRegistered": "نام تجاری برای گیرنده نامه ثبت نشده است", "500ErrorMessage": "سرور پاسخگو نمی باشد", "429ErrorMessage": "تعداد درخواست ها بیشتر از حد مجاز است", "403ErrorMessage": "شما دسترسی لازم را ندارید", "404ErrorMessage": "404 Error"}, "validation": {"required": "تکمیل این فیلد الزامی است", "min8": "حدا<PERSON><PERSON> 8 کاراکتر", "lowerAndUpper": "شامل حروف بزرگ و کوچک", "specialChar": "شامل کاراکتر های خاص", "includeNumber": "شامل اعداد", "useNumberAndChar": "از حروف انگلیسی و اعداد استفاده کنید"}, "strings": {}, "daysOfWeek": {"saturday": "شنبه", "sunday": "یکشنبه", "monday": "دوشنبه", "tuesday": "سه شنبه", "wednesday": "چهارشنبه", "thursday": "پنج شنبه", "friday": "جمعه"}}, "administration": {"labels": {"administration-panel": "پنل مدیریت", "administration-dashboard": "دا<PERSON><PERSON>و<PERSON>د مدیریتی", "orders": "سفارش‌ها", "discount-codes-management": "مدیریت کدهای تخفیف", "allocation-of-discount-code": "تخصیص کد تخفیف", "assign-discount-code": "تخصیص کد تخفیف", "payment-status": "وضعیت پرداخت", "determine-payment-status": "تعیین وضعیت پرداخت", "approve-payment": "تا<PERSON><PERSON>د پرداخت", "decline-payment": "رد پرداخت", "approved": "تا<PERSON><PERSON>د شده", "pending": "تعیین وضعیت نشده", "declined": "ر<PERSON> ش<PERSON>ه"}}, "admins": {"labels": {"raminedCapacity": "ظر<PERSON><PERSON>ت باقی مانده", "viewAccesses": "مشاهده دسترسی‌ها", "add": "افزودن", "remove": "<PERSON><PERSON><PERSON>", "edit": "ویرایش", "countOfEmployee": "کارمند در این گروه", "userCount": "تعداد کاربر", "addedEmployeeCorrespondenceAdmins": "افزودن کارمند به راهبران مکاتبات", "addedUserEmployeeCorrespondence": "افزودن کاربر به مکاتبات", "addedUserEmployeeMeeting": "افزودن کاربر به جلسات", "accessesCorrespondenceAdmins": "دسترسی‌های راهبر مکاتبات", "userName": "نام کارمند موردنظر را وارد کنید", "users": "کاربران", "correspondenceAdmins": "راهبران مکاتبات", "meetingAdmins": "راهبران جلسات", "addedEmployeeMeetingAdmins": "افزودن کارمند به راهبران جلسات", "addedEmployeeMeetingSuperAdmins": "افزودن کارمند به راهبران ارشد جلسات", "accessesMeetingAdmins": "دسترسی‌های راهبر جلسات"}, "massages": {"user-access-success": "دسترسی به کاربر با موفقیت انجام شد", "user-revoke-access": "دسترسی از کاربر با موفقیت سلب گردید"}}, "auth": {"labels": {"confirmation": "تا<PERSON><PERSON>د", "editInfo": "ویرایش اطلاعات", "register": "ثبت نام کنید", "goToLogin": "رفتن به صفحه ورود", "changeNumber": "تغییر شماره", "recoverPassword": "بازیابی رمز عبور", "changePassword": "تغییر رمز عبور", "newPassword": "<PERSON><PERSON><PERSON> عبور جدید", "goToLoginAfterRecoverPassword": "بازگشت به صفحه ورود", "loginTitle": "شروع به کار", "registerTitle": "با یک حساب رایگان شروع کنید"}, "messages": {"enterVerificationCode": "کد تایید را وارد نمایید", "sentVerificationCode": "کد تایید برای شماره موبایل {{value}} ارسال گردید", "resendCode3min": " ارسال مجدد کد تا {{value}} ثانیه دیگر", "resendCode": "ارسال مجدد کد", "forgetPassword": "رمز عبور خود را فراموش کردید؟", "requestEndTime": "مدت اعتبار درخواست شما تمام شده است", "existUser": "شماره موبایل وارد شده در سیستم موجود است", "enterIfRegistered": "اگر قبلا ثبت نام کرده اید وارد شوید", "successRegister": "ثبت نام شما با مشخصات زیر با موفقیت انجام شد", "userNotFound": "نام کاربری یا رمز عبور صحیح نمی باشد", "mobileInvalid": "شماره موبایل معتبر نیست", "usernameInvalid": "نام کاربری موجود نیست", "passwordInvalid": "رمز عبور را وارد کنید", "existUsername": "کاربر با این شناسه در سیستم موجود است", "successRecoverPassword": "رمز عبور جدید با موفقیت تغییر کرد"}, "errors": {}, "validation": {}, "strings": {"doNotHaveAccount": "حساب کاربری ندارید؟", "ifWantEnter": "حساب کاربری دارید؟", "enterIt": "وارد شوید", "canUseInfo": "شما می توانید اطلاعات بالا را برای اضافه شدن به سازمان مورد نظر به مالک سازمان خود ارائه دهید"}}, "organization-dashboard": {"labels": {"organization-dashboard": "میزکار", "header-correspondence": "نامه‌های ارجاع به من", "header-meetings": "جلسات امروز من", "read-letters-count": "خوانده شده", "unread-letters-count": "خوانده نشده", "no-meetings": "جلسه‌ای برای امروز ندارید.", "no-letters": "نامه‌‌ای به شما ارجاع داده نشده.", "organization-owner": "مالک سازمان", "guide": "آموزش", "changes-log": "تاریخچه", "support": "پشتیبانی", "profile": "پروفایل", "no-correspondence-access": "شما به راهکار مکاتبات دسترسی ندارید.", "no-meetings-access": "شما به راهکار جلسات دسترسی ندارید.", "no-correspondence-credit": "راهکار مکاتبات در سازمان شما فعال نیست.", "no-meetings-credit": "راهکار جلسات در سازمان شما فعال نیست.", "correspondence-access": "دسترسی مکاتبات", "meetings-access": "دسترسی جلسات"}}, "profile": {"labels": {"titleEditForm": "پروفایل کاربر", "titleChangePassword": "تغییر رمز عبور", "titleManageSignature": "مدیریت امضا", "username": "نام کاربری", "mobile": "شماره موبایل", "firstName": "نام", "firstName_en": "First Name", "lastName": "نام خانوادگی", "lastName_en": "Last Name", "province": "استان", "provinces": "استان ها", "city": "شهر", "cities": "شهرها", "address": "آدرس", "postalAddress": "نشانی پستی", "phone": "شماره تماس", "currentPassword": "ر<PERSON>ز عبور فعلی", "newPassword": "<PERSON><PERSON><PERSON> عبور جدید", "saveChangePassword": "تغییر رمز عبور", "saveManageSignature": "ثبت تغییرات", "save": "ثبت تغییرات", "cancel": "انصراف", "have-not-define-a-signature-yet": "شما در حال حاضر امضا تعریف نکرده اید", "signature-must-be-png": "فرمت فایل امضا باید png باشد", "image-size-less-than-1-mb": "حجم تصویر باید کمتر از 1 مگابایت باشد", "image-has-any-background": "فایل امضا باید بدون پس‌زمینه باشد", "edit-signature": "ویرایش امضا", "choiceNewPicture": "انتخاب تصویر جدید", "pictureProfile": "تصویر پروفایل", "personalInfo": "اطلاعات هویتی", "lastLoginData": "زمان آخرین ورود"}, "messages": {}, "errors": {}, "validation": {}, "strings": {}}, "organization": {"labels": {"organization-legal-info": "اطلاعات حقوقی سازمان", "createOrganizationBtn": "ایجاد سازمان", "editOrganizationBtn": "ثبت تغییرات", "organizationsTitle": "سازمان ها", "userOrganizations": "سازمان های من", "hasNotOrganizationTitle": "به آوات خوش آمدید 🎉", "hasNotOrganizationP1": "یک سازمان ایجاد کنید و راه‌کارهای مورد نظرتان را در آن فعال کنید.", "hasNotOrganizationP2": "یا به مدیر خود اطلاع دهید تا شما را عضو سازمانش کند.", "ownedOrganizations": "سازمان های عضو", "organizationId": "شناسه سازمان", "displayName": "نام سازمان", "displayName_en": "Company Name", "owner": "مالک سازمان", "lastEmployees": "آخرین کارمندان اضافه شده", "organizationStatus": "وضعیت سازمان", "recentEvents": "رویدادهای اخیر", "activeSolutions": "راهکارهای فعال", "manage-active-solutions": "مدیریت راهکارها", "addFirstSolution": "با اضافه کردن اولین محصول فعالیت سازمان خود را شروع کنید", "AwatSolutions": "راهکارهای آوات", "activeSolutionsCount": "تعداد محصولات فعال", "employeesCount": "تعداد کارمندان", "contactsCount": "تعداد مخاطبان", "noActiveSolution": "شما راهکار فعالی در این سازمان ندارید.", "noOtherAvailableSolutions": "شما راهکار فعال دیگری در این سازمان ندارید.", "noCorrespondenceCredit": "برای ورود به مکاتبات کاربر خود را به مکاتبات اضافه کنید.", "noMeetingsCredit": "برای ورود به جلسات کاربر خود را به جلسات اضافه کنید", "backToOrganization": "بازگشت به سازمان", "edit": "ویرایش", "cancel": "انصراف", "addOrganizationAdmin": "افزودن راهبر سازمان", "accessesOrganizationSuperAdmins": "دسترسی‌های راهبر ارشد سازمان", "addEmployeeOrganizationSuperAdmin": "افزودن کارمند به راهبران ارشد سازمان", "organizationSuperAdmins": "راهبران ارشد", "organizationAdmins": "راهبران", "organizationAccess": "گروه‌های دسترسی", "welcomeToAwat": "به آوات خوش آمدید", "helpWorkAwat": "من به شما کمک می کنم تا کار خود را در آوات شروع کنید.", "letsGo": "بزن بریم", "newLogo": "انتخاب لوگو جدید", "logoOrganization": "لوگو سازمان", "fileSize": "فقط فرمت های png و jpg مجاز می باشد.', 'حجم تصویر باید کمتر از 1 مگابایت باشد.", "viewPersonnelChart": "مشاهده چارت پرسنلی", "addNewPosition": "اضافه کردن سمت جدید", "organizationalChart": "چارت سازمانی", "personnelChart": "چارت پرسنلی", "employeesOrganization": "کارمند سازمان", "organizationAudience": "مخاطب سازمان", "genderTypes": "جنسیت", "infoOrganization": "اطلاعات سازمان", "access": "دسترسی‌ها", "organization-stamp": "مهر سازمان", "stamp-preview": "پیش‌نمایش مهر", "change-stamp": "تغییر مهر سازمان", "upload-stamp": "آپلود مهر سازمان", "remove-stamp": "حذف مهر سازمان", "documentsList": "فایل‌های سازمان", "projectsList": "پروژه ها", "usedOperation": "راهکار مورد استفاده", "correspondence": "مکاتبات", "meetings": "جلسات", "all": "همه"}, "documents-list": {"fileName": "نام فایل", "uploadDate": "تاریخ آپلود", "uploaderName": "نام آپلود کننده", "fileSize": "اندازه فایل", "operation": "راهکار"}, "dialog": {"edit": "با ویرایش اطلاعات حقوقی سازمان، در صورت وجود پیش‌فاکتور پرداخت نشده، همه پیش‌فاکتورهای شما لغو خواهد شد."}, "messages": {"organizationCreated": "سازمان با موفقیت ایجاد شد", "employeeAddedByOwner": "شخصی که سازمان را می‌سازد، باید شما را به عنوان کارمند به سازمان دعوت کند.", "showInviteInorganizations": "این دعوتنامه را در صفحه سازمان‌ها می‌توانید ببینید.", "activeUpperPosition": "ابتدا سمت بالا دستی را فعال کنید.", "deactiveUpperPosition": " سمت بالا دستی این جایگاه غیرفعال است.", "expireSolution": "زمان اعتبار این راهکار به پایان رسیده است.", "viewTariff": " لطفا سرویس خود را با استفاده از گزینه «مشاهده تعرفه و خرید» تمدید کنید.", "orderConfirmationCorrespondence": "سفارش مکاتبات شما ثبت و پرداخت شده. لطفا منتظر تایید پرداخت از سمت آوات بمانید.", "no-image-data": "تصویری برای نمایش وجود ندارد", "not-stamp": "در حال حاضر مهر سازمان تعریف نکرده اید", "stamp-format": "فایل مهر باید فرمت PNG، با حجم کمتر از ۱ مگابایت و بدون پس‌زمینه باشد.", "remove-stamp-confirm": "شما در حال حذف مهر سازمان هستید، آیا مطمئن هستید؟"}, "errors": {"organizationNameExisted": "سازمان با این شناسه در سیستم موجود است", "createOrganizationFailed": "سازمان با موفقیت ایجاد نگردید"}, "validation": {"displayName": {"string-min": "نام سازمان باید حداقل 4 حرف باشد"}}, "strings": {}}, "employee": {"labels": {"status": "وضعیت", "firstName": "نام", "firstName_en": "Name", "lastName": "نام خانوادگی", "lastName_en": "Last Name", "username": "نام کاربری", "mobile": "شماره موبایل", "jobTitle": "عنوان شغلی", "title": "لقب", "description": "توضیحات", "addEmployee": "افزودن کارمند", "listOfEmployees": "لیست کارمندان", "rowStatusActive": "فعال", "rowStatusInActive": "غیر فعال", "existNotEmployees": " در حال حاضر کارمندی به این سازمان اضافه نشده است", "modalAddEmployee": "اضافه کردن کارمند", "addEmployeeByUsername": "با نام کاربری", "addEmployeeByMobile": "با شماره موبایل", "registerAddEmployeeAndNew": "ثبت و جدید", "registerAddEmployee": "ثبت", "submitEditEmployee": "ثبت تغییرات", "cancel": "انصراف", "mobileTitle": "پروفایل کارمند", "employeeCode": "ک<PERSON> کارمند", "position": "سمت", "positionName": "نام سمت", "positionName_en": "Job Title", "choosePosition": "انتخاب سمت", "positionFilter": "فیلتر بر اساس سمت:", "searchEmployee": "نام کارمند موردنظر را وارد کنید", "notFoundUser": "کارمندی یافت نشد", "addedUserNameAndUserFamily": "نام، نام خانوادگی یا نام کاربری کارمند موردنظر را وارد کنید "}, "messages": {"employeeAdded": "کارمند با موفقیت به سازمان اضافه شد"}, "validation": {}, "strings": {"insertEmployeeUsername": "نام کاربری کارمند مورد نظر را وارد کنید", "insertEmployeeMobile": "شماره موبایل کارمند مورد نظر را وارد کنید", "existNotUsernameAccount": "حسابی با این نام کاربری موجود نمی باشد", "existNotMobileAccount": "حسابی با این شماره موبایل موجود نمی باشد", "alreadyExistEmployee": "این کارمند از قبل اضافه شده یا عملیات افزودن کارمند با خطا مواجه شد"}}, "position": {"labels": {"addPosition": "اختصاص سمت", "treePositionsTitle": "انتخاب سمت‌ها", "notPositionEmployee": "کارمند دارای سمتی پیدا نشد"}}, "forbidden": {"labels": {"noAccess": "شما به این صفحه دسترسی ندارید", "help": "در صورت اطمینان از دسترسی، با مدیر مربوطه در سازمان خود هماهنگ کنید.", "organizations": "بازگشت به سازمان ها"}}, "contacts": {"helper-text": {"organization-business": " نام سازمان مخاطب شما. مانند: موسسه آموزشی مسیر ایده‌آل ", "position-jobTitle": "عنوان شغلی مخاطب شما. مانند: مدیر آموزش"}, "columns": {"firstName_en": "نام(en)", "lastName_en": "نام خانوادگی(en)", "position-jobTitle_en": "سمت/عنوان شغلی(en)", "organization-business_en": "نام سازمان/نام تجاری(en)"}, "labels": {"status": "وضعیت", "createContact": "ایج<PERSON> مخاطب", "createContactGroup": "ایجاد گروه مخاطب", "editContactGroup": "ویرایش گروه مخاطب", "editContact": "ویرایش مخاطب", "mobileTitle": "مدیریت مخاطبان", "groupName": "نام گروه", "groupDescription": "توضیحات", "saveContactsGroup": "ذخیره", "firstName": "نام", "lastName": "نام خانوادگی", "username": "نام کاربری", "mobile": "شماره موبایل", "email": "ایمیل", "contactGroup": "گروه مخاطب", "title": "لقب", "phone&fax": "تلفن و فکس", "phone": "شماره تلفن", "fax": "شماره فکس", "legalInformation": "اطلاعات حقوقی", "position-jobTitle": "سمت/عنوان شغلی", "position-jobTitle-guide": "عنوان شغلی مخاطب شما. مانند: مدیر آموزش", "organization-business": "نام سازمان/نام تجاری", "organization-business-guide": "نام سازمان مخاطب شما. مانند: موسسه آموزشی مسیر ایده‌آل", "nationalId": "<PERSON><PERSON> مل<PERSON>", "addressTitle": "آدرس", "address": "نشانی پستی", "descriptionTitle": "توضیحات", "description": "توضیحات", "saveCreateContact": "ایجاد و جدید", "saveCreateContactMobile": "ایج<PERSON> مخاطب", "cancelCreateContact": "انصراف", "chooseImageBtn": "انتخاب تصویر مخاطب", "imageTextMobile": "تصویر مخاطب", "mainInfo": "اطلاعات اصلی", "province": "استان", "city": "شهر", "enable": "فعال", "disable": "غیر فعال", "existNotContacts": "مخاطبی ثبت نشده است", "save": "ذخیره", "primaryInfo": "اطلاعات اصلی", "AdditionalInfo": "اطلاعات تکمیلی", "contactInfo": "اطلاعات تماس", "primaryInfo-detail": "برای ثبت مخاطب حداقل یکی از فیلدهای این بخش را وارد کنید.", "primaryInfo-detail-on-mobile": "برای ثبت مخاطب حداقل یکی از فیلدهای بخش اطلاعات اصلی را وارد کنید.", "firstName_en": "First Name", "lastName_en": "Last Name", "position-jobTitle_en": "Job Title", "organization-business_en": "Company Name"}, "messages": {"success-create-contact": "مخاطب با موفقیت ایجاد گردید.", "change-contact-group": "گروه مخاطب تغییر پیدا کرد."}, "errors": {"meetingsDeleteContactDomainException": "این مخاطب در راهکار جلسات استفاده شده است.", "lettersDeleteContactDomainException": "این مخاطب در راهکار مکاتبات استفاده شده است.", "DeleteContactDomainException": "این مخاطب در راهکار مکاتبات و جلسات استفاده شده است.", "error-change-contact-group": "عملیات تغییر گروه مخاطب با خطا مواجه شد."}, "validation": {}, "strings": {}}, "secretariats": {"labels": {"secretariatAdmin": "راهبران دبیرخانه", "createSecretariat": "ایجاد دبیرخانه", "nameHeadCell": "نام دبیرخانه", "codeHeadCell": "ک<PERSON> دب<PERSON>رخانه", "descriptionHeadCell": "توضیحات", "mobileTitle": "دبیرخانه‌ها", "saveCreateSecretariat": "ذخیره", "secretariat": "دب<PERSON><PERSON>خانه", "name": "نام دبیرخانه", "code": "ک<PERSON> دب<PERSON>رخانه", "description": "توضیحات", "secretariatInfoTab": "اطلاعات دبیرخانه", "indicatorsTab": "اندیکاتورها", "letterLayoutsTab": "قالب های چاپی", "eceConfigTab": "تنظیمات ECE", "forwards": "ارجاعات", "saveLayout": "ذخیره", "cancelSaveLayout": "انصراف", "letterPreview": "پیش نمایش", "editSecretariats": "ثبت تغییرات", "cancelEditSecretariats": "انصراف", "createLayout": "<PERSON>ا<PERSON><PERSON> جدید", "default": "پیش فرض", "layout-editor-settings": "تنظیمات کلی قالب چاپی", "TitleEditInfoLayout": "ویرایش اطلاعات قالب نامه", "TitleEditLayout": "ویرایش قالب نامه", "TitleDeleteLayout": "حذ<PERSON> قالب نامه", "createIndicator": "ایجاد اندیکاتور", "indicator": "اندیکاتور", "indicatorStatusHeadCell": "وضعیت", "indicatorNameHeadCell": "نام اندیکاتور", "indicatorsCodeHeadCell": "کد اندیکاتور", "indicatorsDescriptionHeadCell": "توضیحات", "indicatorName": "نام اندیکاتور", "indicatorCode": "کد اندیکاتور", "indicatorDescription": "توضیحات", "indicatorFirstIndex": "اولین شماره مسلسل", "indicatorLastIndex": "آخرین شماره مسلسل", "indicatorResetInterval": "دوره تکرار ریست شدن  شمارنده مسلسل", "letterNumberFormat": "قالب شماره نامه", "indicatorSaveBtn": "ذخیره", "indicatorCreateBtn": "ایجاد اندیکاتور", "indicatorCancelBtn": "انصراف", "indicatorListIndex": "لیست اندیس‌های اندیکاتور", "indicatorExportOnListIndex": "خروجی اکسل از اندیس‌های اندیکاتور", "excelExport": "خروجی اکسل", "SerialNumber": "مسلسل", "Separator": "کاراکتر جداکننده", "IndicatorCode": "کد اندیکاتور", "SecretariatCode": "ک<PERSON> دب<PERSON>رخانه", "organizationCode": "ک<PERSON> سازمان", "YY": "دو رقم سمت راست عدد سال", "YYYY": "چهار رقم کامل سال", "M": "عدد ماه به صورت تک رقمی", "MM": "عدد ماه به صورت دو رقمی", "floodChange": "سیلاب", "sample": "نمونه", "layoutName": "نام قالب", "layoutSize": "اندازه صفحه", "layoutDescription": "توضیحات", "layoutIsDefault": "قالب پیش فرض", "layoutCreate": "ای<PERSON><PERSON> قالب", "layoutEdit": "ثبت تغییرات", "titleDialogEditLayout": "ویرایش اطلاعات قالب چاپی", "titleDialogCreateLayout": "ایج<PERSON> قالب چاپی", "chooseBackground": "فایل سربرگ", "removeBackground": "حذف سربرگ", "cc": "گیرندگان رونوشت", "hiddenCc": "گیرندگان رونوشت مخفی", "date": "تاریخ", "attachment": "پیوست", "registerInformation": "اطلاعات ثبتی", "references": "مراجع", "attachments": "پیوست ها", "signature": "امضا", "hasSignature": "امضا شده", "subject": "موضوع", "priority": "اولویت", "confidentiality": "محرمانگی", "sender": "فرستنده", "recipient": "گیرنده", "employees": "کارمندان", "contacts": "مخاطبان", "pageNumber": "شماره صفحه", "logo": "لوگو", "staticText": "ثابت متنی", "addStaticText": "<PERSON><PERSON><PERSON><PERSON> ثابت متنی", "titleFirstTrue": "لقب + نام + نام خانوادگی +‌ عنوان شغلی/سمت +‌ نام تجاری/سازمان", "titleFirstFalse": "عنوان شغلی/سمت + نام تجاری/سازمان + لقب + نام + نام خانوادگی", "titleFirstFalseWithoutTitle": "عنوان شغلی/سمت + نام تجاری/سازمان + نام + نام خانوادگی", "titleFirstTrueWithoutTitle": " نام + نام خانوادگی +‌ عنوان شغلی/سمت +‌ نام تجاری/سازمان", "Text": "متن ثابت", "useEnglishDigits": "چاپ اعداد به صورت انگلیسی", "fontSizeRange": "در بازه 1 تا 40 پیکسل وارد نمایید", "letterContentFontSize": "اندازه فونت متن نامه برای چاپ", "subjectLetter": "موضوع نامه", "note": "یادداشت", "forwardSender": "فرستنده ارجاع", "forwardReceivers": "گیرنده ارجاع", "forwardedLetter": "تاریخ ارجاع", "letterType": "نوع نامه", "fromDate": "از تاریخ", "toDate": "تا تاریخ", "recipientForwarded": "گیرنده ارجاع ", "senderForwarded": "فرستنده ارجاع", "filter": "فیلتر", "FilterAdded": "فیلتر", "letterNumber": "شماره نامه", "numberingDate": "زم<PERSON> ثبت", "signerStatus": "وضعیت امضاها", "addUserAdminSecretariats": "افزودن کارمند به راهبران دبیرخانه", "accessesSecretariatsSuperAdmins": "دسترسی‌های راهبر دبیرخانه", "massegeDeactiveIndicator": " اندیکاتور انتخاب شده، غیرفعال است. لطفا برای شماره کردن یک اندیکاتور فعال انتخاب کنید.", "orginText": "با کلیک رو هر فایل می توانید آن را به عنوان فایل متن نامه مشخص کنید", "chooseAttachment": "انتخاب فایل ضمیمه", "changeOriginText": "تغییر فایل متن اصلی نامه", "addOriginText": "اضافه کردن فایل متن اصلی نامه", "sizeFile": "حجم هر فایل باید کمتر از 30 مگابایت باشد", "addAtachment": "اضافه کردن پیوست", "dontAtachment": "اضافه فایلی پیوست نشده است پیوست", "clickOriginFile": "با کلیک رو هر فایل می توانید آن را به عنوان فایل متن نامه مشخص کنید", "TranscriptDescription": "با کلیک رو هر فایل می توانید آن را به عنوان فایل متن نامه مشخص کنید", "addDescription": "برای این رونوشت توضیحات بنویسید", "registerSecretariat": "دبیرخانه ثبت کننده", "registrarIndicator": "دفتر اندیکاتور ثبت کننده ", "viewLetter": "مشاهده نامه", "letterTemplateSample": "نمونه شماره نامه:", "previewLayout": "پیش نمایش قالب", "additionalAttachments": "اطلاعات تکمیلی", "additionalAttachmentsBody": "این بخش می‌تواند بعد از امضا یا شماره نامه اضافه شود.", "selectSecretariat": "انتخاب دبیرخانه", "selectIndicator": "انتخاب اندیکاتور", "stamp": "مهر"}, "tokens": {"person": {"firstName": "نام", "lastName": "نام خانوادگی", "jobTitle": "عنوان شغلی/سمت", "business": "نام تجاری/سازمان", "personTitle": "لقب"}}, "messages": {"success-create-indicator": "اندیکاتور با موفقیت ایجاد گردید.", "success-create-secretariat": "دبیرخانه با موفقیت ایجاد گردید.", "success-create-layout": "قالب با موفقیت ایجاد گردید.", "update-ece-list": "به روزرسانی لیست نامه‌های ECE شروع شد.", "success-connect": "اتصال با موفقیت انجام شد.", "success-save-ece-setting": "تنظیمات ارسال و دریافت ECE با موفقیت ذخیره شد."}, "errors": {"error-update-ece-list": "به روزرسانی لیست نامه‌های ECE با خطا مواجه شد.", "error-connect": "اتصال با خطا مواجه شد."}, "validation": {}, "strings": {"noSecretaries": "در حال حاضر دبیرخانه ای در این سازمان وجود ندارد", "noLayout": "در حال حاضر قالب نامه ای تعریف نشده است", "noIndicators": "در حال حاضر اندیکاتوری وجود ندارد"}}, "receipts": {"labels": {"createReceipt": "ثبت رسید ارسال", "edit-receipt": "ویرایش رسید ارسال", "receipt": "ر<PERSON>ی<PERSON> ارسال", "viewReceipt": "مشاهده رسید ارسال", "noReceipt": "رسید ارسالی برای این نامه ثبت نشده است", "receiptList": "لیست رسیدهای ثبت شده"}}, "forwardLetter": {"labels": {"null": "بدون تگ", "ForInformation": "جهت اطلاع", "RequestToSign": "درخواست امضا", "ForNotice": "جهت استحضار", "ForFollowUp": "جهت پیگیری", "RequestToTakeAction": "جهت بررسی و اقدام", "ForAction": "ج<PERSON><PERSON> اقدام", "RequestNumbering": "درخواست شماره", "RequestToSend": "درخواست ارسال", "RequestToCompleteInformation": "درخواست تکمیل اطلاعات", "Uncategorized": "دسته بندی نشده", "normal": "معمولی", "secret": "م<PERSON><PERSON><PERSON>", "normal2": "ارجاع معمولی", "secret2": "ارجاع مخفی", "chooseTags": "با انتخاب یکی از تگ‌های زیر، به گیرنده ارجاع کمک می‌کنید تا نامه را سریع تر پیدا کند.", "typeForward": "نوع ارجاع:", "noDataForward": "گیرنده‌ای برای ارجاع اضافه نشده است", "addForwardtoList": "اضافه کردن گیرندگان بیشتر", "textParaph": "متن دستور (پاراف)", "forwardRecipients": "گیرندگان ارجاع", "recipients": "گیرندگان *", "recipient": "گیرنده *", "sender": "فرستنده *", "transcript": "رونوشت", "addRecipients": "اضافه کردن گیرندگان", "lastForward": "آخرین ارجاع", "referrerForward": "ارجاع دهنده:", "referralOrderForward": "دستور ارجاع:", "addReference": "افزودن مرجع", "notAddReference": "مرجعی ثبت نشده است", "typeReference": "نوع مرجع", "referenceNumber": "شماره نامه مرجع", "dateReference": "تاریخ مرجع", "incomingNumber": "شماره نامه وارده", "addIncomingNumber": "شماره نامه وارده را وارد نمایید", "trackingCodeAndDestinationIndicator": "کد پیگیری / اندیکاتور مقصد", "listAttachmentTitle": "پیوست‌ها", "attachment": "پیوست‌", "chooseAttachment": "انتخاب فایل ضمیمه", "revertLetterForward": "بازگشت ارجاع", "revertForwardLetterConfirmation": "شما در حال بازگرداندن ارجاع به کارتابل خود هستید، آیا مطمئن هستید؟", "letterHasBeenReadByRecipient": "این ارجاع توسط گیرنده ارجاع باز شده است و قابل بازگشت نیست.", "isRevertLetterForward": "شما در حال بازگرداندن ارجاع به کارتابل خود هستید، آیا مطمئن هستید؟", "RevertLetterForwardOpened": "این ارجاع توسط گیرنده ارجاع باز شده است و قابل بازگشت نیست."}}, "correspondence": {"management": {"correspondence-management": "مدیریت مکاتبات", "correspondence-setting": "تنظیمات مکاتبات", "all-forwarded-letter": "ارجاعات سازمان", "forward-access": "دسترسی ارجاعات", "secretariats-management": "مدیریت دبیرخانه‌ها", "reports": "گزارش ها", "reference-letters": "نامه‌های مرجع", "reference": "مرجع هستند", "not-reference": "مرجع نیستند", "is-references-letter-in-another-letter": "نامه‌هایی که در یک نامه دیگر"}, "letters": {"ece": {"labels": {"transformToLetter": "تبدیل به نامه", "createEceLetter": "ثبت نامه ECE", "choiceEceLetter": "انتخاب فایل XML", "allowFormatXml": "- فقط فرمت XML مجاز است.", "sizeFile": "- حجم فایل باید کمتر از 10 مگا بایت باشد", "transformXmlToReceipt": " فایل XML، برای تبدیل به نامه وارده، دارای مشکلات زیر است :"}, "sent": {"labels": {"eceBoxDate": "تاریخ‌ارسال", "to": "گیرنده", "subject": "موضوع", "requester": "ارسال‌کننده", "status": "وضعیت", "received": "رسیده به اینباکس مقصد", "sender": "ارسال‌شده", "remindTime": "زمان باقیمانده تا درخواست مجدد:", "understand": "متوجه شدم", "update": "به روزرسانی", "notExistEce": "نامه ECE وجود ندارد"}}}, "labels": {"print": "چاپ", "printLayout": "نسخه چاپی", "send": "ارسال", "export": "دریافت", "send-ece": "ارسال با فرمت ECE", "export-ece": "دریافت فرمت ECE", "enter-ece-email-recipient": "ایمیل گیرنده ECE را وارد کنید", "ece-email-recipient": "ایمیل گیرنده ECE", "choose-layout-send-ece": "انتخاب قالب برای خروجی ECE", "print-letter": "چاپ نامه", "choose-letter-layout": "انت<PERSON>اب قالب", "no-letter-layout": "بدون قالب", "default-letter-layout": "قالب پیش فرض سازمان", "compose-info-config": "شخصی‌سازی نمایش پیشفرض بخش‌های نامه", "correct-email": "لطفا ایمیل را به درستی وارد نمایید", "successSendECE": "ارسال ECE با موفقیت انجام شد", "from": "فرستنده ایمیل", "to": "گیرنده ایمیل:", "emailSubject": "موضوع ایمیل:", "emailText": "متن ایمیل:", "subject": "موضوع ایمیل", "eceBoxDate": "زمان ورود ایمیل", "retryToConvertEceToletter": "", "showSignInPrint": "تصویر امضاها در چاپ نمایش داده شود.", "show-stamps-in-print": "تصویر مهر سازمان در چاپ نمایش داده شود.", "read-in": "خوانده شده در", "unread": "خوانده نشده", "internal": "داخلی", "incoming": "وارده", "outgoing": "ارسالی", "dateLetters": "تاریخ نامه", "forwardDate": "تاریخ ارجاع", "forwardSender": "فرستنده ارجاع", "numberingDate": "زم<PERSON> ثبت", "recipientsLetter": "گیرندگان", "senderLetter": "فرستنده", "signerLetter": "وضعیت امضاها", "mailReceiver": "ایمیل مقصد", "requester": "ارسال کننده", "dateSender": "تاریخ ارسال", "senderState": "وضعیت ارسال", "correspondenceEnded": "نامه‌های بسته شده", "correspondenceTerminated": "مختومه", "correspondenceDiscarded": "لغو شده", "noForwardLetter": "نامه‌ ارجاع شده‌ای وجود ندارد", "clearSelectedCategory": "پاک کردن همه", "showResult": "نمایش نتایج", "allLetter": "همه نامه‌ها", "forwardDescription": "دستور ارجاع:", "description": "توضیحات", "terminate": "مختومه شده", "hasTerminate": "اختتام نامه", "request": "درخواست", "answer": "پا<PERSON><PERSON>", "create-forward-letter": "دستورهای ایجاد شده", "submit": "ثبت", "readyText": "متن دستور آماده", "create-forward-letter-sample": "ایجاد نمونه متن دستور جدید", "create-new-forward-letter": "متن دستور ارجاع جدید", "signers": "امضاکنندگان", "statusSign": "وضعیت امضا :", "signed": "امضا شده", "awaitingSignature": "در انتظار امضا", "generalStatusLetter": "وضعیت کلی نامه :", "closedLetter": "مختومه شده", "cancelLetter": "لغو شده", "NotShowingFreePositions": "عدم نمایش جایگاه‌های آزاد", "deprivationPosition": "ابتدا سمت را از کارمند مربوطه بگیرید.", "employeesList": "مشاهده لیست کارمندان", "reservedPosition": "این سمت دارای جایگاه رزرو شده است.", "freeSlot": "ابتدا آن جایگاه‌ها را آزاد کنید.", "correspondence-sample-note": "نام قالب متنی", "create-new-correspondence-sample": "ایجاد نمونه متن جدید", "create-new-correspondence-sample-hint": "قالب متن ایجاد شده توسط همه کاربران مکاتبات قابل مشاهده و استفاده خواهد بود.", "referenceCount": "تعداد مرجع", "ece-receipt": "ارسال رسید ECE"}, "void-letter": {"labels": {"void-letter": "ابطال نامه", "voided": "باطل شده", "dont-void-letter": "نامه قابل ابطال نیست."}, "messages": {"confirm-void-letter": "این نامه باطل خواهد شد، آیا مطمئن هستید؟", "massages-void-letter": "با باطل شدن نامه، شماره آن نامه دوباره قابل استفاده نخواهد بود.", "LetterHasReceiptException": "برای این نامه رسید ارسال ثبت شده است.", "LetterExposedByEceException": "این نامه از طریق ECE ارسال شده است.", "LetterAlreadyVoidedException": "این نامه از قبل باطل شده است.", "LetterIsVoidException": "این نامه در حال انجام عملیات (بروزرسانی، چاپ، ای‌سی‌ای، امضا، شماره کردن، ارجاع، بازگشت ارجاع، اختتام، ثبت رسید، لغو) می باشد.", "create-forward-letter-sample-warning": "متن دستور ایجاد شده توسط همه کاربران مکاتبات قابل مشاهده و استفاده خواهد بود."}}}, "noDataMessage": {"labels": {"correspondenceEnded": "نامه‌ بسته شده‌ای وجود ندارد", "correspondenceDiscarded": "نامه‌ لغو شده‌ای وجود ندارد", "correspondenceTerminated": "نامه‌ مختومه‌ای وجود ندارد", "noLetters": "نامه‌ بسته شده‌ای وجود ندارد"}}}, "meetings": {"labels": {"create-meeting": "ایج<PERSON> جلسه جدید", "select-meeting-location": "انتخاب محل جلسه", "create-meeting-location": "ایج<PERSON> محل جلسه", "edit-meeting-location": "ویرایش محل جلسه", "status": "وضعیت", "meeting-location": "<PERSON><PERSON><PERSON>", "meeting-location-title": "نام محل جل<PERSON>ه", "duration": "زمان", "requester": "ایج<PERSON> کننده", "date": "تاریخ", "meeting-history": "سوابق جلسه", "meetings-recurrence": "تکرارپذیری جلسه", "no-recurrent": "بدون تکرار", "weekly-recurrent-in-certain-days": "هفتگی در روزهای مشخص", "recurrent-in-specific-date": "تکرار در تاریخ‌های مشخص", "back_to_meetings_cartable": "بازگشت به جلسات من", "recurrent-meeting": "تکرار جلسه", "without-recurrence-end": "بدون پایان", "edit-recurrent-meeting": "ویرایش جلسه تکرارپذیر", "cancel-recurrent-meeting": "لغو جلسه تکرارپذیر", "update-single-instance": "ویرایش همین جلسه", "cancel-single-instance": "لغو همین جلسه", "edit-recurrent-meetings-series-from-here-on": "ویرایش سری از اینجا به بعد", "cancel-recurrent-meetings-series-from-here-on": "لغو سری از اینجا به بعد", "edit-recurrent-meetings-series-description": "در صورت انتخاب این گزینه، کلیه نمونه‌های سری از اینجا به بعد حذف شده و جلسات جدیدی مطابق با این جلسه ایجاد خواهد شد.", "delete-meeting-recurrence": "شما تکرارپذیری این جلسه را حذف کردید.", "delete-meeting-recurrence-description": "با حذف الگوی تکرارپذیری، تمام جلسات این سری حذف می‌شود و فقط همین یک جلسه باقی خواهد ماند.", "edit-recurrence-item-meeting": "شما تکرارپذیری این جلسه را تغییر دادید", "edit-recurrence-item-meeting-description": "کلیه نمونه‌های سری از اینجا به بعد حذف شده و جلسات جدیدی مطابق با این جلسه ایجاد خواهد شد", "delete-meetinge-recurrence": "شما تکرارپذیری این جلسه را حذف کردید.", "delete-meetinge-recurrence-description": "با حذف الگوی تکرارپذیری، تمام جلسات این سری حذف می‌شود و فقط همین یک جلسه باقی خواهد ماند.", "finish-meeting": "اخت<PERSON>ا<PERSON> جلسه", "minutesMeeting": "ثبت صورت جلسه", "viewMinutesMeeting": "مشاهده صورت جلسه", "finish-meeting-description": "با اختتام جلسه، از این به بعد اطلاعات جلسه و صورت جلسه قابل ویرایش نخواهد بود.", "backToMeeting": "بازگشت به جلسه", "meetingOrder": "دستور جلسه را وارد کنید", "recurrenceEndDate": "تاریخ پایان تکرار", "timeMeeting": "ساعت جلسه", "startDate": "ساعت شروع", "endDate": "ساعت پایان", "periodTime": "مدت زمان :", "fullTime": "تمام روز", "meetingLocationInformation": "اطلاعات محل جلسه", "statusMeetingLocation": "وضعیت محل جلسه در تاریخ", "hoursFull": "ساعات پر شده", "fromHours": "از ساعت", "to": "تا", "viewMeeting": "مشاهده جلسه", "titleMeeting": "عنوان جلسه", "dateMeeting": "تاریخ جلسه", "setTimeMeeting": "تعیین ساعت جلسه", "participants": "مدعوین جلسه", "countParticipants": "تعداد مدعوین", "attachments": "پیوست ها", "addMeetingOrder": "دستور جلسه", "finalizedMeeting": "جلسه نهایی شده", "cancelMeeting": "جلسه لغو شده", "invited": "دعوت شدم", "creator": "ایج<PERSON> کردم"}, "messages": {"noMinutesMeeting": "صورت جلسه برای این جلسه ثبت نشده.", "createMeetingWithPosition": "با کدام سمت می‌خواهید جلسه ایجاد کنید؟", "setTimeMeeting": "لطفا زمان جلسه را تعیین کنید.", "sizeFile": "حجم هر فایل باید کمتر از 15 مگابایت باشد.", "unsetMeetingLocation": "محل جلسه‌ای ایجاد نشده است", "orderConfirmationMeeting": "سفارش جلسات شما ثبت و پرداخت شده. لطفا منتظر تایید پرداخت از سمت آوات بمانید.", "notActiveMeetingAndAddUser": "راهکار جلسات برای کاربر شما فعال نیست. برای ورود به جلسات کاربر خود را به جلسات اضافه کنید"}}, "meetings-cartable": {"labels": {"next-week": "هفته بعد", "today": "امروز", "prev-week": "ه<PERSON><PERSON><PERSON> قبل", "meetings-calendar": "نمایش تقویم", "meetings-list": "نمایش لیست", "show-canceled-meetings": "نمایش لغو شده‌ها"}}, "projects": {"labels": {"title-project": "عنوان پروژه", "create-new-project": "ایجاد پروژه جدید", "create-project": "ایجاد پروژه", "update-project": "ویرایش پروژه", "save-project": "ذخیره", "new-create-project": "ایجاد پروژه جدید", "project-owner": "مالک پروژه", "owner-member": "عضو/ مالک پروژه", "project-member": "اعضا پروژه", "no-position-Projects": "برای ایجاد پروژه سمت شما باید در سازمان مشخص باشد.", "added-employee": "اضافه کردن اعضا", "select-project": "انتخاب پروژه", "add-member": "اضافه کردن اعضا"}, "tabs": {"members": "اعضای پروژه", "tasks": "کارهای پروژه", "info": "اطلاعات پروژه"}, "massages": {"delete-project": "حذف عضو از پروژه؟", "announcement-delete-project": "حذف عضو از پروژه", "confirm-delete-member-project": "آیا از حذف این عضو از پروژه مطمئن هستید؟", "member-task-project": "این عضو مسئول انجام یک یا چند کار است و در حال حاضر امکان حذف او وجود ندارد.", "delete-task-project": "حذف کار از پروژه؟", "confirm-delete-task-project": "آیا از حذف این کار از پروژه مطمئن هستید؟", "not-member": "کارمند انتخاب‌شده در پروژه حضور ندارد. لطفاً کارمند دیگری را انتخاب کنید یا او را به پروژه اضافه کنید."}}, "tasks": {"labels": {"tasks": "کارها", "view-task": "مشاهده کار", "create-new-task": "ایج<PERSON> کار جدید", "create-task": "ای<PERSON><PERSON> کار", "unfinished-tasks": "کارهای انجام نشده", "finished-tasks": "کارهای انجام شده", "task-done": "کار انجام شد", "task-undone": "کار انجام نشد", "delete-task": "<PERSON><PERSON><PERSON> کار", "history-task": "سوابق کار", "assigned-task-to": "اختصاص کار به", "create-task-by": "ایجاد کار توسط"}, "massages": {"delete-confirm": "شما در حال حذف کار هستید، آیا مطمئن هستید؟", "add-position-for-create-task": "برای ایجاد کار سمت شما باید در سازمان مشخص باشد."}}, "solutionHistory": {"labels": {"letter": {"sign": "امضا", "compose": "ایجاد", "forward": "ارجاع", "number": "شماره", "receipt": "<PERSON><PERSON><PERSON> رسید", "edit-receipt": "ویرایش رسید", "terminate": "مختومه", "discard": "لغو", "history": "سوابق نامه", "basicInformation": "اطلاعات اولیه", "letter-forwards-filter": "فقط ارجاعات نمایش داده شود.", "history-print": "چاپ سوابق"}, "task": {"history": "سوابق کار", "create": "کار را ایجاد کرد.", "updateStatus": "وضعیت کار را تغییر داد.", "assign": "اختصاص کار را تغییر داد.", "update": "کار را ویرایش کرد.", "clearAssign": "اختصاص کار پاک شد.", "card-collapse": {"updateStatus": "وضعیت کار را تغییر داد.", "clearAssign": "اختصاص کار را تغییر داد.", "assign": "اختصاص کار به:", "update": "کار را ویرایش کرد.", "create": "کار را ایجاد کرد.", "done": "کار را به حالت انجام شده تغییر داد.", "undone": "کار را به حالت انجام نشده تغییر داد."}, "fields": {"description": "توضیحات را تغییر داد.", "startDate": "زمان را تغییر داد.", "title": "عنوان کار را تغییر داد.", "startTime": "ساعت را تغییر داد."}}, "meeting": {"update": "جلسه را ویرایش کرد.", "cancel": "جلسه را لغو کرد.", "create": "جلسه را ایجاد کرد.", "inviteEmployee": "کارمند به مدعوین اضافه شد.", "removeEmployee": "کارمند از مدعوین حذف شد.", "inviteContact": "مخاطب به مدعوین اضافه شد.", "removeContact": "مخاط<PERSON> از مدعوین حذف شد.", "createMinutes": "صورت جلسه را ثبت کرد.", "updateMinutes": "صورت جلسه را ویرایش کرد.", "finish": "جلسه را مختومه کرد.", "history": "سوابق جلسه", "backLabel": "بازگشت یه جلسه", "cancel-description": "توضیحات لغو", "card-collaspe": {"inviteEmployee": "اضافه شدند.", "removeEmployee": "حذ<PERSON> شدن<PERSON>.", "inviteContact": "اضافه شدند.", "removeContact": "حذ<PERSON> شدن<PERSON>."}}}}, "letterHistory": {"labels": {"sign": "نامه را امضا کرد.", "compose": "نامه را ایجاد کرد.", "forward": "نامه را ارجاع داد.", "number": "نامه را شماره کرد.", "receipt": "رسید ارسال ثبت کرد.", "terminate": "نامه را مختومه کرد.", "discard": "نامه را لغو کرد.", "void": "نامه را باطل کرد", "update": "نامه را ویرایش کرد.", "forwardedLetterReverted": "ارجاع را برگرداند.", "forward-reverse": "ارجاع را برگرداند.", "revertForwardFrom": "بازگشت ارجاع از:", "revertSecretForwardFrom": "بازگشت ارجاع مخفی از:", "forwardedLetterRevertedAt": "ارجاع بازگشت داده شده در:", "void-description": "توضیحات ابطال:", "forward-to": "ارجاع به", "secret": "م<PERSON><PERSON><PERSON>"}, "fields": {"body": "متن نامه را ویرایش کرد.", "sender": "ارسال کننده نامه را تغییر داد.", "recipient": "گیرندگان نامه را تغییر داد.", "attachments": "پیوست‌های نامه را تغییر داد.", "secretariat": "دبیرخانۀ نامه را تغییر داد.", "indicator": "اندیکاتور نامه را تغییر داد.", "subject": "موضوع نامه را تغییر داد.", "incomingDate": "تارخ نامه وارده نامه را تغییر داد.", "incomingNumber": "شماره نامه وارده نامه را تغییر داد.", "cc": "گیرندگان رونوشت نامه را تغییر داد.", "bcc": "گیرندگان رونوشت مخفی نامه را تغییر داد.", "signer": "امضا کنندگان نامه را تغییر داد.", "description": "توضیحات نامه را تغییر داد.", "priority": "اولویت نامه را تغییر داد.", "references": "مراجع نامه را تغییر داد.", "confidentiality": "محرمانگی نامه را تغییر داد.", "date": "تاریخ نامه را تغییر داد.", "additionalAttachments": "ضمائم تکمیلی نامه را تغییر داد."}}, "solutions": {"labels": {"solutions": "راهکارها", "active-solutions": "راهکارهای فعال", "other-solutions": "سایر راهکارها", "develop-solutions": "راهکارهای درحال توسعه", "no-active-solution": "در حال حاضر راهکار فعالی در این سازمان ندارید", "awat-solutions": "راهکارهای آوات", "Correspondence": "مکاتبات", "correspondence": "مکاتبات", "Tasks": "کارها", "Meeting": "جلسات", "Meetings": "جلسات", "meetings": "جلسات", "soon": "به زودی", "alarmSolution": "آماده شد خبرم کن", "monthly-free-trial": "طرح رایگان یک ماهه", "expiration-date": "پایان اعتبار", "showTariff": "مشاهده تعرفه‌ها و خرید", "trialTariff": "استفاده رایگان", "Correspondence-free-service": "طرح رایگان مکاتبات", "Tasks-free-service": "طرح رایگان کارها", "Meetings-free-service": "طرح رایگان جلسات", "active-free-service": "فعالش کن", "Gigabyte": "گیگابایت", "User": "کاربر", "Day": "روز", "UserLeft": "کاربر باقی‌مانده", "DayLeft": "روز باقی‌مانده", "GigabyteLeft": "گیگابایت باقی‌مانده", "buy-service": "<PERSON><PERSON><PERSON>د سرویس", "solution-settings": "تنظیمات راهکار", "upgrade": "ارتقا سرویس", "newOrderPeriod": "خرید برای دوره زمانی جدید", "viewTariffAndSale": "مشاهده تعرفه و خرید", "notActiveCorrespondenceAndAddUser": "راهکار مکاتبات برای کاربر شما فعال نیست. برای ورود به مکاتبات کاربر خود را به مکاتبات اضافه کنید.", "toman": "تومان", "moreInformation": "اطلاعات بیشتر", "orderSuccessfully": "سفارش شما با موفقیت ثبت شد", "newOrderAndServicePeriod": "برای خرید سرویس جدید از 14 روز مانده به پایان دوره سرویس می توانید اقدام کنید.", "enterYourRequestedValues": "مقادیر درخواستی خود را وارد کنید", "Forms": "فرم‌های اداری", "Workflow": "گردش کار و مدیریت فرایندهای اداری", "Messages": "چت و پیام‌رسانی", "Salary": "حقوق و دستمزد", "Attendance": "حضور و غیاب", "Accounting": "حسابدا<PERSON>ی", "ProjectManagement": "مدیریت پروژه", "DocumentManagement": "مدیریت مستندات"}, "messages": {"term1": "برای مشاهده قیمت و ایجاد سفارش، لازم است تا تعداد کاربر(بر حسب نفر)، مقدار فضای در اختیار(بر حسب گیگابایت) و دوره زمانی (30 روزه یا 365 روزه) مورد نظر خود را وارد کنید.", "term2": "گزینه «جزییات هزینه‌ها» جزئیات مربوط به مبلغ قابل پرداخت را به شما نمایش می‌دهد.", "term3": "تا پیش از پایان دوره زمانی یک سرویس، می‌توانید تعداد کاربران و یا فضای در اختیار آن سرویس را به اندازه مورد نیازتان ارتقاء بدهید.", "term4": "در صورتی که سرویس رایگان 30 روزه را فعال کرده باشید، می‌توانید در هر زمانی قبل از پایان 30 روز، سفارش جدیدی ثبت کنید.", "success-order": "سفارش با موفقیت ایجاد گردید"}, "solutions-list-description": {"correspondence": "مکاتبات اداری «آوات» به خوبی با نیازها، روال‌ها و ساختارهای سازمانی در شرایط امروزی سازگار است. «آوات» نامه‌نگاری را به یک روش ارتباطی ساده، سریع و ارزان در مراودات اداری تبدیل می‌کند؛ در عین حال امن است، به واسطۀ ابری بودن از همه جا در دسترس است و به هیچ ابزار واسطی مانند مایکروسافت آفیس وابستگی ندارد. هر کارمندی در سازمان با هر میزان دانش و سابقه در امور مربوط به دبیرخانه، می‌تواند با «آوات» کار کند و به دور از پیچیدگی‌های این حوزه، بر کارهای تخصصی خود متمرکز شود.", "meetings": "جلسات «آوات» یک راه‌کار ابری مدرن برای برنامه‌ریزی رویدادهای متنوع سازمانی‌ست؛ به کارمندان کمک می‌کند تا ساده و سریع زمان‌های در دسترس افراد را بیابند، آن‌ها را به یک رویداد دعوت کنند و در مورد دستور و نتیجه آن رویداد با آن‌ها هم‌فکری کنند. یکپارچگی و هماهنگی راه‌کار جلسات با راه‌کارهای دیگر «آوات» این امکان را فراهم می‌کند تا بتوان داده‌هایی مثل صورت جلسه را در سایر راه‌کارها هم به جریان انداخت...", "tasks": "کارهای «آوات» چیزی بیشتر از تعریف، به جریان انداختن و پیگیری کارهاست؛ راه‌کاریست برای با هم کار کردن و نتیجه‌بخش بودن. کارمندان و مدیران سازمان را از دنیای حسابرسی ساعت‌ها به دنیای رقابتی ارزش‌آفرینی می‌برد. این راه‌کار تمام ابزارهای متداول مدیریت کارها را دارد و در عین حال ابزارهای نوینی را هم در اختیار افراد مختلف در سازمان قرار می‌دهد تا بتوانند روی کیفیت کارها و بهبود نتایج متمرکز شوند...", "forms": "سیستم فرم‌های اداری با حذف کاغذبازی و افزایش دقت، فرآیندهای سازمانی را سریع‌تر و پیگیری درخواست‌ها را آسان‌تر می‌کند و به هماهنگی بهتر بین کارکنان و مدیران کمک می‌کند.", "workflow": "گردش کار و مدیریت فرآیندهای اداری، با افزایش هماهنگی و کاهش اتلاف منابع، انجام سریع‌تر و پیگیری مؤثرتر وظایف را برای کارکنان و مدیران فراهم می‌کند.", "messages": "سیستم چت و پیام‌رسانی، ارتباط سریع و هماهنگی بین تیم‌ها را تسهیل کرده و پیگیری گفتگوها را ساده‌تر می‌کند؛ این سیستم تعاملات کاری را مؤثرتر و بهره‌وری را افزایش می‌دهد.", "salary": "سیستم حقوق و دستمزد، محاسبات دقیق و به‌موقع پرداخت‌ها را تضمین کرده، خطاهای انسانی را کاهش می‌دهد و پیگیری وضعیت پرداخت‌ها و مزایا را برای کارکنان و مدیران آسان‌تر می‌کند.", "attendance": "سیستم حضور و غیاب، پایش دقیق و خودکار ساعات کاری را ممکن کرده و فرآیند ثبت، پیگیری و مدیریت حضور کارکنان را ساده‌تر و شفاف‌تر می‌کند و به افزایش بهره‌وری سازمان کمک می‌کند.", "accounting": "سیستم حسابداری با خودکارسازی ثبت و پردازش مالی، دقت محاسبات را افزایش داده و گزارش‌دهی مالی را تسهیل می‌کند؛ این سیستم به شفافیت و مدیریت بهتر منابع مالی سازمان کمک می‌کند.", "project-management": "سیستم مدیریت پروژه با سازماندهی وظایف، تعیین زمان‌بندی و پیگیری پیشرفت کارها، هماهنگی تیم را بهبود می‌بخشد و دستیابی به اهداف پروژه را سریع‌تر و کارآمدتر می‌سازد.", "document-management": "سیستم مدیریت مستندات، سازماندهی، ذخیره و دسترسی آسان به اسناد را فراهم کرده و جستجو و اشتراک‌گذاری اطلاعات را سریع‌تر و ایمن‌تر می‌کند و از پراکندگی داده‌ها جلوگیری می‌کند."}}, "tariff": {"discount": {"messages": {"invalid": "کد تخفیف معتبر نیست.", "valid": "کد تخفیف با موفقیت اعمال شد.", "expired": "تاریخ استفاده از کد تخفیف گذشته است.", "ran_out": "کد تخفیف قبلا استفاده شده است."}}, "labels": {"cancelOrder": "لغو سفارش قبلی", "paymentOrder": "پرداخت سفارش", "already-have-an-order": "شما از قبل یک سفارش ثبت شده برای این راهکار دارید", "already-have-an-order-correspondence": "شما از قبل یک سفارش ثبت شده برای راهکار مکاتبات دارید", "already-have-an-order-meetings": "شما از قبل یک سفارش ثبت شده برای راهکار جلسات دارید", "already-have-an-order-and-we-call-you": "شما در حال حاضر یک سفارش ثبت شده دارید.", "cancelOrderAndCreateNewOrder": "لغو سفارش قبلی و ادامه", "confirm-cancel-order": "سفارش قبلی لغو شود؟", "yes": "بله", "no": "<PERSON><PERSON><PERSON>", "remainDays": "ارتقا برای remainDays روز باقی مانده از سرویس فعلی", "not-zero-counts": "هر دو مقدار کاربر و فضای ذخیره سازی نمی توانند 0 باشند.", "understand": "متوجه شدم", "discount-code-input": "کد تخفیف را اینجا وارد کنید", "discount-check-btn": "اعمال کد", "discount-clear-btn": "<PERSON><PERSON><PERSON>", "has-discount-code": "کد تخفیف دارید؟", "tariff-code": "کد تعرفه", "usersCount": "تعداد کاربر", "daysCount": "تعداد روزها", "storage": "فضای ذخیره سازی", "order-details": "مشخصات سفارش", "discount-code": "کد تخفیف", "copy-discount-code": "ک<PERSON>ی کد تخفیف", "discount-tariff": "تعرفه تخفیف", "30DaysBasic": "30 روزه", "365DaysBasic": "365 روزه", "day": "روزه", "Duration": "(مدت زمان استفاده از طرح)", "accessCorrespondence": "(تعداد نفرات دارای دسترسی به مکاتبات)", "availableSpaceLetterAttachment": "(میزان فضای در اختیار برای فایل‌های ضمیمه نامه)", "accessMeeting": "(تعداد نفرات دارای دسترسی به جلسات)", "availableSpaceMeetingAttachment": "(میزان فضای در اختیار برای فایل‌های ضمیمه جلسات)", "more-user": "تعداد کاربر اضافه‌ای که نیاز دارید", "more-user-in-new-service": "تعداد کاربر مورد نیاز در سرویس جدید", "moreStorage": "فضای ذخیره سازی اضافه‌ای که نیاز دارید", "moreStorageInNewService": "فضای ذخیره سازی مورد نیاز در سرویس جدید", "paymentAndViewInvoice": "ثبت سفارش و مشاهده پیش‌فاکتور", "maxStorage": "فضای ذخیره سازی نمی‌تواند بیشتر از 999 باشد", "maxUsers": "تعداد کاربران نمی‌تواند بیشتر از 999 باشد", "call-to-sales-department": "برای خرید {{count}} {{unit}} به بالا با بخش فروش تماس بگیرید:", "enter-tariff-code-message": "در صورتی که پیش‌فاکتور دریافت کرده‌اید، کد خود را در این قسمت وارد کنید تا سفارش شما ثبت شود:", "enter-tariff-code": "کد را اینجا وارد کنید", "discount": "تخفیف", "more-than": "بیشتر از"}}, "payment-result": {"labels": {"upload-payment-success": "سند پرداخت با موفقیت آپلود شد", "payment-success": "سفارش شما با موفقیت فعال شد.", "alert-payment-success": "پس از تایید، سفارش شما فعال خواهد شد", "download-payment-doc": "د<PERSON><PERSON><PERSON>د سند پرداخت", "back-to-organization": "بازگشت به سازمان "}}, "administration-orders-table": {"labels": {"display_name": "نام سازمان", "user_name_customer": " نام کاربری سفارش دهنده", "payment": "م<PERSON><PERSON><PERSON> قابل پرداخت", "payment_document": "سند پرداخت", "payment_date": "تاریخ پرداخت", "registered_name": "نام ثبتی سازمان", "payment_state": "وضعیت پرداخت"}}, "invoice": {"label": {"choicePayment": "انتخاب روش پرداخت", "activeSoon": "به زودی فعال می شود", "paymentBankPortal": "پرداخت از طریق درگاه بانکی", "uploadPaymentDoc": "آپلود سند پرداخت", "choicePaymentDoc": "انتخاب سند پرداخت", "create-invoice": "ثبت درخواست", "show-agreement": "مشاهده توافق‌نامه", "view-invoice-correspondence": "پیش فاکتور فروش سرویس مکاتبات اداری", "view-invoice-meetings": "پیش فاکتور فروش سرویس جلسات اداری", "number-invoice": "شماره پیش فاکتور", "invoice-date": "تاریخ صدور", "valid-date": "این پیش فاکتور از زمان صدور به مدت 3 روز اعتبار دارد.", "customer-name": "نام خریدار", "address": "آدرس", "telephone": "تلفن", "payment": " پرداخت", "agreement-confirmation": "توافق‌نامه را مطالعه کرده ام و می پذیرم.", "agreement": "توافق‌نامه", "organization-business-name": "نام ثبتی سازمان", "business-name": "نام ثبتی", "registeredCode": "شماره ثبتی", "national-id": "شناسه ملی / شماره ثبت", "economic-code": "کد (شماره) اقتصادی", "legalRepresentativeName": "نام و نام خانوادگی نماینده قانونی", "legalRepresentativePosition": "سمت نماینده قانونی", "legal-representative": "نماینده قانونی", "legalRepresentativeNationalId": "شماره (کد) ملی نماینده قانونی", "national-legal-representative": "کد ملی نماینده قانونی", "organization-address": "آدرس سازمان", "postal-code": "ک<PERSON> پستی", "organization-telephone-number": "شماره تلفن سازمان", "legal-Representative-Position": "سمت نمانده قانونی", "download": "د<PERSON><PERSON><PERSON>د", "completeLegalInformation": " تکمیل کردن اطلاعات حقوقی برای انجام تراکنش های مالی الزامی است", "invoicAndLegalInfo": "برای صدور پیش فاکتور و خرید، ابتدا اطلاعات حقوقی سازمان را تکمیل کنید."}, "invoice-table": {"rial": "ریال", "line": "ر<PERSON><PERSON><PERSON>", "total": "جمع کل", "total-discount": "مجموع تخفیف ها", "totalAmountDiscount": "م<PERSON><PERSON>غ کل بعد از اعمال تخفیف‌ها", "tax": "مالیات بر ارزش افزوده", "price": "م<PERSON><PERSON><PERSON> قابل پرداخت", "CorrespondenceStorage": "فضای در اختیار", "CorrespondenceUser": "کاربر مکاتبات", "MeetingsStorage": "فضای در اختیار", "MeetingsUser": "کاربر جلسات", "description": "شرح", "unitInvoice": "واحد", "unitPrice": "فی(ریال)", "quantity": "تعداد", "duration": "دوره زمانی (روز)", "sum": "ج<PERSON><PERSON> مب<PERSON>غ (ریال)", "unit": {"User": "کاربر", "Gigabyte": "گیگابایت"}}, "agreement": {"term_1": "به محض اتمام روز استفاده امکان ارتباط با راهکارها قطع می‌گردد . ولی مالک سازمان به اطلاعات اصلی سازمان دسترسی دارد و کارمندان به اطلاعات کارمندی یا کاربران به اطلاعات کاربری خود دسترسی دارند.", "term_2": "قبل از تمام شدن زمان سرویس سه بار پیام اخطار اتمام ارسال می‌گردد.", "term_3": "ارائه اکسپورت از اطلاعات ماهی یکبار با درخواست کاربر انجام خواهد شد . بیشتر از ماهی یکبار کاربر باید هزینه ای را تحت عنوان دریافت اکسپورت اطلاعات،  بیشتر از یکبار در ماه پرداخت خواهد کرد", "term_4": "قبل از اینکه اطلاعات کاربر از دسترس خارج شود ، سه بار از کانال انتخابی به کاربر اطلاع داده می شود که مبادرت به دریافت اکسپورت یا درخواست دریافت اکسپورت بنماید.", "term_5": "طرح Trial (رایگان) 30 روزه 3 کاربره 1 گیگابایت فضای در اختیار", "term_6": "این طرح را نمی‌توان ارتقاء داد .", "term_7": "طرح رایگان 30 روز است .", "term_8": "درصورتیکه در اواسط دوره طرح رایگان کاربر تصمیم به خرید طرح جدیدی بنماید ، به محض پرداخت ، طرح رایگان لغو خواهد شد", "term_9": "بعد از اتمام سرویس رایگان ، به مدت 60 روز اطلاعات نگهداری می‌شود. در صورتیکه در این زمان طرح جدیدی خریداری شود ، سرویس مورد نظر برای مالک اکتیو می‌شود و هزینه مدت زمان نگهداری اطلاعات گرفته نمیشود. مدت 60 روز به عنوان فرجه تصمیم گیری به کاربر داده می‌شود.", "term_10": "اگر 60 روز از اتمام سرویس رایگان بگذرد و مالک سازمان مبادرت به خرید طرح جدید ننماید، اطلاعات به طور کلی از دسترس مالک خارج خواهد شد. ", "term_11": "در مدت 60 روز فرجه کاربر ملزم به دریافت اکسپورت اطلاعات خود می باشد.", "term_12": "طرح Custom (سفارشی)", "term_13": "در حالت ارتقاء تنها زمان قابل تغییر نیست و امکان افزایش بقیه ریسورس ها وجود دارد.", "term_14": "کاربر مینیمم حجم فضا و تعداد کاربر را به اندازه ریسورس استفاده شده و کاربر فعال میتواند انتخاب کند .", "term_15": "در صورتیکه مالک سازمان طرح جدیدی را خریداری ننماید ، اطلاعات مربوط به راهکارها تا 2 ماه نگهداری می‌شود و کاربر می‌تواند در این زمان اکسپورت از اطلاعات خود را با فرمت XML  (در حال حاضر) بگیرد و باید هنگام گرفتن اکسپورت هزینه نگهداری اطلاعات تا زمان گرفتن اکسپورت را بدهد . ", "term_16": "در غیر اینصورت بعد از 60 روز دسترسی مالک سازمان به قسمت گرفتن اطلاعات قطع می گردد . پس از آن مالک سازمان 30 روز فرصت دارد درخواست گرفتن اطلاعات را داشته باشد که در این صورت فاکتور هزینه نگهداری مدت زمانی که اطلاعات برایش نگهداری شده + هزینه آماده شدن فایل اکسپورت را پرداخت می نماید و فایل را دریافت میکند.", "term_17": "در صورتیکه مالک در فاصله زمانی 90 روز از قطع ارتباط مبادرت به تمدید طرح بنماید ، باید هزینه نگهداری اطلاعات در این فاصله زمانی را به همراه هزینه سرویس جدید پرداخت نماید .", "term_18": "در صورتی که مالک سازمان در فاصله زمانی 90 روز از قطع ارتباط کاربران ، مبادرت به خرید طرح جدید ننماید ، کلیه اطلاعات از دسترس مالک خارج می‌شود و دیگر حتی امکان گرفتن اکسپورت از اطلاعات را ندارد", "term-title-user-agreement": "توافق‌نامه کاربری", "term_19": "اتوماسیون اداری ابری آوات از پیشگامان سیستم‌های ابری در ایران است.", "term_20": "همه شرکت‌ها در شروع کار دغدغه  برخورداری از یک راهکار نرم‌افزاری  را دارند که در کنار تسهیل کارهای اداری و شرکت‌داری و ارائه قابلیت‌های برقراری ارتباط بین پرسنل، به ساده‌ترین شکل ممکن برای سازمان و کاربرانش قابل دسترسی باشد و هزینه تمام‌شده را به حداقل برساند.", "term_21": "«محصول شرکت راهکارهای نوین چارگون با نام آوات» پاسخی به این نیاز است.", "term_22": "از آنجایی ‌که در دنیای مدرنِ امروزی، دسترسی و استفاده از هر نوع محصول یا خدماتی فقط همراه با انعقاد توافق‌نامه امکان‌پذیر است؛ بر این اساس تیم آوات نیز توافق‌نامه‌ای شفاف و روشن را آماده کرده است.", "term_23": "شرایط و مقررات این صفحه توافقاتی است که بین آوات و کاربران محترم منعقد می‌شود. قبل از استفاده از هر گونه خدمات حتماً شرایط مطرح‌ شده در این صفحه را مطالعه کنید.", "term_24": "در ادامه کلمات «فروشنده»، «فروشنده خدمات»، «ما»، «آوات»، «تیم آوات» و «شرکت راهکاران نوین چارگون»اشاره به نرم‌افزار آوات دارد و کلمات «خریدار»، «خریداران خدمات»،«سازمان»، «شما» و «کاربر» اشاره به کاربر دارد که می‌تواند هر شخص حقیقی یا حقوقی باشد که از خدمات آوات استفاده می‌کند.", "term_25": "همچنین «خدمات»، «نرم‌افزار»، «محصولات»، «امکانات»، «برنامه»، «اپ یا اپلیکیشن»، «سرویس‌ها»، «ابزار»، «وب‌سایت»، «بلاگ آوات»، «بلاگ»، و موارد مشابه اشاره به خدمات آوات به ‌صورت کلی دارد.", "term-title-agreement-subject": "موضوع توافق‌نامه", "term_26": "این توافق‌نامه شامل شرایط پذیرش، شرایط عمومی استفاده از خدمات آوات، کاربران مجاز، تعهدات و قوانین حاکم بر خدمات، تعهدات آوات و قابلیت‌های آوات است.", "term-title-acceptance-of-agreement": "پذیرش توافق‌نامه", "term_27": "کاربرتا<PERSON><PERSON>د می‌کند که هرگونه استفاده از خدمات آوات بدین معناست که کاربر این توافق‌نامه را مطالعه کرده و با مفاد آن موافق است.", "term_28": "تیم آوات ممکن است اصلاحاتی در مفاد و شرایط توافق‌نامه اعمال کند که این اصلاحات به صورت به روز شده در همین صفحه گذاشته می‌شود. کاربر تایید می‌نماید که با اصلاحات جدید و آخرین نسخه توافق‌نامه موافق است.", "term_29": "آوات متعهد می‌شود تمامی تغییرات توافق‌نامه را در همین سند در سایت آوات به اطلاع کاربران برساند و هرگونه اطلاع‌رسانی غیرکتبی فاقد رسمیت است.", "term_30": "با دسترسی به امکانات آوات چه به صورت مستقیم (کاربری نرم‌افزاری یا اپلیکیشن‌) چه به صورت غیرمستقیم (اشتراک‌گذاری محتواهای آوات در شبکه‌های اطلاع‌رسانی)، تیم آوات و کاربران در چارچوب این توافق‌نامه متقابلاً به هم متعهد می‌شوند.", "term-title-awat-product": "محصول آوات", "term_31": "محصول آوات شامل نرم‌افزارهای متعدد با زیرساخت ابری، اپلیکیشن‌ها، ابزارها و رسانه‌های مربوط به خدمات آوات است. این محصول متعلق به شرکت راهکارهای نوین چارگون است. آوات خدمات خود را برای ارزش‌هایی مانند کمک به مدیر کسب‌و‌کار یا کارآفرین در مدیریت شرکت، انجام امور مربوط به شرکتداری و انجام مراودات اداری بر اساس نرم‌افزار خریداری شده و درنهایت داشتن تجربه بهتری از کار اداری، ایجاد گردیده است .کاربر متعهد می‌شود از آوات  تنها برای این خدمات یا موارد مشابه استفاده کند.", "term-title-awat-audience": "مخا<PERSON>ب آوات", "term_32": "تمامی خدمات آوات  تابع قوانین جمهوری اسلامی ایران است. هرچند امکانات ارائه‌ شده در آوات در سراسر جهان کاربرد خواهد داشت، اما مخاطب آوات افرادی هستند که تحت قوانین جمهوری اسلامی ایران در حوزه اداری به فعالیت می‌پردازند. این تعریف شامل افرادی که به صورت قانونی تابع قوانین جمهوری اسلامی ایران هستند نیز می‌شود.", "term-title-limitation-of-liability": "تحدید مسئولیت", "term_33": "آوات  هیچ‌گونه مسئولیتی بابت عواقب تصمیم‌های کاربران ندارد. فعالیت‌هایی که کاربر با هرگونه برداشت مستقیم و غیرمستقیم بر اساس محتوای متنی، تصویری، فیلم، گرافیک، بلاگ، شبکه‌های مجازی یا هر گونه محتوای مرتبط با آوات  انجام دهد متوجه آوات  نیست. مسئولیت تصمیم‌های کاربر به هر روشی اعم از تأثیر مستقیم از آوات  یا غیرمستقیم از طریق پیشنهاد دهنده یا شخص ثالث به نقل‌قول از آوات، متوجه خود کاربر یا شخص ثالث است و در هیچ شکلی مسئولیتی بر عهده آوات نیست.", "term-title-use-of-content": "استفاده از محتوا", "term_34": "گستره‌ای از اطلاعات، در قالب «محتوا» در نرم‌افزارهای آوات تحت عنوان فایل‌های آموزشی، سایت و بلاگ ارائه ‌شده است. هدف محتوا در آوات کمک به سهولت تصمیم‌گیری مدیران کسب‌وکار و آموزش محصول و نرم‌افزارهای آوات است. در حالیکه آوات، مجدانه تلاش می‌کند محتوای دقیق، به‌روز و جامعی را فراهم کند، نمی‌تواند ضمانت یا مسئولیت دقت، کامل بودن و به‌روز بودن محتوا را بپذیرد. به همین واسطه در کلیه موارد پیشنهاد می‌شود محتوای آوات جهت راهنما برای استفاده از نرم‌افزارها استفاده شود و در صورت ابهام با پشتیبانی آوات تماس گرفته شود.", "term_35": "در صورت استفاده نادرست از هر گونه محتوا و رسانه، عواقب این امر به عهده فرد، افراد یا شخص حقوقی سوءاستفاده کننده قرار دارد. آوات هیچ‌گونه مسئولیتی در قبال استفاده نادرست از نرم‌افزارهای آوات و همچنین در قبال بازنشر محتوا توسط دیگران به صورت غیرقانونی و بدون مجوز کتبی از شرکت راهکار‌های نوین چارگون ندارد.", "term-title-account-creation-rules": "قوانین ایجاد حساب کاربری", "term_36": "با هر شماره موبایل، تنها می‌توان یک حساب کاربری ایجاد کرد.", "term_37": "نام کاربری یک مقدار یکه (واح<PERSON>) است و نمی‌توان یک نام کاربری را برای چند حساب کاربری استفاده کرد.", "term_38": "پس از ایجاد حساب کاربری، امکان تغییر نام کاربری و شماره موبایل وجود ندارد.", "term_39": "هر حساب کاربری، باید تنها توسط صاحب همان حساب استفاده شود.", "term_40": "هر کاربر مسئول نگهداری از نام کاربری و رمز عبور خود است و نباید این اطلاعات را در اختیار دیگران قرار دهد. همچنین هر کاربر موظف است تا این اطلاعات را به صورتی امن، تعریف و نگهداری کند تا در دسترس دیگران نباشد.", "term-title-organization-creation-rules": "قوانین ایجاد سازمان", "term_41": "مالک سازمان، کاربری است که سازمان خود را در«آوات» ایجاد می‌کند و مسئولیت تمام فعالیت‌های انجام شده در سازمان را بر عهده می‌گیرد.", "term_42": "استفاده از نام سازمان‌های خصوصی و غیر خصوصی و برندهای تجاری ثبت شده و رسمی کشور به عنوان «نام سازمان» و «شناسه سازمان» فقط برای مالکین یا نماینده قانونی آن‌ها مجاز است.", "term-title-buy-a-subscription": "<PERSON><PERSON><PERSON><PERSON> اشتراک", "term_43": "در حال حاضر، خرید اشتراک تنها شیوۀ خرید راهکار در «آوات» است. خریدار بر اساس طرح انتخابی، مبلغ آن را به صورت کامل پرداخت می‌کند و پس از آن، راهکار مربوطه در سازمان فعال می‌شود.", "term_44": "پس از فعال شدن راهکار برای سازمان، تمامی امکانات آن راهکار، بر اساس طرح انتخابی، برای خریدار فعال می‌شود.", "term_45": "پس از نهایی شدن خرید، اشتراک مربوطه در سازمان فعال می‌شود و امکان اعمال تغییر در خرید، انصراف از خرید، حذف یا تغییر در طرح وجود ندارد.", "term_46": "اشتراک خریداری شده، پس از پایان دوره زمانی آن منقضی می‌شود.", "term_47": "پس از منقضی شدن اشتراک، دسترسی خریدار به راهکار مربوطه و اطلاعات ایجاد شده در آن غیرفعال خواهد شد.", "term_48": "در صورتیکه خریدار در طول مدت اعتبار اشتراک، درخواست اضافه کردن تعداد کاربر یا فضای در اختیار را داشت، از «طرح ارتقاء سرویس» بر اساس نرخ روز استفاده خواهد کرد.", "term_49": "در «طرح ارتقاء سرویس» امکان تغییر مدت زمان طرح اولیه که ارتقاء روی آن انجام گرفته، وجود ندارد.", "term_50": "محاسبه هزینه در «طرح ارتقاء سرویس» بر اساس تعداد کاربر یا فضای در اختیار انتخابی خریدار و تعداد روزهای باقیمانده از طرح اولیه، انجام می‌شود.", "term-title-free-plan-activation": "فعال‌سازی طرح رایگان", "term_51": "در «طرح رایگان» می‌توان بدون پرداخت هزینه از امکانات یک راهکار استفاده کرد.", "term_52": "«طرح رایگان» شامل یک دوره زمانی محدود (به عنوان مثال 14 روز) و تعداد کاربران و فضای در اختیار ثابت است.", "term_53": "در هر سازمان و برای هر راهکار، فقط یک بار می‌توان «طرح رایگان» را فعال کرد.", "term_54": "«طرح رایگان» را نمی‌توان تمدید کرد و یا ارتقا داد.", "term_55": "«طرح رایگان» یک راهکار در شرایط زیر منقضی می‌شود: 1. به پایان رسیدن دوره زمانی آن 2.خرید اشتراک برای همان راهکار", "term_56": "در صورت به پایان رسیدن دوره زمانی «طرح رایگان» دسترسی سازمان به آن راهکار و اطلاعات ایجاد شده در آن غیرفعال خواهد شد؛ این دسترسی، با «خرید اشتراک» مجددا قابل فعال شدن است.", "term-title-awat-obligations": "تعهدات آوات", "term_57": "آوات کلیه اطلاعات و داده‌های مشتریان را محرمانه تلقی می‌کند و از افشای آن‌ها بدون موافقت خریدار و به هر شکلی خودداری خواهد کرد.", "term_58": "آوات تمام تلاش خود را می‌کند که خدمات خود را به صورت بی‌وقفه، به موقع و بدون خطا ارائه کند و از روش‌های حفاظتی و امنیتی کارا برای ایجاد امنیت در سامانه‌های سخت‌افزاری و نرم‌افزاری و روش‌های ارتباطی خود استفاده کند.", "term_59": "امکانات «آوات» به صورت مداوم به روزرسانی می‌شود و امکان سفارشی‌سازی را ندارد.", "term_60": "«آوات» تابع کلیه قوانین جاری کشور است؛ تمام کاربران موظف به رعایت این قوانین هستند و انتشار هر نوع داده مغایر با عرف جامعه ممنوع بوده و در صورت مشاهده «آوات» مجاز به حذف اطلاعات و در موارد خاص و در صورت درخواست سازمان‌های بالادستی مجاز به قطع خدمات ارائه شده می‌باشد و مسئولیت‌های حقوقی و کیفری ناشی از آن به عهده خریدار است.", "term_61": "در صورت نیاز به توقف موقت خدمات به منظور نگه‌داری و یا بروزرسانی زیرساخت‌های سخت‌افزاری یا نرم‌افزاری «آوات» متعهد می‌شود برای انجام این امر، مراتب را از یک روز قبل به خریداران اطلاع‌رسانی کند.", "term_62": "راهکارهای «آوات» به صورت کاربردی آماده شده است و استفاده از آن سهل و راحت است. همچنین دارای بخش راهنمای کاربری است و تیم پشتیبانی «آوات» از طریق ارتباط متنی درون برنامه‌ای به کاربران خدمات رسانی دارد.", "term_63": "از آن‌جایی که ممکن است مفاد این توافق‌نامه در هر زمان از سوی «آوات» مورد بازبینی و به‌روز‌رسانی قرار گیرد، کاربر باید همواره با مراجعه به این توافق‌نامه از به‌روز‌رسانی و تغییرات احتمالی آن آگاه شود. ", "term_64": "اگر چه عدم مراجعه کاربر به این سند، مانع از اجرای مفاد آن نخواهد بود و این سند بلافاصله پس از هرگونه تغییر، به اجرا گذاشته خواهد شد. لازم به ذکر است بازبینی، اصلاح یا به روزرسانی جزئی یا کلی مفاد توافق‌نامه به اطلاع کاربران خواهد رسید. این اطلاع‌رسانی می‌تواند از طریق ارسال ایمیل یا به شکل اعلان در وبسایت یا هنگام ورود به سیستم باشد.", "term_65": "در شرایط زیر، مسئولیتی بر عهده «آوات» نخواهد بود:", "term_66": "اشكالات ناشی از شبكه و سیستم‌های كامپيوتری و نرم‌افزاری دیگری که خریدار از آن‌ها استفاده می‌کند.", "term_67": "تبعات ناشی از استفاده اشتباه از امکانات عرضه شده در این محصول، توسط خریدار.", "term_68": "اشکالات ناشی از زیرساخت‌های ارتباطی کشور یا شرایط فورس ماژور.", "term-title-wat-support": "پشتیبانی آوات", "term_69": "امکان ارتباط با همکاران پشتیبانی آوات وجود دارد. همکاران ما در ساعات کاری 8.30 الی 17 همیشه پاسخگو هستند. تلاش می‌گردد این پاسخگویی در ساعت‌های غیر کاری نیز انجام شود.", "term_70": "چنانچه درباره هر کدام از موارد این توافق‌نامه، امکانات آوات  شامل قابلیت نرم‌افزاری، گزارش باگ نرم‌افزاری، مشاهده مشکل در هر بخشی از آوات ، پیشنهاد یا انتقادی برای محتوا، قابلیت‌ها و سرویس‌های آوات داشتید از طریق اطلاع به پشتیبانی موضوع را بیان کنید. درخواست می‌گردد موارد شفاف مطرح شود تا جوابگویی متناسب با مسئله از سوی تیم پشتیبانی ارائه شود. تیم آوات، نکات، انتقادها و پیشنهادهای شما را به دقت بررسی خواهد نمود و در صورتی که لازم بداند تصمیم و اقدام لازم را اتخاذ خواهد کرد.", "term-title-the-right-of-moral-possess": "حقوق مالکیت معنوی", "term_71": "مالکیت معنوی تمامی اطلاعات ایجاد شده و فایل‌های بارگذاری شده در سازمان و راهکارها، متعلق به سازمان است.", "term_72": "مالکیت انحصاری و تمام حقوق مادی و معنوی «آوات» تحت هر عنوان و به هر شکل برای شرکت راهکارهای نوین چارگون محفوظ است و بهره برداری از آن خارج از این توافق‌نامه، به هر صورت و با هر قصد و نیتی، ممنوع است. در صورت هر گونه بهره‌برداری غیر مجاز، حق شرکت راهکارهای نوین چارگون بر اقامۀ دعاوی قانونی مرتبط و پیگیری حقوق خود از مجاری قانونی محفوظ خواهد بود.", "term_73": "طراحی نرم‌افزار، رابط کاربری و طراحی تجربه کاربری و تمام محتوای ارائه‌ شده در آوات  توسط شرکت راهکارهای نوین چارگون (سهامی خاص) تهیه، تولید و مجوز نشر آن از طریق این سایت اخذ گردیده است. کلیه حقوق آن متعلق به این شرکت است. هرگونه کپی‌برداری و استفاده کلی و جزئی، به هر نحوی از محتوا، طراحی ظاهری، ساختاری یا قابلیت‌های سایت غیرقانونی است. "}}, "terms": {"labels": {"registerTerms": "قوانین ثبت نام", "rulesReaded": "قوانین را مطالعه کردم", "navigateAwatTermsPage": "صفحه قوانین و شرایط استفاده از آوات", "termsPages": "صف<PERSON>ه قوانین و شرایط", "latestChanges": "جدید<PERSON>رین تغییرات", "showLatestChanges": " مشاهده تاریخچه تغییرات", "historyChange": "تاریخچه تغییرات محصول"}}, "validation": {"string_min": "مقدار وارده از حد مجاز کمتر است", "string_max": "مقدار وارده از حد مجاز بیشتر است", "string_lowercase": "همه حروف باید کوچک باشند", "string_pattern_base": "از حروف و اعداد انگلیسی و '_' می‌توانید استفاده کنید", "string_alphanum": "از حروف انگلیسی و اعداد استفاده کنید", "string_base": "داده وارده باید string باشد", "string_empty": "پر کردن این فیلد اجباری‌ست", "any_required": "پر کردن این فیلد اجباری‌ست", "any_empty": "پر کردن این فیلد اجباری‌ست", "string_email": "فرمت ایمیل اشتباه است"}, "cartables": {"labels": {"allLetters": "همه نامه‌ها", "activeLetters": "نامه‌های جاری", "closeLetters": "بسته شده", "notExchanged": "به گردش در نیامده", "forwardedToMe": "ارجاع به من"}}, "error": {"com-chargoon-cloud-svc-orders": {"orders": {"cancelOrder": {"OrderStatusNotCancellableException": "سفار<PERSON> قبلی شما قابل انصراف نمی باشد"}}}, "com-chargoon-cloud-svc-organizations": {"organizations": {"inviteEmployeeByUsername": {"DuplicateEmployeeCodeException": "کد تکراری", "DuplicateEmployeeException": "کاربر تکراری", "UnauthorizedException": "عدم دسترسی"}, "inviteEmployeeByMobile": {"DuplicateEmployeeException": "کاربر تکراری", "DuplicateEmployeeCodeException": "کد تکراری", "UnauthorizedException": "عدم دسترسی"}, "getUserOrganizations": {"UnauthorizedException": "عدم دسترسی به سازمان های عضو"}, "createOrganization": {"UnauthorizedException": "عدم دسترسی به ایجاد سازمان", "DuplicateIdException": "سازمان تکراری"}, "updateOrganizationProfile": {"UnauthorizedException": "عدم دسترسی به آپدیت سازمان"}, "updateEmployeeStatus": {"UnauthorizedException": "عدم دسترسی به آپدیت وضعیت"}, "updateEmployeeCode": {"DuplicateEmployeeCodeException": "کد تکراری", "UnauthorizedException": "عدم دسترسی"}, "getOwnedOrganizations": {"UnauthorizedException": "عدم دسترسی سازمان های من"}, "getOrganizationEmployees": {"UnauthorizedException": "عدم دسترسی"}, "getEmployeeProfile": {"UnauthorizedException": "عدم دسترسی"}, "getEmployee": {"UnauthorizedException": "عدم دسترسی", "EmployeeNotFoundException": "کارمند یافت نگردید", "UserDataException": "user data", "positionsDataException": "position data"}, "updateEmployeeProfile": {"UnauthorizedException": "عدم دسترسی"}, "getOrganizations": {"GetOrganizationsDataException": "GetOrganizationsData", "GetOrganizationDataException": "GetOrganizationDataException", "UnauthorizedException": "عدم دسترسی", "UserDataException": "user data"}, "searchEmployeeFullNames": {"UnexpectedUsersData": "UnexpectedUsersData", "SearchingOperationFailed": "SearchingOperationFailed"}}, "positions": {"getPositionByCode": {"UnauthorizedException": "عدم دسترسی"}, "getEmployeePositions": {"UnauthorizedException": "عدم دسترسی"}, "getPositionEmployees": {"GetPositionEmployeesDataException": "خطا", "UnauthorizedException": "عدم دسترسی"}, "getOrganizationPositions": {"UnauthorizedException": "عدم دسترسی", "GetOrganizationPositionsDataException": "خطا"}, "getPosition": {"PositionsDataException": "خطا", "UnauthorizedException": "عدم دسترسی", "ParentPositionDataException": "خطا در سمت بالا دستی"}, "getUserAllPositions": {"UnauthorizedException": "عدم دسترسی", "PositionsDataException": "خطا"}, "createPosition": {"DuplicateIdException": "سمت تکراری", "UnauthorizedException": "عدم دسترسی", "ApprovePositionException": "تا<PERSON>ید نشده", "RefusePositionException": "ر<PERSON> ش<PERSON>ه", "DuplicatePositionCodeException": "کد سمت تکراری می‌باشد", "UnexpectedErrorException": "Unexpected Error Exception"}, "updatePosition": {"UnauthorizedException": "عدم دسترسی", "CapacityException": "خطا در ظرفیت"}, "updatePositionCode": {"UnauthorizedException": "عدم دسترسی", "ApproveUpdatePositionCodeException": "کد تایید نشده", "ResetPositionCodeException": "خطا در حذف کد", "DuplicatePositionCodeException": "کد سمت تکراری می‌باشد"}, "updatePositionStatus": {"UnauthorizedException": "عدم دسترسی", "UnknownPositionStatusException": "وضعیت ناشناخته", "UnknownPositionStatusDataException": "Unknown Position Status Data Exception", "DuplicatePositionStatusException": "Duplicate Position Status Exception", "PositionHasAlreadyEmployeesException": "Position Has Already Employees Exception", "ApproveUpdatePositionStatusException": "Approve Update Position Status Exception", "ResetPositionStatusException": "Reset Position Status Exception"}, "assignPosition": {"UnauthorizedException": "عدم دسترسی", "PositionHasAlreadyBeenAssignedException": "این سمت از قبل اختصاص داده شده است", "CapacityIsAlreadyFullException": "ظر<PERSON><PERSON>ت تکمیل است", "ForbiddenSlotAssignment": "خطا در انتصاب جایگاه"}, "revokePosition": {"UnauthorizedException": "عدم دسترسی", "PositionHasNotBeenAssignedBeforeException": "این سمت اختصاص داده نشده بود", "UserHasACriticalRoleException": "این کارمند دارای نقش می باشد، نمی توانید تمام سمت های این کارمند را بگیرید", "TypeError": ""}, "releaseSlot": {"UnauthorizedException": "عدم دسترسی", "PositionHasSubsetException": "این جایگاه زیرمجموعه رزرو شده دارد", "PositionHasAnEmployeeException": "این جایگاه دارای کارمند می‌باشد", "SlotHasActiveConsumersException": "کارتابل مربوط به این جایگاه، دارای نامه «جاری» است"}, "updatePositionParent": {"UnauthorizedException": "عدم دسترسی", "ParentOfTheRootException": "Parent Of The Root Exception"}, "setPrimaryPosition": {"UnauthorizedException": "عدم دسترسی", "OrganizationNotFoundException": "سازمان پیدا نشد"}}, "projects": {"UpdateProjectMembersFailed": {"ProjectMemberUpdateFailedException": "حذف کاربر موفقیت‌آمیز نبود"}}}, "com-chargoon-cloud-svc-users": {"users": {"createUser": {"BadPasswordException": "رمز عبور قابل  قبول نیست", "DuplicateIdException": "ای دی کاربر تکراری", "DuplicateUsernameException": "کاربر تکراری", "PasswordLengthException": "خطا در طول رمز عبور", "LowercaseException": "خطا در فرمت رمز عبور", "UppercaseException": "خطا در فرمت رمز عبور", "DigitsException": "خطا در فرمت رمز عبور", "SpecialCharactersException": "خطا در فرمت رمز عبور"}, "CreateUserInitialized": {"DuplicateMobileException": "شماره موبایل تکراری است"}, "changePassword": {"UnauthorizedException": "عدم دسترسی", "UsernameOrOldPasswordException": "رمز عبور فعلی نامعتبر", "MobileException": "خطا در شماره موبایل ارسال شده", "UsernameOrMobileIsNotSpecific": "Username Or Mobile Is Not Specific", "SamePasswordException": "رمز عبور فعلی و جدید یکسان است"}, "updateUserProfile": {"UnauthorizedException": "عدم دسترسی"}, "updateUserSignature": {"UnauthorizedException": "عدم دسترسی"}, "findOneByUsername": {"NotFoundException": "کاربر پیدا نشد"}, "findOneByMobile": {"NotFoundException": "کاربر پیدا نشد"}, "getUserByEmail": {"NotFoundException": "کاربر پیدا نشد"}, "getUserProfile": {"UnauthorizedException": "عدم دسترسی"}, "searchUserFullNames": {"FullNameLengthException": "Full Name Length Exception", "NameMustBeSpecificException": "Name Must Be Specific Exception"}}}, "cartables": {"cartables": {"com-chargoon-cloud-svc-letters": {"CannotRevokeSolution": "این کاربر راهبر مکاتبات می‌باشد و نمی‌تواند از کاربران مکاتبات حذف شود", "CannotGrantSolution": "افزودن کاربر به مکاتبات با خطا مواجه شد"}}}, "com-chargoon-cloud-svc-meetings": {"meeting-cartables": {"com-chargoon-cloud-svc-meetings": {"CannotRevokeSolution": "این کاربر راهبر جلسات می‌باشد و نمی‌تواند از کاربران جلسات حذف شود"}}}, "com-chargoon-cloud-svc-letters": {"letters": {"getLettersNotExchanged": {"UnauthorizedException": "عدم دسترسی", "LettersNotExchangedDataException": "Letters Not Exchanged Data Exception", "DecorateLetterException": "Decorate Letter Exception"}, "searchOwnedLetters": {"UnauthorizedException": "عدم دسترسی", "OrganizationDataException": "OrganizationDataException", "OrganizationNotFound": "OrganizationNotFound", "SearchEmployeeFullNamesDataException": "SearchEmployeeFullNamesDataException", "SearchContactNamesDataException": "SearchContactNamesDataException"}, "searchLetters": {"UnauthorizedException": "عدم دسترسی"}, "getLetter": {"UnauthorizedException": "عدم دسترسی", "GetLetterDataException": "GetLetterDataException", "LetterNotFoundException": "LetterNotFoundException", "DecorateLetterException": "DecorateLetterException"}, "getAssignedUnsignedLetters": {"UnauthorizedException": "عدم دسترسی"}, "exportLetter": {"GetLetterDataException": "GetLetterDataException", "UnauthorizedException": "UnauthorizedException", "LayoutDataException": "LayoutDataException", "SignatureDataException": "SignatureDataException"}, "exportEce": {"LetterDataException": "LetterDataException", "UnauthorizedException": "UnauthorizedException", "InvalidLetterTypeException": "InvalidLetterTypeException", "LetterHasNotBeenNumberedException": "LetterHasNotBeenNumberedException", "LetterHasNotBeenSignedException": "LetterHasNotBeenSignedException", "ExportLetterDataException": "ExportLetterDataException", "AttachmentsDataException": "AttachmentsDataException", "OrganizationDataException": "OrganizationDataException"}, "parseEce": {"NoLetterNumber": "این نامه ECE شماره نامه ندارد.", "ProcessFilesAndDocumentsServiceException": "ProcessFilesAndDocumentsServiceException", "BadEceStructureException": "این فایل xml نمی باشد", "ParseXmlException": "فایل xml مورد نظر ece نمی باشد", "NoXmlAttached": "", "RecipientNotFound": "که به عنوان گیرنده ذکر شده، در لیست کارمندان شما وجود ندارد.", "SenderNotFound": "که به عنوان فرستنده ذکر شده، در لیست مخاطبان سازمان شما وجود ندارد.", "UnauthorizedException": "UnauthorizedException", "LetterHasNotBeenNumberedException": "LetterHasNotBeenNumberedException", "SearchPositionsDataException": "SearchPositionsDataException", "GetPositionsDataException": "GetPositionsDataException", "SearchUserFullNamesDataException": "SearchUserFullNamesDataException", "SearchEmployeeFullNamesDataException": "SearchEmployeeFullNamesDataException", "GetUsersDataException": "GetUsersDataException", "SearchContactNameDataException": "SearchContactNameDataException"}, "getNumberedLettersByIndicator": {"GetIndicatorDataException": "GetIndicatorDataException", "UnauthorizedException": "UnauthorizedException", "GetNumberedLettersByIndicatorDataException": "GetNumberedLettersByIndicatorDataException", "DecorateLetterException": "DecorateLetterException"}, "getMyCartableLetters": {"UnauthorizedException": "عدم دسترسی", "GetMyCartableLettersDataException": "GetMyCartableLettersDataException", "GetLettersDataException": "GetLettersDataException"}, "getLettersForwardedByMe": {"UnauthorizedException": "عدم دسترسی", "GetLettersForwardedByMeDataException": "GetLettersForwardedByMeDataException", "GetLettersDataException": "GetLettersDataException"}, "getLettersForwardedToMe": {"UnauthorizedException": "عدم دسترسی", "GetLettersForwardedToMeException": "GetLettersForwardedToMeException", "GetLettersDataException": "GetLettersDataException"}, "getEndedLetters": {"UnauthorizedException": "عدم دسترسی", "GetEndedLettersDataException": "GetEndedLettersDataException"}, "getLetterReceipts": {"GetLetterDataException": "GetLetterDataException", "UnauthorizedException": "عدم دسترسی"}, "getReceipt": {"DecorateReceiptException": "DecorateReceiptException", "GetLetterDataException": "GetLetterDataException", "UnauthorizedException": "UnauthorizedException"}, "getLetters": {"GetLettersDataException": "GetLettersDataException", "LettersNotFoundException": "LettersNotFoundException"}, "exportNumberedLettersByIndicator": {"GetNumberedLettersByIndicatorException": "GetNumberedLettersByIndicatorException"}, "getEmployeeReceiptedLetters": {"UnauthorizedException": "عدم دسترسی", "GetEmployeeReceiptedLettersException": "GetEmployeeReceiptedLettersException", "DecorateLetterException": "DecorateLetterException"}, "composeLetter": {"UnauthorizedException": "UnauthorizedException", "FinalizeComposeLetterException": "FinalizeComposeLetterException"}, "updateLetterStatus": {"UpdateLetterStatusException": "UpdateLetterStatusException"}, "updateLetter": {"UnauthorizedException": "عدم دسترسی", "NumberedLetterIsNotModifiableException": "NumberedLetterIsNotModifiableException", "SignedLetterIsNotModifiableException": "SignedLetterIsNotModifiableException"}, "signLetter": {"UnauthorizedException": "UnauthorizedException", "NotInTheSignersListException": "NotInTheSignersListException", "AlreadySignedException": "نامه از قبل امضا شده است", "LetterIsNotInTheSignersCartableException": "این نامه در کارتابل شما وجود ندارد"}, "setLetterNumber": {"UnauthorizedException": "عدم دسترسی", "LetterIsAlreadyNumberedException": "LetterIsAlreadyNumberedException", "SetLetterNumberException": "SetLetterNumberException"}, "forwardLetter": {"UnauthorizedException": "عدم دسترسی", "WrongParentIdException": "WrongParentIdException", "SelectLastOperationAsParentIdException": "نسخه جدیدتر این نامه در کارتابل شما موجود است", "UnnumberedLettersCanOnlyBeSentToOnePersonException": "UnnumberedLettersCanOnlyBeSentToOnePersonException", "FinalizeForwardLetterException": "FinalizeForwardLetterException"}, "terminateLetter": {"UnauthorizedException": "عدم دسترسی", "LetterIsNotNumberedException": "LetterIsNotNumberedException", "LetterIsAlreadyTerminatedException": "LetterIsAlreadyTerminatedException", "WrongParentIdException": "WrongParentIdException", "LetterIsNotInTheTerminatorsCartableException": "LetterIsNotInTheTerminatorsCartableException", "FinalizeTerminateLetterException": "FinalizeTerminateLetterException"}, "discardLetter": {"CannotDiscardALetterWhichIsNumberedException": "CannotDiscardALetterWhichIsNumberedException", "AlreadyDiscardedException": "AlreadyDiscardedException", "LetterIsNotInTheRequestersCartableException": "LetterIsNotInTheRequestersCartableException", "UserNotASignerException": "UserNotASignerException", "FinalizeDiscardLetterException": "FinalizeDiscardLetterException"}, "addReceipt": {"UnauthorizedException": "عدم دسترسی", "LetterIsNotNumberedException": "نامه شماره نشده است", "LetterTypeException": "LetterTypeException"}, "editReceipt": {"ReceiptNotFoundException": "ReceiptNotFoundException", "UnauthorizedException": "UnauthorizedException"}, "revertForwardLetter": {"LetterHasBeenReadByRecipient": "این ارجاع توسط گیرنده ارجاع باز شده است و قابل بازگشت نیست."}, "voidLetter": {"LetterHasReceiptException": "برای این نامه رسید ارسال ثبت شده است.", "LetterAlreadyVoidedException": "این نامه از قبل باطل شده است.", "LetterIsVoidException": "این نامه در حال انجام عملیات (بروزرسانی، چاپ، ای‌سی‌ای، امضا، شماره کردن، ارجاع، بازگشت ارجاع، اختتام، ثبت رسید، لغو) می باشد."}}}, "com-chargoon-cloud-svc-contacts": {"contact-groups": {"getOrganizationContactGroups": {"UnauthorizedException": "عدم دسترسی"}, "searchContactGroups": {"UnauthorizedException": "عدم دسترسی"}, "createContactGroup": {"UnauthorizedException": "عدم دسترسی", "DuplicateIdException": "کد تکراری"}, "updateContactGroup": {"UnauthorizedException": "عدم دسترسی"}, "changeContactGroupStatus": {"UnauthorizedException": "عدم دسترسی"}}, "contacts": {"getContactGroupContacts": {"UnauthorizedException": "عدم دسترسی"}, "searchContacts": {"UnauthorizedException": "عدم دسترسی", "SearchContactsDataException": "SearchContactsDataException"}, "getOrganizationContacts": {"UnauthorizedException": "عدم دسترسی"}, "getContact": {"UnauthorizedException": "عدم دسترسی", "GetContactDataException": "GetContactDataException"}, "createContact": {"UnauthorizedException": "عدم دسترسی", "DuplicateContactIdException": "DuplicateContactIdException"}, "updateContactProfile": {"UnauthorizedException": "عدم دسترسی"}, "changeContactStatus": {"UnauthorizedException": "عدم دسترسی"}, "changeContactGroup": {"UnauthorizedException": "عدم دسترسی"}}}, "com-chargoon-cloud-svc-secretariats": {"indicators": {"getSecretariatIndicators": {"GetSecretariatDataException": "GetSecretariatDataException", "UnauthorizedException": "عدم دسترسی"}, "getIndicator": {"IndicatorNotFoundException": "IndicatorNotFoundException", "UnauthorizedException": "عدم دسترسی"}, "createIndicator": {"UnauthorizedException": "عدم دسترسی", "DuplicateIndicatorIdException:": "شناسه اندیکاتور تکراری", "ApproveIndicatorException": "ApproveIndicatorException", "RefuseIndicatorException": "RefuseIndicatorException", "DuplicateIndicatorCodeException": "کد اندیکاتور تکراری", "UnexpectedErrorException": "Unexpected Error Exception", "FirstIndexShouldNotBeGreaterThanLastIndexException": "آخرین شماره مسلسل باید بزرگتر از اولین شماره مسلسل باشد"}, "updateIndicatorStatus": {"UnauthorizedException": "عدم دسترسی"}, "updateIndicator": {"UnauthorizedException": "UnauthorizedException"}, "reserveSerialNumber": {"SerialNumberHasBeenAlreadyReservedException": "SerialNumberHasBeenAlreadyReservedException", "SerialNumberIsAlreadyBookedException": "SerialNumberIsAlreadyBookedException"}}, "secretariats": {"forceFetchEceMails": {"RepetitiousRefreshRequest": "بروزرسانی هر 5 دقیقه یکبار مجاز است "}, "validateEceMailConfig": {"validateIncomingEceMailConfigServerFailed": "اتصال برای دریافت ECE با خطا مواجه شد.", "validateOutgoingEceMailConfigServerFailed": "اتصال برای ارسال ECE با خطا مواجه شد."}, "getOrganizationSecretariats": {"UnauthorizedException": "عدم دسترسی"}, "getSecretariatLetterLayouts": {"SecretariatNotFoundException": "SecretariatNotFoundException", "UnauthorizedException": "عدم دسترسی"}, "createSecretariat": {"UnauthorizedException": "عدم دسترسی", "DuplicateSecretariatIdException": "شناسه دبیرخانه تکراری", "ApproveSecretariatException": "ApproveSecretariatException", "RefuseSecretariatException": "RefuseSecretariatException", "DuplicateSecretariatCodeException": "کد دبیرخانه تکراری", "UnexpectedErrorException": "Unexpected Error Exception"}, "updateSecretariat": {"UnauthorizedException": "عدم دسترسی", "DuplicateSecretariatCodeException": "کد دبیرخانه تکراری می باشد"}, "createLetterLayout": {"UnauthorizedException": "عدم دسترسی", "Error": "ایجاد قالب چاپی با خطا مواجه شد"}, "updateLetterLayout": {"UnauthorizedException": "عدم دسترسی"}}, "ecemails": {"getSecretariatEceMails": {"EceConfigNotFoundException": "لطفا تنظیمات مربوط به کانفیگ ECE را انجام دهید"}, "getMailboxEceMails": {"TypeError": "لطفا تنظیمات مربوط به کانفیگ ECE را انجام دهید"}, "voidLetter": {"LetterExposedByEceException": "این نامه از طریق ECE ارسال شده است."}}}, "com-chargoon-cloud-svc-invoices": {"invoices": {"getClearableInvoice": {"InvoiceNotFoundException": "سفارش شما لغو شده است"}}}}, "commands": {"administration": {"tariffAssigned": "تخصیص تعرفه"}, "cartables": {"SolutionGranted": "اختصاص راهکار مکاتبات", "GrantSolutionFailed": "اختصاص راهکار مکاتبات", "SolutionAccessRevoked": "سلب دسترسی راهبکار مکاتبات", "RevokeSolutionAccessFailed": "سلب دسترسی راهبکار مکاتبات"}, "contactGroups": {"ContactGroupCreated": "ایجاد گروه مخاطب", "CreateContactGroupFailed": "ایجاد گروه مخاطب", "ContactGroupUpdated": "ویرایش گروه مخاطب", "UpdateContactGroupFailed": "ویرایش گروه مخاطب", "ContactGroupStatusChanged": "تغییر وضعیت گروه مخاطب", "ChangeContactGroupStatusFailed": "تغییر وضعیت گروه مخاطب"}, "contacts": {"ContactCreated": "ایج<PERSON> مخاطب", "CreateContactFailed": "ایج<PERSON> مخاطب", "ContactProfileUpdated": "به روزرسانی مخاطب", "UpdateContactProfileFailed": "به روزرسانی مخاطب", "ContactStatusChanged": "تغییر وضعیت مخاطب", "ChangeContactStatusFailed": "تغییر وضعیت مخاطب", "ContactGroupChanged": "تغییر گروه مخاطب", "ChangeContactGroupFailed": "تغییر گروه مخاطب", "deleteContact": "<PERSON><PERSON><PERSON>", "isDeletingContact": "شما در حال حذف مخاطب هستید، آیا مطمئن هستید؟", "contactCannotDeleted": "مخا<PERSON><PERSON> قابل حذف نمی‌باشد."}, "correspondenceAuthz": {"UserAddedToRole": "افزودن کاربر به راهبران دبیرخانه", "AddUserToRoleFailed": "افزودن کاربر به راهبران دبیرخانه", "UserRemovedFromRole": "حذف کاربر از راهبران دبیرخانه", "RemoveUserFromRoleFailed": "حذف کاربر از راهبران دبیرخانه"}, "ecemails": {"ConvertEceToLetterFailed": "تبدیل ECE به نامه", "EceConvertedToLetter": "تبدیل ECE به نامه"}, "indicators": {"IndicatorCreated": "ایجاد اندیکاتور", "CreateIndicatorFailed": "ایجاد اندیکاتور", "IndicatorUpdated": "ویرایش اندیکاتور", "UpdateIndicatorFailed": "ویرایش اندیکاتور", "IndicatorDisabled": "غیرفعال‌سازی اندیکاتور", "DisableIndicatorFailed": "غیرفعال‌سازی اندیکاتور", "IndicatorEnabled": "فعال‌سازی اندیکاتور", "EnableIndicatorFailed": "فعال‌سازی اندیکاتور"}, "letters": {"LetterComposed": "ایجاد نامه", "ComposeECE": "ثبت نامه ECE", "ComposeLetterFailed": "ایجاد نامه", "LetterUpdated": "ویرایش نامه", "UpdateLetterFailed": "ویرایش نامه", "LetterSigned": "امضای نامه", "SignLetterFailed": "امضای نامه", "LetterNumberSet": "شماره کردن نامه", "SetLetterNumberFailed": "شماره کردن نامه", "LetterForwarded": "ارجاع نامه", "ForwardLetterFailed": "ارجاع نامه", "LetterTerminated": "اختتام نامه", "TerminateLetterFailed": "اختتام نامه", "LetterDiscarded": "لغو نامه", "DiscardLetterFailed": "لغو نامه", "ReceiptAdded": "افزو<PERSON>ن رسید", "AddReceiptFailed": "افزو<PERSON>ن رسید", "ReceiptEdited": "ویرایش رسید", "EditReceiptFailed": "ویرایش رسید", "statusLetter": "وضعیت نامه", "typeLetter": "نوع نامه :", "timeRegistrationLetter": "زمان ثبت نامه :", "numberLetter": "شماره نامه :", "notNumberLetter": "شماره نشده", "timeRegistrationNumberLetter": "زمان ثبت شماره نامه :"}, "managementCenterAuthz": {"UserAddedToRole": "افزودن کاربر به راهبران ارشد", "AddUserToRoleFailed": "افزودن کاربر به راهبران ارشد", "UserRemovedFromRole": "حذف کاربر از راهبران ارشد", "RemoveUserFromRoleFailed": "حذف کاربر از راهبران ارشد"}, "meetingCartables": {"SolutionGranted": "اختصاص راهکار جلسات", "GrantSolutionFailed": "اختصاص راهکار جلسات", "SolutionAccessRevoked": "سلب دسترسی راهبکار جلسات", "RevokeSolutionAccessFailed": "سلب دسترسی راهبکار جلسات"}, "meetingLocations": {"MeetingLocationCreated": "ایج<PERSON> محل جلسه", "CreateMeetingLocationFailed": "ایج<PERSON> محل جلسه", "MeetingLocationUpdated": "ویرایش محل جلسه", "UpdateMeetingLocationFailed": "ویرایش محل جلسه", "MeetingLocationDisabled": "غیرفعال کردن محل جلسه", "DisableMeetingLocationFailed": "غیرفعال کردن محل جلسه", "MeetingLocationEnabled": "فعال کردن محل جلسه", "EnableMeetingLocationFailed": "فعال کردن محل جلسه"}, "meetingsAuthz": {"UserAddedToRole": "افزودن کاربر به راهبران دبیرخانه", "AddUserToRoleFailed": "افزودن کاربر به راهبران دبیرخانه", "UserRemovedFromRole": "حذف کاربر از راهبران دبیرخانه", "RemoveUserFromRoleFailed": "حذف کاربر از راهبران دبیرخانه"}, "meetings": {"MeetingCreated": "ایج<PERSON> جلسه", "CreateMeetingFailed": "ایج<PERSON> جلسه", "EmployeeParticipantsAdded": "افزودن مدعوین", "AddEmployeeParticipantsFailed": "افزودن مدعوین", "EmployeeParticipantsRemoved": "<PERSON><PERSON><PERSON> مدعوین", "RemoveEmployeeParticipantsFailed": "<PERSON><PERSON><PERSON> مدعوین", "ContactParticipantsAdded": "افزودن مدعوین", "AddContactParticipantsFailed": "افزودن مدعوین", "ContactParticipantsRemoved": "<PERSON><PERSON><PERSON> مدعوین", "RemoveContactParticipantsFailed": "<PERSON><PERSON><PERSON> مدعوین", "MeetingUpdated": "ویرایش جلسه", "UpdateMeetingFailed": "ویرایش جلسه", "MeetingCanceled": "لغو جلسه", "CancelMeetingFailed": "لغو جلسه", "MeetingMinutesCreated": "افزودن صورت جلسه", "CreateMeetingMinutesFailed": "افزودن صورت جلسه", "MeetingMinutesUpdated": "ویرایش صورت جلسه", "UpdateMeetingMinutesFailed": "ویرایش صورت جلسه"}, "notifications": {"MarkedAsRead": "نشان کردن به عنوان خوانده شده", "MarkAsReadFailed": "نشان کردن به عنوان خوانده شده", "MarkedAsUnread": "نشان کردن به عنوان خوانده نشده", "MarkAsUnreadFailed": "نشان کردن به عنوان خوانده نشده", "read-notification": "تمام اعلانات خوانده شد.", "errors": {"error-read-notification": "عملیا<PERSON> خواندن اعلانات با خطا مواجه شد."}}, "orders": {"OrderCanceled": "لغو سفارش", "CancelOrderFailed": "لغو سفارش", "OrderCreated": "ایج<PERSON> سفارش", "CreateOrderFailed": "ایج<PERSON> سفارش", "OrderDelivered": "فعال‌سازی سفارش"}, "organizations": {"EmployeeInvitedByUsername": "دعوت کارمند با استفاده از نام کاربری", "InviteEmployeeByUsernameFailed": "دعوت کارمند با استفاده از نام کاربری", "EmployeeInvitedByMobile": "دعوت کارمند با استفاده از شماره موبایل", "InviteEmployeeByMobileFailed": "دعوت کارمند با استفاده از شماره موبایل", "InvitationAccepted": "پذیرفتن دعوت به سازمان", "AcceptInvitationFailed": "پذیرفتن دعوت به سازمان", "InvitationDeclined": "نپذیرفتن دعوت به سازمان", "DeclineInvitationFailed": "نپذیرفتن دعوت به سازمان", "OrganizationCreated": "ایجاد سازمان", "CreateOrganizationFailed": "ایجاد سازمان", "OrganizationProfileUpdated": "ویرایش پروفایل سازمان", "UpdateOrganizationProfileFailed": "ویرایش پروفایل سازمان", "LegalInfoUpdated": "ویرایش اطلاعات حقوقی سازمان", "UpdateLegalInfoFailed": "ویرایش اطلاعات حقوقی سازمان", "EmployeeStatusUpdated": "ویرایش وضعیت کارمند", "UpdateEmployeeStatusFailed": "ویرایش وضعیت کارمند", "EmployeeCodeUpdated": "ویرایش کد کارمند", "UpdateEmployeeCodeFailed": "ویرایش کد کارمند", "EmployeeProfileUpdated": "ویرایش پروفایل کارمند", "UpdateEmployeeProfileFailed": "ویرایش پروفایل کارمند"}, "positions": {"PositionCreated": "ایج<PERSON> سمت", "CreatePositionFailed": "ایج<PERSON> سمت", "PositionUpdated": "ویرایش سمت", "UpdatePositionFailed": "ویرایش سمت", "PositionCodeUpdated": "ویرایش کد سمت", "UpdatePositionCodeFailed": "ویرایش کد سمت", "PositionStatusUpdated": "ویرایش وضعیت سمت", "UpdatePositionStatusFailed": "ویرایش وضعیت سمت", "PositionAssigned": "اختصاص سمت", "AssignPositionFailed": "اختصاص سمت", "PositionRevoked": "سلب سمت", "RevokePositionFailed": "سلب سمت", "ParentPositionUpdated": "ویرایش سمت بالا‌سری", "UpdatePositionParentFailed": "ویرایش سمت بالا‌سری", "PrimaryPositionSet": "اختصاص سمت اصلی", "SetPrimaryPositionFailed": "اختصاص سمت اصلی", "SlotReleased": "آزاد کردن جایگاه", "ReleaseSlotFailed": "آزاد کردن جایگاه", "CreatedByMe": "ایجاد شده توسط من", "AssignedToMe": "اختصاص داده شده به من"}, "secretariats": {"SecretariatCreated": "ایجاد دبیرخانه", "CreateSecretariatFailed": "ایجاد دبیرخانه", "SecretariatUpdated": "ویرایش دبیرخانه", "UpdateSecretariatFailed": "ویرایش دبیرخانه", "LetterLayoutCreated": "ایج<PERSON> قالب چاپی", "CreateLetterLayoutFailed": "ایج<PERSON> قالب چاپی", "LetterLayoutUpdated": "ویرایش قالب چاپی", "UpdateLetterLayoutFailed": "ویرایش قالب چاپی", "LetterLayoutDeleted": "حذ<PERSON> قالب چاپی", "DeleteLetterLayoutFailed": "حذ<PERSON> قالب چاپی", "EceMailConfigSet": "ثبت تنظیمات ECE", "SetEceMailConfigFailed": "ثبت تنظیمات ECE", "EceMailConfigValidated": "تست تنظیمات ECE", "ValidateEceMailConfigFailed": "تست تنظیمات ECE"}, "users": {"UserCreated": "ثبت‌نام", "CreateUserFailed": "ثبت‌نام", "PasswordChanged": "تغییر رمز عبور", "ChangePasswordFailed": "تغییر رمز عبور", "UserProfileUpdated": "ویرایش پروفایل", "UpdateUserProfileFailed": "ویرایش پروفایل", "UserSignatureUpdated": "ویرایش امضا", "UpdateUserSignatureFailed": "ویرایش امضا"}}}}