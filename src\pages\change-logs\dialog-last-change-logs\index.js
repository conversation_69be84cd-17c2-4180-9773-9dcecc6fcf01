import React, { useEffect, useState } from 'react';

import { marked } from 'marked';
import DialogLogs from './dialog-logs';
import { stringToHTMLConverter } from '../../../utils/index';
import { useCommonContext } from '../../../contexts/commonContext/context';
import useListener from '../../../hooks/useListener';

const markdown = require('../change-logs.md');

const DialogLastChangeLogs = () => {
  const {
    changeLog: { openChangeLog: open, setOpenChangeLog: setOpen },
    changeLogMostBeOpen,
  } = useCommonContext();
  // const [open, setOpen] = useState(false);
  const [lastChangeLogs, setLastChangeLogs] = useState();
  const dateRegex = /<h5>(.*?)<\/h5>/g;
  useEffect(() => {
    fetch(markdown)
      .then((res) => res.text())
      .then((text) => {
        const splittedChanges = marked.parse(text).split(/<hr>/g);
        const stringHTML = stringToHTMLConverter(splittedChanges[0]);
        const date = stringHTML.querySelector('h5');
        const div = document.createElement('div');
        div.setAttribute('id', 'logs');

        const lastLogs = marked.parse(text).split(dateRegex)[2].split(/<hr>/g)[0];

        const logsTitle = Array.from(stringToHTMLConverter(lastLogs).querySelectorAll('li'));

        const renderHTML = logsTitle.reduce(
          (result, elem) => `${result}${elem.outerHTML}`,
          `${date.outerHTML}`,
        );

        div.innerHTML = stringToHTMLConverter(renderHTML).innerHTML;
        setLastChangeLogs(div.outerHTML);
      });
  }, []);

  useEffect(() => {
    handleOpenDialog(true);
  }, [lastChangeLogs]);

  useListener(() => {
    handleOpenDialog();
  }, [changeLogMostBeOpen]);

  const handleOpenDialog = (isFirstShow = false) => {
    if (lastChangeLogs) {
      const date = dateRegex.exec(lastChangeLogs)?.[1];
      const storedDate = localStorage.getItem('last-logs');
      if (date !== undefined && date !== storedDate) {
        setOpen(true, isFirstShow);
      }
    }
  };

  const handleCloseDialog = () => {
    if (lastChangeLogs) {
      const date = dateRegex.exec(lastChangeLogs)?.[1];
      localStorage.setItem('last-logs', date);
      setOpen(false);
    }
  };

  return (
    <>
      {open && (
        <DialogLogs
          open={open}
          lastChangeLogs={lastChangeLogs}
          handleCloseDialog={handleCloseDialog}
        />
      )}
    </>
  );
};

export default DialogLastChangeLogs;
