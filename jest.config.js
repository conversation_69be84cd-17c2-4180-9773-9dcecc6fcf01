// jest.config.js
module.exports = {
  // Specify the root directory for <PERSON><PERSON> to search for tests
  rootDir: './src',

  // Specify the test environment (e.g., jsdom for browser-like environment)
  testEnvironment: 'jsdom',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],

  // Add any test-specific setup files
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.js'],

  testPathIgnorePatterns: ['./node_modules/**', './dist/', './dist-test/'],
  transformIgnorePatterns: [
    '/node_modules/(?!(@mui|@material-ui)/)',
  ],

  // Define patterns for files that contain your tests
  testRegex: '(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx|js|ts|tsx)?$',

  // Define module name mapper for importing modules, including CSS and assets
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
    '^@/(.*)$': '<rootDir>/src/$1',
  },
};

// Add TextEncoder polyfill
if (typeof TextEncoder === 'undefined') {
  global.TextEncoder = require('util').TextEncoder;
}

