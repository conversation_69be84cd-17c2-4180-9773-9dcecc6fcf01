import { ComponentType } from 'react';
import { useTranslation } from 'react-i18next';
import useExtractPath from 'hooks/path-extractor';
import { compose } from '@reduxjs/toolkit';
import { connect } from 'react-redux';

const withDiscoverPath = (WrappedComponent: ComponentType) => (props: any) => {
  const { t } = useTranslation();
  const { organization, secretariats, letters, groups, ...otherProps } = props;

  const { translatedPathSegments, urlPathSegments } = useExtractPath({
    organization,
    secretariats,
    letters,
    groups,
  });

  document.title = `${t('common.labels.awat')} | ${translatedPathSegments.join(' - ')}`;

  return <WrappedComponent {...{ ...otherProps, translatedPathSegments, urlPathSegments }} />;
};

const mapStateToProps = (state: any) => {
  const { organization, secretariats, letters, groups } = state;
  return { organization, secretariats, letters, groups };
};

export default compose(connect(mapStateToProps, {}), withDiscoverPath);
