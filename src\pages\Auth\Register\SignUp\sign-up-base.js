import { forwardRef } from 'react';

import { PatternFormat } from 'react-number-format';
import Button from '@mui/material/Button';
import Link from '@mui/material/Link';
import TextField from '@mui/material/TextField';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import FormHelperText from '@mui/material/FormHelperText';
import OutlinedInput from '@mui/material/OutlinedInput';
import FormControl from '@mui/material/FormControl';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import IconButton from '@mui/material/IconButton';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';

import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import { Link as RouterLink } from 'react-router-dom';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import CheckIcon from '@mui/icons-material/Check';
import RemoveIcon from '@mui/icons-material/Remove';
import logo from 'assets/images/logo/Logo.svg';
import SocialMedia from 'components/SocialMedia';
import Loading from 'components/Loading';
import { useMainLayoutStyles } from 'contexts/mainLayoutStylesContext';
import { useRegisterContext } from 'contexts/pageContext/auth/registerContext';
import AWBox from 'components/AWComponents/AWBox';
import TermsModal from '../../../terms/component/terms-modal';

const useStyles = makeStyles()((theme) => ({
  root: {
    width: '100%',
    height: '100%',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    minHeight: 650,
  },
  mobileRoot: {
    position: 'relative',
    textAlign: 'center',
    flexGrow: 1,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  mobileMiddlePaper: {
    width: '100%',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '24px 19px 24px 19px',
    margin: 'auto',
    flex: 1,
    marginTop: 0,
    '& .marginTopMobile': {
      marginTop: theme.spacing(3),
    },
  },
  logo: {
    display: 'flex',
    margin: theme.spacing(2, 0),
  },
  title: {
    margin: theme.spacing(2, 0),
  },
  form: {
    height: '100%',
    width: '100%',
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    flexGrow: 2,
  },
  input: {
    marginTop: theme.spacing(1),
    marginBottom: theme.spacing(1),
  },
  ltrInput: {
    '& input': {
      direction: 'ltr',
    },
  },
  center: {
    textAlign: 'center',
  },
  marginTop1: {
    marginTop: theme.spacing(1),
  },
  marginTop2: {
    marginTop: '16px',
  },
  validationPassword: {
    padding: 0,
    margin: 0,
    listStyle: 'none',
  },
  login: {
    position: 'absolute',
    bottom: 0,
  },
  createUser: {
    display: 'flex',
    alignContent: 'center',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
    '& h6': {
      textAlign: 'center',
      marginBottom: theme.spacing(4),
    },
  },
  successIcon: {
    color: '#00c89c',
    fontSize: '5rem',
  },
  errorText: {
    color: 'red',
  },
  margin: {
    margin: '15px 0',
  },
  phoneNumberInput: {
    direction: 'ltr',
    '& input': {
      letterSpacing: '2px',
    },
  },
  checkBox: {
    width: '100%',
  },
  itemValidationPassword: {
    width: '50%',
    float: 'right',
    color: theme.palette.text.gray,
    display: 'flex',
    alignItems: 'center',
    '& svg': {
      fill: '#00c89c',
      fontSize: '.9rem',
      marginRight: '2px',
    },
    '& svg.dash': {
      fill: theme.palette.text.gray,
    },
  },
  inputs: {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    justifyContent: ' space-around',
  },
  PermissionError: {
    color: theme.palette.text.error,
    '& .MuiButtonBase-root, .MuiTypography-root': {
      color: theme.palette.text.error,
    },
  },
  rules: {
    color: theme.palette.text.link,
    fontWeight: 'bold',
    textDecoration: 'underline!important',
    textUnderlineOffset: '5px',
  },
  goLogin: {
    marginTop: theme.spacing(4),
    color: '#111111',
  },
  loginLink: {
    textDecoration: 'underline!important',
    textUnderlineOffset: '5px',
    color: theme.palette.text.link,
  },
  mobileMode: {
    flex: 1,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'end',
  },
}));

const NumericFormatCustom = forwardRef((props) => {
  const { onChange, ...other } = props;

  return (
    <PatternFormat
      {...other}
      format="#### ### ####"
      value="09"
      onValueChange={(values) => {
        onChange({
          target: {
            name: props.name,
            value: values.value,
          },
        });
      }}
      valueIsNumericString
    />
  );
});

const BaseSignUp = ({ isMobile }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslation();
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes: globalClasses } = makeStyles()(stylesGenerator)();

  const {
    getValidationErrorEx,
    isInvalid,
    getValidationError,
    submitSignUp,
    state,
    onChange,
    onChangeCheckPermission,
    phonErrorText,
    userExisted,
    usernameErrorText,
    passwordError,
    setPasswordError,
    showFirstPassword,
    setShowFirstPassword,
    loadingSignUp,
    handleOpenTermsModal,
  } = useRegisterContext();

  return (
    <>
      <Container
        component="div"
        maxWidth="xs"
        className={isMobile ? classes.mobileRoot : classes.root}
      >
        <div className={isMobile ? classes.mobileMiddlePaper : globalClasses.middlePaper}>
          <img src={logo} alt="Logo" className={classes.logo} />
          <Typography variant="body1" className={cx(globalClasses.bold, classes.title)}>
            {t('auth.labels.registerTitle')}
          </Typography>
          <form className={cx(classes.form)} onSubmit={submitSignUp}>
            <div className={classes.inputs}>
              <div className={classes.phoneNumber}>
                <TextField
                  variant="outlined"
                  fullWidth
                  name="mobile"
                  label={` * ${t('common.labels.mobile')}`}
                  className={classes.phoneNumberInput}
                  error={phonErrorText || isInvalid('mobile')}
                  helperText={
                    getValidationError('mobile') ? getValidationError('mobile') : phonErrorText
                  }
                  value={state?.mobile}
                  onChange={onChange}
                  InputProps={{
                    inputComponent: NumericFormatCustom,
                  }}
                />
              </div>
              <TextField
                className={cx(classes.input, classes.marginTop2, classes.ltrInput)}
                label={`${t('common.labels.username')} *`}
                variant="outlined"
                fullWidth
                autoComplete="off"
                name="username"
                value={state?.username}
                onChange={onChange}
                error={userExisted || !!usernameErrorText || isInvalid('username')}
                helperText={
                  getValidationError('username')
                    ? getValidationError('username')
                    : usernameErrorText ||
                      (userExisted
                        ? t('auth.messages.existUsername')
                        : t('common.validation.useNumberAndChar'))
                }
              />
              <FormControl
                variant="outlined"
                className={cx(classes.input, classes.marginTop1)}
                fullWidth
              >
                <InputLabel
                  htmlFor="outlined-adornment-password"
                  error={passwordError || isInvalid('password')}
                >
                  {`${t('common.labels.password')} *`}
                </InputLabel>
                <OutlinedInput
                  className={classes.ltrInput}
                  id="outlined-adornment-password"
                  type={showFirstPassword ? 'text' : 'password'}
                  name="password"
                  value={state?.password}
                  onChange={onChange}
                  onFocus={() => setPasswordError(false)}
                  error={passwordError || isInvalid('password') || !!getValidationError('password')}
                  endAdornment={
                    <InputAdornment position="end">
                      <IconButton
                        aria-label="toggle password visibility"
                        onClick={() => setShowFirstPassword(!showFirstPassword)}
                        edge="end"
                      >
                        {showFirstPassword ? <Visibility /> : <VisibilityOff />}
                      </IconButton>
                    </InputAdornment>
                  }
                />
                <FormHelperText id="my-helper-text" component="div">
                  <ul className={cx(classes.validationPassword)}>
                    <li className={cx(classes.itemValidationPassword)}>
                      {getValidationErrorEx('password') &&
                      !getValidationErrorEx('password').find(
                        (x) => x.type === 'passwordComplexity.tooShort',
                      ) ? (
                        <CheckIcon />
                      ) : (
                        <RemoveIcon className="dash" />
                      )}
                      <span>{t('common.validation.min8')}</span>
                    </li>
                    <li className={cx(classes.itemValidationPassword)}>
                      {getValidationErrorEx('password') &&
                      !getValidationErrorEx('password').find(
                        (x) => x.type === 'passwordComplexity.lowercase',
                      ) &&
                      !getValidationErrorEx('password').find(
                        (x) => x.type === 'passwordComplexity.uppercase',
                      ) ? (
                        <CheckIcon />
                      ) : (
                        <RemoveIcon className="dash" />
                      )}
                      <span>{t('common.validation.lowerAndUpper')}</span>
                    </li>
                    <li className={cx(classes.itemValidationPassword)}>
                      {getValidationErrorEx('password') &&
                      !getValidationErrorEx('password').find(
                        (x) => x.type === 'passwordComplexity.symbol',
                      ) ? (
                        <CheckIcon />
                      ) : (
                        <RemoveIcon className="dash" />
                      )}
                      <span>{t('common.validation.specialChar')}</span>
                    </li>
                    <li className={cx(classes.itemValidationPassword)}>
                      {getValidationErrorEx('password') &&
                      !getValidationErrorEx('password').find(
                        (x) => x.type === 'passwordComplexity.numeric',
                      ) ? (
                        <CheckIcon />
                      ) : (
                        <RemoveIcon className="dash" />
                      )}
                      <span>{t('common.validation.includeNumber')}</span>
                    </li>
                  </ul>
                </FormHelperText>
              </FormControl>
            </div>
            <div className="marginTopMobile">
              <FormControlLabel
                className={
                  getValidationError('checkPermission')
                    ? cx(classes.checkBox, classes.marginTop1, classes.PermissionError)
                    : cx(classes.checkBox, classes.marginTop1)
                }
                control={
                  <Checkbox
                    name="checkPermission"
                    checked={state.checkPermission}
                    onChange={onChangeCheckPermission}
                  />
                }
                label={
                  <Typography
                    variant="subtitle2"
                    className={getValidationError('checkPermission') ? classes.PermissionError : ''}
                  >
                    <Link
                      onClick={handleOpenTermsModal}
                      classes={{ root: classes.rules }}
                      className="border"
                    >
                      {t('common.labels.awatRole')}
                    </Link>
                    {t('common.labels.acceptanceRules')}
                  </Typography>
                }
              />
              <Button variant="contained" type="submit" fullWidth disabled={loadingSignUp}>
                {loadingSignUp ? <Loading color="white" /> : t('common.labels.register')}
              </Button>
            </div>
          </form>
          <Grid container justifyContent="center" className={classes.marginTop1}>
            <Typography variant="subtitle1" className={classes.goLogin}>
              {t('auth.strings.ifWantEnter')}{' '}
              <Link component={RouterLink} to="/" variant="subtitle1" className={classes.loginLink}>
                {t('auth.strings.enterIt')}
              </Link>
            </Typography>
          </Grid>
        </div>
        <AWBox className={isMobile ? classes.mobileMode : ''}>
          <SocialMedia />
        </AWBox>
      </Container>
      <TermsModal isMobile={isMobile} />
    </>
  );
};
BaseSignUp.propTypes = {
  isMobile: PropTypes.bool,
};

BaseSignUp.defaultProps = {
  isMobile: false,
};

export default BaseSignUp;
