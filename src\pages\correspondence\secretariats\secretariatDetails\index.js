/* eslint-disable no-nested-ternary */
/* eslint-disable no-undef */
import { useState, useEffect, useMemo } from 'react';

import PropTypes from 'prop-types';
import Jo<PERSON> from 'joi';
import { v4 as uuidv4 } from 'uuid';
import { connect } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import useAxios from 'axios-hooks';

import { useMediaQuery } from '@mui/material';
import useIndicatorsHasAccess from 'hooks/useIndicatorsHasAccess';
import { baseUrl } from 'api/v1/services/api-client';
import { useTranslation } from 'react-i18next';
import {
  getSecretariatsAsync as getSecretariatsAsync_,
  editSecretariatAsync as editSecretariatAsync_,
  getSecretariatsReset as getSecretariatsReset_,
  getIndicatorsAsync as getIndicatorsAsync_,
  getLetterLayoutsAsync as getLetterLayoutsAsync_,
  getLetterLayoutsReset as getLetterLayoutsReset_,
  getIndicatorsReset as getIndicatorsReset_,
  createLetterLayoutsReset as createLetterLayoutsReset_,
  editLetterLayoutReset as editLetterLayoutReset_,
  createLetterLayoutAsync as createLetterLayoutAsync_,
  editLetterLayoutAsync as editLetterLayoutAsync_,
  deleteLetterLayoutAsync as deleteLetterLayoutAsync_,
} from '../secretariatsSlice';
import { withValidation } from '../../../../common/validation';
import { RouterPrompt } from '../../../../components/RouterPrompt';
import SecretariatDetailsMedia from './secretariat-details-media';
import { useLayout } from '../../../../contexts/layoutContext';
import { SecretariatContext } from '../../../../contexts/pageContext/secretariat/secretariatContext';
import snackbarUtils from '../../../../utils/snackbarUtils';
import { downloadBlob } from '../../../../utils';
import { A4, A5 } from '../../../../components/LayoutEditor/defaultLayout';
import { withCorrespondenceInitializer } from '../../withCorresponseInitializer';
import withSecretariatsSecurity from '../with-secretariats-security';

const initialState = {
  name: '',
  code: '',
  description: '',
};

const schema = {
  name: Joi.string().required().max(32),
  code: Joi.string().required(),
  description: Joi.string().optional().allow(''),
};

const SecretariatDetailsValidation = withValidation({ initialState, schema })(({
  touched,
  validationOnChange,
  validationOnSubmit,
  onResetEditForm,
  onChange,
  setTouched,
  isBlocking,
  setIsBlocking,
  isResetTouched,
  secretariats,
  indicatorsList,
  organization,
  employees,
  secretariatSelected,
  tab,
  onSetTab,
  state,
  isInvalid,
  getValidationError,
  submitEditForm,
  showLoadingEditForm,
  isSecretariatEdited,
  showLoadingIndicators,
  showLoadingLetterLayouts,
  handleClickOpenIndicator,
  handleClickOpenIndicatorEdit,
  handleAddLetterLayout,
  showDialogAddLayout,
  typeDialogAddLayout,
  handleCloseAddLayout,
  handleClickOpenAddLayout,
  submitAddLayout,
  showLoadingAddLayout,
  handleClickOpenDeleteLayout,
  handleCloseDeleteLayout,
  deleteLayoutObject,
  layoutState,
  onChangeLayout,
  layoutSizes,
  onChangeLayoutSize,
  onChangeLayoutSizeInput,
  handleEditLayoutInfo,
  handleRequestDeleteLayout,
  handleConfirmDeleteLayout,
  handleCloseDeleteLayoutDialog,
  handleEditLayout,
  getRequestData,
  goToLetterList,
  setLayout,
  handleExport,
  security,
}) => {
  const [isTouched, setIsTouched] = useState(false);
  const onResetForm = () => {
    setTouched([]);
    onResetEditForm();
  };

  const onChange2 = (e) => {
    validationOnChange(e);
    onChange(e);
  };
  useEffect(() => {
    setIsTouched(touched.length > 0);
  }, [touched]);

  useEffect(() => {
    setIsBlocking(isTouched);
  }, [isTouched, setIsBlocking]);

  useEffect(() => {
    if (isResetTouched) {
      setTouched([]);
    }
  }, [isResetTouched]);

  useEffect(() => {
    if (isSecretariatEdited) {
      setTouched([]);
    }
  }, [isSecretariatEdited]);

  const onEditForm = () => {
    if (validationOnSubmit(state)) {
      submitEditForm();
    }
  };

  const onAddLayout = () => {
    if (getRequestData().name && getRequestData().size) {
      submitAddLayout();
    }
  };

  return (
    <SecretariatContext.Provider
      value={{
        security,
        secretariats,
        employees,
        indicatorsList,
        secretariatSelected,
        tab,
        onSetTab,
        state,
        isInvalid,
        getValidationError,
        onEditForm,
        showLoadingEditForm,
        isTouched,
        onResetForm,
        onChange: onChange2,
        showLoadingIndicators,
        showLoadingLetterLayouts,
        handleClickOpenIndicator,
        handleClickOpenIndicatorEdit,
        handleAddLetterLayout,
        showDialogAddLayout,
        typeDialogAddLayout,
        handleCloseAddLayout,
        handleClickOpenAddLayout,
        onAddLayout,
        showLoadingAddLayout,
        handleClickOpenDeleteLayout,
        handleCloseDeleteLayout,
        deleteLayoutObject,
        layoutState,
        onChangeLayout,
        layoutSizes,
        onChangeLayoutSize,
        onChangeLayoutSizeInput,
        handleEditLayoutInfo,
        handleRequestDeleteLayout,
        handleConfirmDeleteLayout,
        handleCloseDeleteLayoutDialog,
        handleEditLayout,
        setLayout,
        ownerId: organization.selectedOrganization?.owner?.id,
        goToLetterList,
        handleExport,
      }}
    >
      <RouterPrompt isBlocking={isBlocking} />
      <SecretariatDetailsMedia />
    </SecretariatContext.Provider>
  );
});

const SecretariatDetails = ({
  secretariats,
  organization,
  getSecretariatsAsync,
  editSecretariatAsync,
  getSecretariatsReset,
  getIndicatorsAsync,
  getLetterLayoutsAsync,
  getLetterLayoutsReset,
  getIndicatorsReset,
  createLetterLayoutsReset,
  createLetterLayoutAsync,
  editLetterLayoutAsync,
  editLetterLayoutReset,
  deleteLetterLayoutAsync,
  security,
  accessibility,
  securityLevelDictionary,
  correspondenceAuthz,
}) => {
  const navigate = useNavigate();
  const params = useParams();
  const { setTitle } = useLayout();
  const [tab, setTab] = useState('secretariatInfo');
  const [secretariatSelected, setSecretariatSelected] = useState(null);
  const [initState, setInitState] = useState(initialState);
  const [state, setState] = useState(initialState);
  const [showLoadingEditForm, setShowLoadingEditForm] = useState(false);
  const [showLoadingIndicators, setShowLoadingIndicators] = useState(false);
  const [showLoadingLetterLayouts, setShowLoadingLetterLayouts] = useState(false);
  const [isBlocking, setIsBlocking] = useState(false);
  const [isResetTouched] = useState(false);
  const [isSecretariatEdited, setIsSecretariatEdited] = useState(true);
  const [showDialogAddLayout, setShowDialogAddLayout] = useState(false);

  const [deleteLayoutObject, setDeleteLayoutObject] = useState({
    loading: false,
    showDialog: false,
    selectedItem: null,
  });
  const [typeDialogAddLayout, setTypeDialogAddLayout] = useState('');
  const [showLoadingAddLayout, setShowLoadingAddLayout] = useState(false);
  const [addLayout, setAddLayout] = useState(false);
  const [layoutState, setLayoutState] = useState({
    name: '',
    size: 'A4',
    sizeInput: 'A4',
    description: '',
    isDefault: false,
    layoutId: '',
  });
  const [layout, setLayout] = useState(null);
  const { organizationId, secretariatId } = params;
  const ownerId = organization.selectedOrganization?.owner?.id;
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const { t } = useTranslation();

  const readData = (data) => ({
    name: data ? data.name : '',
    code: data ? data.code : '',
    description: data ? data.description : '',
  });

  useEffect(() => {
    getLetterLayoutsReset();
    getIndicatorsReset();
    getSecretariatsReset();
  }, [secretariatId]);

  useEffect(() => {
    if (secretariatSelected) {
      setInitState(readData(secretariatSelected));
      setState(readData(secretariatSelected));
      setTitle(secretariatSelected.name);
    }
  }, [secretariatSelected]);

  useEffect(() => {
    if (secretariats.secretariatsList.data?.length > 0) {
      setSecretariatSelected(
        secretariats.secretariatsList.data.find((x) => x._id === secretariatId),
      );
    } else {
      getSecretariatsAsync({ path: organizationId });
    }
  }, [secretariats.secretariatsList.data]);

  const onResetEditForm = () => {
    setState(readData(secretariatSelected));
  };

  const indicatorsList = useMemo(() => {
    const indicators = secretariats.indicatorsList.data;
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;
    setShowLoadingIndicators(false);
    return useIndicatorsHasAccess({
      accessibility,
      organizationId,
      secretariat: secretariatSelected,
      indicators,
      ownerId,
      indicatorsListSecurityDictionary: securityLevelDictionary.indicatorsListSecurityDictionary,
      solutionOrganizationRoles,
      userPermissions,
    });
  }, [
    secretariats.indicatorsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
    tab,
  ]);

  const indicatorsListToGetIndicator = useMemo(() => {
    const indicators = secretariats.indicatorsList.data;
    const solutionOrganizationRoles = correspondenceAuthz.solutionOrganizationRoles.data;
    const userPermissions = correspondenceAuthz.userPermissions.data;
    setShowLoadingIndicators(false);
    return useIndicatorsHasAccess({
      accessibility,
      organizationId,
      secretariat: secretariatSelected,
      indicators,
      ownerId,
      indicatorsListSecurityDictionary: securityLevelDictionary.securityDictionaryForGetIndicator,
      solutionOrganizationRoles,
      userPermissions,
    });
  }, [
    secretariats.indicatorsList.data,
    correspondenceAuthz.solutionOrganizationRoles.data,
    correspondenceAuthz.userPermissions.data,
    tab,
  ]);

  useEffect(() => {
    if (secretariats.indicatorsList.status === 'idle') {
      setShowLoadingIndicators(false);
    }
  }, [secretariats.indicatorsList.status, tab]);

  const getIndicators = () => {
    const options = {
      path: secretariatId,
    };
    setShowLoadingIndicators(true);
    getIndicatorsAsync(options);
  };

  useEffect(() => {
    if (secretariats.letterLayoutsList.status === 'idle') {
      setShowLoadingLetterLayouts(false);
    }
  }, [secretariats.letterLayoutsList.status, tab]);

  const getLetterLayouts = () => {
    setShowLoadingLetterLayouts(true);
    const options = {
      secretariatId,
    };
    getLetterLayoutsAsync(options);
  };

  const onSetTab = (event, newValue) => {
    if (newValue === 'indicators') {
      getIndicators();
    }
    if (newValue === 'letter-layouts') {
      getLetterLayouts();
    }
    setTab(newValue);
  };

  const handleClickOpenIndicator = () => {
    navigate(
      `/organizations/${organizationId}/correspondence/correspondence-management/secretariats/${secretariatId}/new-indicator`,
    );
  };

  const handleClickOpenIndicatorEdit = (row) => {
    const existIndicator = indicatorsListToGetIndicator.find((value) => value?._id === row._id);
    if (existIndicator) {
      navigate(
        `/organizations/${organizationId}/correspondence/correspondence-management/secretariats/${secretariatId}/${row._id}`,
      );
      return;
    }

    snackbarUtils.error(t('forbidden.labels.noAccess'));
  };
  useEffect(() => {
    if (secretariats.editSecretariat?.success) {
      setIsBlocking(false);
      setShowLoadingEditForm(false);
      getSecretariatsReset();
    } else {
      setShowLoadingEditForm(false);
    }
  }, [secretariats.editSecretariat.data]);

  const submitEditForm = () => {
    setShowLoadingEditForm(true);
    setIsSecretariatEdited(false);
    const options = {
      data: {
        code: state.code,
        description: state.description,
        id: secretariatId,
        name: state.name,
        secretariatId,
      },
    };
    editSecretariatAsync(options);
  };
  const onChange = (e) => {
    setState((preState) => ({ ...preState, [e.target.name]: e.target.value }));
  };

  const handleAddLetterLayout = () => {
    setLayoutState({
      name: '',
      size: 'A4',
      sizeInput: 'A4',
      description: '',
      isDefault: false,
      layoutId: '',
    });
    setLayout(null);
    setShowDialogAddLayout(true);
    setTypeDialogAddLayout('create');
    createLetterLayoutsReset();
  };

  const handleCloseDeleteLayout = () => {
    setShowDialogDeleteLayout(false);
  };

  const handleClickOpenDeleteLayout = () => {
    setShowDialogDeleteLayout(true);
  };

  const handleCloseAddLayout = () => {
    setShowDialogAddLayout(false);
  };

  const handleClickOpenAddLayout = () => {
    setShowDialogAddLayout(true);
  };

  useEffect(() => {
    const type = typeDialogAddLayout === 'create' ? 'createLetterLayout' : 'editLetterLayout';
    if (addLayout && secretariats[type]?.success) {
      setAddLayout(false);
      setShowLoadingAddLayout(false);
      handleCloseAddLayout();
      getLetterLayoutsReset();
      getLetterLayouts();
      createLetterLayoutsReset();
      editLetterLayoutReset();
      if (typeDialogAddLayout === 'create') {
        navigate(
          `/organizations/${organizationId}/correspondence/correspondence-management/secretariats/${secretariatId}/letter-layout/${secretariats[type].data.layoutId}`,
        );
      }
    } else {
      setShowLoadingAddLayout(false);
    }
  }, [secretariats.createLetterLayout.data, secretariats.editLetterLayout.data]);

  const getRequestData = (name, description, layoutId, size, isDefault) => ({
    description: layoutState.description || description,
    id: secretariatId,
    // layout: typeDialogAddLayout === 'create' ? (layoutState.size === 'A5' ? A5 : A4) : layout || {},
    layout:
      typeDialogAddLayout === 'create'
        ? layoutState.size === 'A5'
          ? A5
          : A4
        : layoutState.layout || {},
    layoutId: layoutState.layoutId || layoutId || uuidv4(),
    name: layoutState.name || name,
    organization: { id: organizationId },
    secretariat: { id: secretariatId },
    size: layoutState.size || size,
    isDefault: layoutState.isDefault ?? isDefault,
    secretariatId,
  });

  const submitAddLayout = () => {
    setShowLoadingAddLayout(true);
    setAddLayout(true);
    (typeDialogAddLayout === 'create' ? createLetterLayoutAsync : editLetterLayoutAsync)({
      data: getRequestData(),
    });
  };

  const onChangeLayout = (e) => {
    if (e.target.name === 'isDefault') {
      setLayoutState((preState) => ({ ...preState, [e.target.name]: e.target.checked }));
    } else {
      setLayoutState((preState) => ({ ...preState, [e.target.name]: e.target.value }));
    }
  };

  const onChangeLayoutSize = (e, newInputValue) => {
    setLayoutState((preState) => ({ ...preState, size: newInputValue }));
  };

  const onChangeLayoutSizeInput = (e, newInputValue) => {
    setLayoutState((preState) => ({ ...preState, sizeInput: newInputValue }));
  };

  const handleEditLayoutInfo = (e, row) => {
    setShowDialogAddLayout(true);
    setTypeDialogAddLayout('edit');
    setLayoutState({
      name: row.name,
      size: row.size,
      sizeInput: '',
      description: row.description,
      isDefault: row.isDefault,
      layoutId: row.layoutId,
      layout: row.layout,
    });
  };

  const handleRequestDeleteLayout = (e, row) => {
    setDeleteLayoutObject((prevState) => ({ ...prevState, showDialog: true, selectedItem: row }));
  };

  const handleCloseDeleteLayoutDialog = () => {
    setDeleteLayoutObject((prevState) => ({
      ...prevState,
      showDialog: false,
      loading: false,
      selectedItem: null,
    }));
  };

  const handleConfirmDeleteLayout = () => {
    setDeleteLayoutObject((prevState) => ({
      ...prevState,
      loading: true,
    }));
    deleteLetterLayoutAsync({ secretariatId, layoutId: deleteLayoutObject.selectedItem.layoutId });
  };

  useEffect(() => {
    setDeleteLayoutObject((prevState) => ({
      ...prevState,
      showDialog: false,
      loading: false,
      selectedItem: null,
    }));
    if (secretariats.deleteLetterLayout?.success) {
      setShowLoadingAddLayout(false);
      getLetterLayoutsReset();
      getLetterLayouts();
    }
  }, [secretariats.deleteLetterLayout.data]);

  const handleEditLayout = (e, row) => {
    navigate(
      `/organizations/${organizationId}/correspondence/correspondence-management/secretariats/${secretariatId}/letter-layout/${row.layoutId}`,
    );
  };

  const [{ error: exportError }, exportExcel] = useAxios(
    {
      url: `${baseUrl}/api/v1/letters/numbered/indicators/export`,
      method: 'POST',
      responseType: 'blob',
    },
    { manual: true },
  );

  useEffect(() => {
    if (exportError) {
      snackbarUtils.error(t('common.errors.oparationError'));
    }
  }, [exportError]);

  const handleExport = async (e, row) => {
    e.stopPropagation();
    const res = await exportExcel({ data: { indicatorId: row.id, secretariatId } });
    const blob = new Blob([res.data], { type: 'xlsx' });
    downloadBlob(blob, 'export.xlsx');
  };

  const goToLetterList = (e, row) => {
    e.stopPropagation();
    e.preventDefault();
    navigate(
      `/organizations/${organizationId}/correspondence/correspondence-management/secretariats/${secretariatId}/${row.id}/numberedLetters`,
    );
  };

  return (
    <SecretariatDetailsValidation
      {...{
        security,
        onResetEditForm,
        secretariats,
        indicatorsList,
        organization,
        organizationId,
        secretariatSelected,
        tab,
        onSetTab,
        initialState: initState,
        state,
        onChange,
        submitEditForm,
        showLoadingEditForm,
        setIsBlocking,
        isResetTouched,
        isBlocking,
        isSecretariatEdited,
        showLoadingIndicators,
        showLoadingLetterLayouts,
        handleClickOpenIndicator,
        handleClickOpenIndicatorEdit,
        handleAddLetterLayout,
        showDialogAddLayout,
        typeDialogAddLayout,
        handleCloseAddLayout,
        handleClickOpenAddLayout,
        submitAddLayout,
        showLoadingAddLayout,
        handleClickOpenDeleteLayout,
        handleCloseDeleteLayout,
        deleteLayoutObject,
        layoutState,
        onChangeLayout,
        layoutSizes,
        onChangeLayoutSize,
        onChangeLayoutSizeInput,
        handleEditLayoutInfo,
        handleRequestDeleteLayout,
        handleConfirmDeleteLayout,
        handleCloseDeleteLayoutDialog,
        handleEditLayout,
        layout,
        getRequestData,
        goToLetterList,
        handleExport,
        setLayout,
      }}
    />
  );
};

const mapStateToProps = (state) => {
  const { secretariats, organization, letters, employees, correspondenceAuthz } = state;
  return {
    secretariats,
    organization,
    letters,
    employees,
    correspondenceAuthz,
  };
};

const layoutSizes = ['A4', 'A5'];

SecretariatDetails.propTypes = {
  getSecretariatsAsync: PropTypes.func.isRequired,
  editSecretariatAsync: PropTypes.func.isRequired,
  getSecretariatsReset: PropTypes.func.isRequired,
  organization: PropTypes.shape({}).isRequired,
  letters: PropTypes.shape({}).isRequired,
  secretariats: PropTypes.shape({
    editLetterLayout: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
      }),
    }),
    editSecretariat: PropTypes.shape({
      data: PropTypes.shape({
        success: PropTypes.bool,
      }),
    }),
    secretariatsList: PropTypes.shape({
      status: PropTypes.string,
      data: PropTypes.arrayOf(
        PropTypes.shape({
          _id: PropTypes.string,
        }),
      ),
    }),
    letterLayoutsList: PropTypes.shape({
      status: PropTypes.string,
      data: PropTypes.arrayOf(PropTypes.shape({})),
    }),
    indicatorsList: PropTypes.shape({
      status: PropTypes.string,
      data: PropTypes.arrayOf(PropTypes.shape({})),
    }),
    createLetterLayout: PropTypes.shape({
      data: PropTypes.shape({
        data: PropTypes.shape({
          layout: PropTypes.shape({}),
          isDefault: PropTypes.bool,
          layoutId: PropTypes.string,
          size: PropTypes.string,
          name: PropTypes.string,
          description: PropTypes.string,
        }),
      }),
    }),
    deleteLetterLayout: PropTypes.shape({
      data: PropTypes.shape({}),
    }),
  }).isRequired,
  getIndicatorsAsync: PropTypes.func.isRequired,
  getLetterLayoutsAsync: PropTypes.func.isRequired,
  getLetterLayoutsReset: PropTypes.func.isRequired,
  createLetterLayoutsReset: PropTypes.func.isRequired,
  editLetterLayoutReset: PropTypes.func.isRequired,
  createLetterLayoutAsync: PropTypes.func.isRequired,
  editLetterLayoutAsync: PropTypes.func.isRequired,
  getIndicatorsReset: PropTypes.func.isRequired,
  deleteLetterLayoutAsync: PropTypes.func.isRequired,
  setTitle: PropTypes.func,
};
SecretariatDetails.defaultProps = {
  setTitle: () => {},
};

export default withCorrespondenceInitializer(
  withSecretariatsSecurity(
    connect(mapStateToProps, {
      deleteLetterLayoutAsync: deleteLetterLayoutAsync_,
      getSecretariatsAsync: getSecretariatsAsync_,
      editSecretariatAsync: editSecretariatAsync_,
      getSecretariatsReset: getSecretariatsReset_,
      getIndicatorsAsync: getIndicatorsAsync_,
      getLetterLayoutsAsync: getLetterLayoutsAsync_,
      getLetterLayoutsReset: getLetterLayoutsReset_,
      createLetterLayoutsReset: createLetterLayoutsReset_,
      editLetterLayoutReset: editLetterLayoutReset_,
      getIndicatorsReset: getIndicatorsReset_,
      createLetterLayoutAsync: createLetterLayoutAsync_,
      editLetterLayoutAsync: editLetterLayoutAsync_,
    })(SecretariatDetails),
  ),
  // { isCallCorrespondenceAdminsPermissions: false },
);
