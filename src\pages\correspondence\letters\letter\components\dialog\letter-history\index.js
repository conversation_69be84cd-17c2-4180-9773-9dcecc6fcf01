import { useState, useEffect } from 'react';

import useMediaQuery from '@mui/material/useMediaQuery';

import MobileLetterHistory from './letter-history-mobile';
import DesktopLetterHistory from './letter-history-desktop';
import letterHistoryMaker from './letter-history-maker';
import { LetterHistoryContext } from '../../../../../../../contexts/pageContext/letters/letter-history/letter-history-context';
import { useLetter } from '../../../../../../../contexts/pageContext/letters/letterContext';

const LetterHistoryDialog = ({ isMobile = false }) => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));

  //* ///////////////////////////////////////////////////////////////////
  //* -----------------------------states------------------------------//
  //* ///////////////////////////////////////////////////////////////////
  const [letterHistoryRecords, setLetterHistoryRecords] = useState([]);
  const [mobileLetterHistorySegments, setMobileLetterHistorySegments] = useState([{}, {}, {}]);
  const [collapseCardId, setCollapseCardId] = useState(null);
  const [touchStartX, setTouchStartX] = useState(0);
  const [touchEndX, setTouchEndX] = useState(0);
  const [touchStartY, setTouchStartY] = useState(0);
  const [touchEndY, setTouchEndY] = useState(0);
  const [currentSwipeItem, setCurrentSwipeItem] = useState(null);
  const [openDocListSidebar, setOpenDocListSidebar] = useState({ open: false, attachments: [] });
  const [checkedLetterForwardsFilter, setCheckedLetterForwardsFilter] = useState(false);
  const { letterHistory, setLetterHistoryLoading, toggleVisibleLetterHistoryDialog } = useLetter();
  const initRecordsSegmenter = (data) => {
    if (data.length === 0) return [];
    if (data.length === 1) return [{}, data[0], {}];
    if (data.length > 1) return [data[data.length - 2], data[data.length - 1], {}];
    return [];
  };

  //* ////////////////////////////////////////////////////////////////////
  //* -----------------------------memorizes----------------------------//
  //* ////////////////////////////////////////////////////////////////////
  useEffect(() => {
    if (letterHistory) {
      let structuredData = letterHistoryMaker(letterHistory);

      if (checkedLetterForwardsFilter) {
        const allowedTypes = ['forward', 'forward-reverse'];
        structuredData = structuredData
          .map((dayGroup) => {
            const date = Object.keys(dayGroup)[0];
            const items = dayGroup[date].filter((item) => allowedTypes.includes(item.type));
            return items.length > 0 ? { [date]: items } : null;
          })
          .filter(Boolean);
      }

      setLetterHistoryRecords(structuredData);
      setLetterHistoryLoading(false);
      if (isMobile) {
        setMobileLetterHistorySegments(initRecordsSegmenter(structuredData));
      }
    }
  }, [letterHistory, checkedLetterForwardsFilter]);

  useEffect(() => {
    if (letterHistory) {
      const structuredData = letterHistoryMaker(letterHistory);
      setLetterHistoryRecords(structuredData);
      setLetterHistoryLoading(false);
      if (isMobile) {
        setMobileLetterHistorySegments(initRecordsSegmenter(structuredData));
      }
    }
  }, [letterHistory]);

  useEffect(() => {
    const currentIndex = letterHistoryRecords.findIndex(
      (item) => JSON.stringify(item) === JSON.stringify(currentSwipeItem),
    );
    if (touchEndY > touchStartY + 50 || touchEndY < touchStartY - 50) {
      return;
    }
    if (touchEndX < touchStartX) {
      //* Left Swipe - Show right
      if (currentIndex !== letterHistoryRecords.length - 1) {
        handleNextLetterHistory(currentSwipeItem);
      }
    }
    if (touchEndX > touchStartX) {
      //* Right Swipe - Show left
      if (currentIndex !== 0) {
        handlePrivousLetterHistory(currentSwipeItem);
      }
    }
  }, [touchEndX, touchEndY]);

  //* ///////////////////////////////////////////////////////////////////
  //* -----------------------------functions---------------------------//
  //* ///////////////////////////////////////////////////////////////////
  const handleChangeTouchStartX = (e) => {
    setTouchStartX(e.changedTouches[0].screenX);
    setTouchStartY(e.changedTouches[0].screenY);
  };

  const handleChangeTouchEndX = (e, current) => {
    setTouchEndX(e.changedTouches[0].screenX);
    setTouchEndY(e.changedTouches[0].screenY);
    setCurrentSwipeItem(current);
  };

  const toggleCollapseCard = (value) => {
    setCollapseCardId((prevState) => {
      if (prevState === value) return null;
      return value;
    });
  };

  const mobileCardArranger = (current, direction) => {
    const currentIndex = letterHistoryRecords.findIndex(
      (item) => JSON.stringify(item) === JSON.stringify(current),
    );
    //* Left Direction
    if (direction === 'left')
      return [
        letterHistoryRecords[currentIndex - 2] || {},
        letterHistoryRecords[currentIndex - 1],
        letterHistoryRecords[currentIndex] || {},
      ];
    //* Right Direction
    return [
      letterHistoryRecords[currentIndex] || {},
      letterHistoryRecords[currentIndex + 1],
      letterHistoryRecords[currentIndex + 2] || {},
    ];
  };

  const handlePrivousLetterHistory = (current) => {
    setMobileLetterHistorySegments(mobileCardArranger(current, 'left'));
  };

  const handleNextLetterHistory = (current) => {
    setMobileLetterHistorySegments(mobileCardArranger(current, 'right'));
  };

  const handleRenderLayout = () => {
    if (mobile) return <MobileLetterHistory isMobile />;
    if (tablet) return <DesktopLetterHistory />;
    if (desktop) return <DesktopLetterHistory />;
    return <></>;
  };

  //* open and close sidebar
  const handleOpenDocumentListSidebar = (attachments) => {
    setOpenDocListSidebar({
      open: true,
      attachments,
    });
  };

  const handleCloseDocumentListSidebar = () => {
    setOpenDocListSidebar({
      open: false,
      attachments: [],
    });
  };

  const handlePrint = async () => {
    const printable = document.getElementById('printableDiv');
    if (!printable) return;

    const clone = printable.cloneNode(true);

    const iframe = document.createElement('iframe');
    iframe.style.position = 'fixed';
    iframe.style.width = '0';
    iframe.style.height = '0';
    iframe.style.border = '0';
    iframe.style.visibility = 'hidden';
    document.body.appendChild(iframe);

    const iframeWindow = iframe.contentWindow;
    const iframeDoc = iframeWindow.document;

    const customStyles = `
    @page {
      margin: 0;
      size: auto;
    }

    * {
      box-sizing: border-box;
      -webkit-print-color-adjust: exact !important;
      print-color-adjust: exact !important;
      color-adjust: exact !important;
    }

    body {
      font-family: 'Vazir', sans-serif;
      margin: 0;
      padding: 24px;
      direction: rtl;
      color: #000;
    }

    .topBox {
      border: 1px solid #999;
      padding: 12px 16px;
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: bold;
      text-align: right;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      font-size: 12px;
      border: 1px solid #1C1B1F !important;
    }

    thead th {
      background-color: #EFEFEF !important;
      color: white !important;
      padding: 4px 8px !important;
      white-space: nowrap;
      font-weight: bold;
    }

    th, td {
      border-top: 1px solid #999;
      border-bottom: 1px solid #999;
      padding: 8px 6px;
      text-align: right;
      vertical-align: top;
    }

    tbody tr:nth-of-type(even) {
      background-color: #F8F8F8 !important;
    }

    tbody tr:nth-of-type(odd) {
      background-color: #fff !important;
    }

    .bold {
      font-weight: bold;
    }
  `;

    // Write the initial document
    iframeDoc.open();
    iframeDoc.write(`
    <!DOCTYPE html>
    <html lang="fa" dir="rtl">
      <head>
        <meta charset="UTF-8" />
        <title>Print</title>
        <style>
         @font-face {
            font-family: 'Vazir';
            src: url('data:font/woff2;charset=utf-8;base64,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') format('woff2');
            font-weight: normal;
            font-style: normal;
            }
        </style>
        <style>${customStyles}</style>
      </head>
      <body></body>
    </html>
  `);
    iframeDoc.close();

    iframe.onload = () => {
      iframeDoc.body.appendChild(clone);

      const style = iframeDoc.createElement('style');
      style.textContent = `
      @media print {
        thead th {
          background-color: #EFEFEF !important;
          color: #1C1B1F !important;
          -webkit-print-color-adjust: exact;
        }
        tbody tr:nth-of-type(even) {
          background-color: #F8F8F8 !important;
        }
        tbody tr:nth-of-type(odd) {
          background-color: #fff !important;
        }
      }
    `;
      iframeDoc.head.appendChild(style);

      const cleanup = () => {
        iframeWindow.removeEventListener('afterprint', cleanup);
        document.body.removeChild(iframe);
      };

      iframeWindow.addEventListener('afterprint', cleanup);

      // ✅ Ensure font is ready before printing
      iframeDoc.fonts.ready
        .then(() => {
          iframeWindow.focus();
          iframeWindow.print();
        })
        .catch((err) => {
          console.error('Font loading failed:', err);
          iframeWindow.focus();
          iframeWindow.print();
        });
    };
  };

  return (
    <LetterHistoryContext.Provider
      value={{
        handlePrivousLetterHistory,
        handleNextLetterHistory,
        collapseCardId,
        toggleCollapseCard,
        letterHistoryRecords,
        mobileLetterHistorySegments,
        handleChangeTouchStartX,
        handleChangeTouchEndX,
        openDocListSidebar,
        handleOpenDocumentListSidebar,
        handleCloseDocumentListSidebar,
        checkedLetterForwardsFilter,
        setCheckedLetterForwardsFilter,
        handlePrint,
      }}
    >
      {handleRenderLayout()}
    </LetterHistoryContext.Provider>
  );
};

export default LetterHistoryDialog;
