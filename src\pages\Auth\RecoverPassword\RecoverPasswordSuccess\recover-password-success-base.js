import PropTypes from 'prop-types';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Container from '@mui/material/Container';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import SocialMedia from '../../../../components/SocialMedia';
import logo from '../../../../assets/images/logo/Logo.svg';
import { useMainLayoutStyles } from '../../../../contexts/mainLayoutStylesContext';
import { useRecoverPasswordContext } from '../../../../contexts/pageContext/auth/recoverPasswordContext';

const useStyles = makeStyles()((theme) => ({
  root: {
    width: '100%',
    height: '100%',
    position: 'relative',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    minHeight: 650,
  },
  mobileRoot: {
    position: 'relative',
    textAlign: 'center',
    flexGrow: 1,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  },
  mobileMiddlePaper: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '24px 19px 44.2px 19px',
    margin: 'auto',
    marginTop: 0,
  },
  logo: {
    display: 'flex',
    margin: '16px auto',
  },
  successIcon: {
    color: '#00c89c',
    fontSize: '5rem',
  },
  infoBox: {
    backgroundImage:
      'linear-gradient(to right, #d1d1d1 60%, transparent 50%), linear-gradient(to right, #d1d1d1 60%, transparent 60%), linear-gradient(to bottom, #d1d1d1 60%, transparent 60%), linear-gradient(to bottom, #d1d1d1 60%, transparent 60%)',
    backgroundPosition: 'left top, left bottom, left top, right top',
    backgroundRepeat: ' repeat-x, repeat-x, repeat-y, repeat-y',
    backgroundSize: '20px 1px, 20px 1px, 1px 20px, 1px 20px',
    display: 'flex',
    padding: 10,
    justifyContent: 'space-around',
    flexDirection: 'column',
    listStyle: 'none',
    width: '85%',
    borderRadius: 10,
    margin: theme.spacing(2, 0),
    '& li': {
      display: 'flex',
      justifyContent: 'space-between',
      fontSize: '0.875rem',
      padding: '0 16px',
      lineHeight: 2,
      fontWeight: 500,
      color: theme.palette.text.secondary,
    },
  },
  infoText: {
    color: theme.palette.text.gray,
  },
  marginTop2: {
    marginTop: theme.spacing(2),
  },
  marginTop5: {
    marginTop: theme.spacing(5),
  },
}));

const BaseRecoverPasswordSuccess = ({ isMobile }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslation();
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes: globalClasses } = makeStyles()(stylesGenerator)();

  const { goToLogin } = useRecoverPasswordContext();

  return (
    <Container
      component="div"
      maxWidth="xs"
      className={isMobile ? classes.mobileRoot : classes.root}
    >
      <div className={isMobile ? classes.mobileMiddlePaper : globalClasses.middlePaper}>
        <img src={logo} alt="Logo" className={classes.logo} />
        <form onSubmit={goToLogin}>
          <CheckCircleOutlineIcon className={cx(classes.marginTop5, classes.successIcon)} />
          <Typography variant="h6" className={cx(globalClasses.bold)}>
            {t('auth.messages.successRecoverPassword')}
          </Typography>
          <Button type="submit" variant="contained" fullWidth className={classes.marginTop5}>
            {t('auth.labels.goToLoginAfterRecoverPassword')}
          </Button>
        </form>
      </div>
      <SocialMedia />
    </Container>
  );
};
BaseRecoverPasswordSuccess.propTypes = {
  isMobile: PropTypes.bool,
};

BaseRecoverPasswordSuccess.defaultProps = {
  isMobile: false,
};

export default BaseRecoverPasswordSuccess;
