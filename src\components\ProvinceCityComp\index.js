import PropTypes from 'prop-types';
import { makeStyles } from 'tss-react/mui';
import { useTranslation } from 'react-i18next';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import AWAutocomplete from 'components/AWComponents/AWAutoComplete';

const listOfCities = require('../../common/cities.json');

const useStyles = makeStyles()((theme) => ({
  addressTitle: {
    marginRight: theme.spacing(3),
    marginTop: theme.spacing(3),
    color: theme.palette.text.gray,
    width: '100%',
    fontSize: theme.spacing(1.75),
    fontWeight: 700,
  },
  paddingTop: {
    paddingTop: '12px !important',
  },
}));

const ProvinceCityComp = ({ showTittle, state, onChangeAutocomplete }) => {
  const { t } = useTranslation();
  const { classes } = useStyles();

  const onChangeAutocompleteProvince = (e, newValue) => {
    // if (!newValue) {
    onChangeAutocomplete('city', e, '');
    // }
    onChangeAutocomplete('province', e, newValue);
  };

  return (
    <>
      {showTittle && <Typography className={classes.addressTitle}>- اطلاعات تماس</Typography>}
      <Grid item xs={12}>
        <AWAutocomplete
          fullWidth
          noOptionsText={t('common.messages.noOptions')}
          name="province"
          options={Object.keys(listOfCities)}
          getOptionLabel={(option) => option || ''}
          value={state?.province || ''}
          onChange={(e, newValue) => onChangeAutocompleteProvince(e, newValue)}
          // eslint-disable-next-line react/jsx-props-no-spreading
          renderInput={(params) => <TextField {...params} label={t('profile.labels.province')} />}
        />
      </Grid>
      <Grid item xs={12}>
        <AWAutocomplete
          fullWidth
          noOptionsText={t('common.messages.noOptions')}
          name="city"
          disabled={!state?.province}
          options={state?.province ? listOfCities[state.province] : {}}
          getOptionLabel={(option) => option || ''}
          value={state?.city || ''}
          onChange={(e, newValue) => onChangeAutocomplete('city', e, newValue)}
          // eslint-disable-next-line react/jsx-props-no-spreading
          renderInput={(params) => <TextField {...params} label={t('profile.labels.city')} />}
        />
      </Grid>
    </>
  );
};
ProvinceCityComp.propTypes = {
  state: PropTypes.shape({
    province: PropTypes.string,
    city: PropTypes.string,
  }).isRequired,
  onChangeAutocomplete: PropTypes.func.isRequired,
  showTittle: PropTypes.bool.isRequired,
};
export default ProvinceCityComp;
