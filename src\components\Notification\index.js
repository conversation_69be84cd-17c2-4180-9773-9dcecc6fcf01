import { useEffect, useRef } from 'react';
import { connect } from 'react-redux';
import { toast } from 'react-toastify';
import { useMediaQuery } from '@mui/material';
import { useAuth } from '../../contexts/authContext';
import { useUser } from '../../contexts/userContext';
import {
  newNotificationsAsync as newNotificationsAsync_,
  shiftOldestNotification as shiftOldestNotification_,
  readNotificationAsync as readNotificationAsync_,
  getUnReadNotificationsCountAsync as getUnReadNotificationsCountAsync_,
} from '../../pages/notifications/notificationSlice';
import ToastNotification from './toast-notification';
import handleBrowserNotification from './browser-notification';

const NotificationsProvider = ({
  newNotificationsAsync,
  notificationsAsync,
  shiftOldestNotification,
  notification,
  readNotificationAsync,
  getUnReadNotificationsCountAsync,
}) => {
  const toastId = useRef(null);
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const { signedInUser } = useUser();
  const { auth } = useAuth();

  useEffect(() => {
    if (!auth) return;
    const options = {
      path: `${signedInUser?.id}`,
      queryObject: { limit: 10, offset: 0, unread: true },
    };

    getUnReadNotificationsCountAsync(options);

    const interval = setInterval(() => {
      newNotificationsAsync({
        path: `?userId=${signedInUser?.id}`,
      });
    }, 30000);

    return () => clearInterval(interval);
  }, [signedInUser?.id, auth]);

  const ToastNotificationCaller = () => {
    if (notification.newNotifications?.data?.length) {
      if (!document.hidden) {
        toastId.current = toast(<ToastNotification readNotification={readNotificationAsync} />, {
          data: notification.newNotifications.data,
          onClose: () => shiftOldestNotification(),
        });
      }

      // Check if the browser supports service workers and notifications
      if (mobile || document.hidden) {
        handleBrowserNotification(notification.newNotifications.data, shiftOldestNotification);
      }
    }
  };

  useEffect(() => {
    ToastNotificationCaller();
  }, [notification.newNotifications.data]);

  return <></>;
};

const mapStateToProps = (state) => {
  const { notification } = state;
  return { notification };
};

export default connect(mapStateToProps, {
  newNotificationsAsync: newNotificationsAsync_,
  shiftOldestNotification: shiftOldestNotification_,
  readNotificationAsync: readNotificationAsync_,
  getUnReadNotificationsCountAsync: getUnReadNotificationsCountAsync_,
})(NotificationsProvider);
