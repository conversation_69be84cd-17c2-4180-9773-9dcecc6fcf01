import { useBasePath } from 'hooks/useBasePath ';
import { useTranslation } from 'react-i18next';
import { matchRoutes, useLocation, useParams } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import routes from 'routes';

const useExtractPath = ({ organization, secretariats, letters, groups }: any) => {
  const location = useLocation();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const { search } = location;
  const { t } = useTranslation();

  //* Base Path
  const basePath = useBasePath();

  //* Params
  const {
    organizationId,
    secretariatId,
    indicatorId,
    letterId,
    layoutId,
    solutionTariff,
    orderId,
    invoiceId,
    groupId,
  } = params;

  const paramsList = Object.keys(params); //* example: ['organizationId', 'secretariatId',...]

  //* Query Search Params
  // const querySearchParams: object[] = [];
  const querySearchParamsKeys: string[] = [];
  searchParams.forEach((value, key) => {
    // querySearchParams.push({ [key]: value });
    querySearchParamsKeys.push(key);
  });

  //* Type of letter
  const typeOfLetter = new URLSearchParams(search).get('type');

  //* Match Current Route Properties
  const useCurrentRoute = () => {
    const matches = matchRoutes(routes, location.pathname);
    return matches && matches[0].route;
  };

  //* Find Current Route -> organizations/:organizationId/....
  const currentRoute = useCurrentRoute();

  //* Make Current Path -> organizations_organizationId_....
  let currentPath =
    currentRoute?.path
      .split('/')
      .filter((item: string) => item)
      .map((str: string) => (str.startsWith(':') ? str.substring(1) : str))
      .join('_') || '';

  //* Exceptions
  if (currentRoute?.breadcrumbsInfo) {
    const { breadcrumbsInfo } = currentRoute;
    const { name } = breadcrumbsInfo;

    //* Create new letter
    if (name === 'new-letter') {
      currentPath += `-${typeOfLetter}`;
    }

    // //* Edit Letter
    else if (name === 'edit-letter' || name === 'letter-receipts') {
      const newPath = currentPath.split('_');
      const letterIdIndex = newPath.findIndex((item) => item === 'letterId');
      newPath.splice(letterIdIndex, 1, typeOfLetter ?? 'letterId');
      currentPath = newPath.join('_');
    }

    //* Payment Result
    else if (name === 'payment-result') {
      const newPath = currentPath.split('_');
      currentPath = newPath.slice(newPath.length - 2).join('_');
    }

    //* Letter Layout
    else if (name === 'letter-layout') {
      const newPath = currentPath.split('_');
      currentPath = newPath.filter((item) => item !== 'letter-layout').join('_');
    }
  }

  // ? ////////////////////////////////////////////////////////////////////////

  //* Replace value instead of params
  const replaceSegmentValue = (segment: string) => {
    switch (segment) {
      case 'organizationId':
        return organization?.selectedOrganization?.displayName;
      case 'secretariatId':
        return secretariats?.secretariatsList?.data?.find((s: any) => s._id === secretariatId)
          ?.name;
      case 'indicatorId':
        return secretariats?.indicatorsList?.data?.find(
          (s: any) => s._id === indicatorId || searchParams.get(segment),
        )?.name;
      case 'layoutId':
        return secretariats?.letterLayoutsList?.data?.find((s: any) => s.layoutId === layoutId)
          ?.name;
      case 'letterId':
        return letters?.selectedLetter?.data?.subject;
      case 'solutionTariff':
        return t(`breadcrumbs.segments.${solutionTariff}`);
      case 'orderId':
        return t(`breadcrumbs.segments.orderId`);
      case 'invoiceId':
        return t(`breadcrumbs.segments.invoiceId`);
      case 'employeeId':
        return t(`breadcrumbs.segments.employeeId`);
      case 'contactId':
        return t(`breadcrumbs.segments.contactId`);
      case 'groupId':
        return groups?.accessGroupList?.data?.find?.((s: any) => s.id === groupId)?.title ?? '';
      default:
        return segment;
    }
  };

  //* Replace id or type instead of value of params
  const replaceSegmentId = (segment: string) => {
    switch (segment) {
      case 'organizationId':
        return organizationId;
      case 'secretariatId':
        return secretariatId;
      case 'indicatorId':
        return indicatorId;
      case 'letterId':
      case typeOfLetter:
        return letterId;
      case 'layoutId':
        return layoutId;
      case 'solutionTariff':
        return solutionTariff;
      case 'orderId':
        return orderId;
      case 'invoiceId':
        if (basePath === '/organizations/solutions/payment') {
          return location.state.orderId;
        }
        return invoiceId;
      default:
        return segment;
    }
  };

  const segmentPathAddress = (path: string) => {
    const splitPath = path.split('_');
    const res = splitPath.map((item, index) =>
      splitPath
        .slice(0, index + 1)
        .map((purePath) => replaceSegmentId(purePath))
        .join('/'),
    );
    if (organizationId) {
      return res.slice(1, splitPath.length); //* from 1 because we wanna ignore `organizations`
    }
    return res;
  };

  const translateSegment = () => {
    let splitPath = currentPath.split('_');
    if (organizationId) {
      splitPath = splitPath?.slice(1, splitPath?.length);
    }
    const res = splitPath?.map((item) => {
      if (paramsList.includes(item)) {
        return replaceSegmentValue(item);
      }
      return t(`breadcrumbs.segments.${item}`);
    });
    return res;
  };

  const replaceValueInTranslated = (translatedList: string[]) => {
    const res = translatedList.map((item) => {
      let segment = item;

      [...paramsList, ...querySearchParamsKeys].forEach((param) => {
        const regex = new RegExp(param, 'g');
        segment = segment?.replace(regex, replaceSegmentValue(param) ?? '');
      });
      return segment;
    });
    return res;
  };

  //* Examples:
  //* urlPathSegments --> ["organizations/c855a9b4-1595-4b10-9792-33fcebf6b1bf", "organizations/c855a9b4-1595-4b10-9792-33fcebf6b1bf/correspondence"]
  //* translatedPathSegments --> ['نام سازمان', 'مکاتبات', 'دبیرخانه‌ها']

  const extractedPath = {
    urlPathSegments: segmentPathAddress(currentPath),
    translatedPathSegments: replaceValueInTranslated(translateSegment()),
  };

  return extractedPath;
};

export default useExtractPath;
