import * as React from 'react';
import { SvgIcon as MuiSvgIcon, SvgIconProps } from '@mui/material';
import { withStyles } from 'tss-react/mui';

const SvgIcon = withStyles(MuiSvgIcon, {
  root: {
    fill: 'none',
    stroke: 'currentColor',
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    strokeWidth: '0px',
    width: 17,
    height: 18,
  },
});

const defaultProps = {
  viewBox: '0 0 17 18',
  width: 17,
  height: 18,
  focusable: false,
  'aria-hidden': true,
};

const DeleteIcon: React.FunctionComponent<SvgIconProps> = (props) => (
  <SvgIcon {...defaultProps} {...props}>
    <path
      d="M5.525 13.525L8.5 10.5L11.5 13.525L12.675 12.325L9.7 9.3L12.675 6.275L11.5 5.075L8.5 8.1L5.525 5.075L4.325 6.275L7.325 9.3L4.325 12.325L5.525 13.525ZM3.025 18C2.625 18 2.275 17.85 1.975 17.55C1.675 17.25 1.525 16.9 1.525 16.5V2.25H0.5V0.75H5.2V0H11.8V0.75H16.5V2.25H15.475V16.5C15.475 16.9 15.325 17.25 15.025 17.55C14.725 17.85 14.375 18 13.975 18H3.025ZM13.975 2.25H3.025V16.5H13.975V2.25ZM3.025 2.25V16.5V2.25Z"
      fill="#FF4C30"
    />
  </SvgIcon>
);

export default DeleteIcon;
