import React from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import DesktopSignUpSuccess from './sign-up-success-desktop';
import TabletSignUpSuccess from './sign-up-success-tablet';
import MobileSignUpSuccess from './sign-up-success-mobile';

const SignUpSuccess = () => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));

  if (mobile) {
    return <MobileSignUpSuccess isMobile />;
  }
  if (tablet) {
    return <TabletSignUpSuccess />;
  }
  if (desktop) {
    return <DesktopSignUpSuccess />;
  }
  return <></>;
};

export default SignUpSuccess;
