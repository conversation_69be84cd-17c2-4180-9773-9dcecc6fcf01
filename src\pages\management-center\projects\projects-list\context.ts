import { createContext, useContext } from 'react';
import { MediaProjectsListType } from './type';

const defaultValue: MediaProjectsListType = {
  ProjectsDialog: false,
  handleOpenProjectsDialog: () => {},
  handleCloseProjectsDialog: () => {},
  hasPosition: false,
  dataList: [],
  changePage: () => {},
  meta: {
    total: 0,
    offset: 0,
    limit: 0,
  },
  loading: false,
  handleNextPage: () => {},
  handlePrevPage: () => {},
  handleProjectCollapseInfo: () => {},
  reOrderMembers: () => [],
  projectIndexValue: {},
  isPending: false,
  resetAndGetProjectsList: () => {},
  handleSelectProject: () => {},
  handleChangeLimit: () => {},
};
export const ProjectsListContext = createContext(defaultValue);

export const useProjectsListContext = () => useContext(ProjectsListContext);
