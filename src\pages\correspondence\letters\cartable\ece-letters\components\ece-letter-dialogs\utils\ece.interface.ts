import { XmlField } from './xml-field.interface';

export class Ece {
  $: any;

  Origins: { Origin: XmlField[] }[];

  Attachments: { Attachment: XmlField[] }[];

  Receiver: XmlField[];

  OtherReceivers: { OtherReceiver: XmlField[] }[];

  Sender: XmlField[];

  Subject: XmlField[];

  RelatedLetters: {
    RelatedLetter: {
      RelationType: XmlField[];
      RelatedLetterNo: XmlField[] | string[];
      RelatedLetterDateTime: XmlField[] | string[];
      RelatedLetterSender: XmlField[];
    }[];
  }[];

  Priority: XmlField[];

  Classification: XmlField[];

  LetterNo: XmlField[] | string[];

  LetterDateTime: XmlField[] | string[];
}
