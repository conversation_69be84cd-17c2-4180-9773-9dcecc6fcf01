import PropTypes from 'prop-types';
import Grid from '@mui/material/Grid';
import Typography from '@mui/material/Typography';
import { makeStyles } from 'tss-react/mui';
import ArrowDropDown from '@mui/icons-material/ArrowDropDown';
import Add from '@mui/icons-material/Add';
import Paper from '@mui/material/Paper';
import Chip from '@mui/material/Chip';
import { t } from 'i18next';

import { contactInfo } from 'utils/contact-info';
import DocumentViewer from 'components/document-previewer';
import PositionEmployeeInput from './position-employee-input';
import LastForward from './last-forward';
import { useLetter } from '../../../../../contexts/pageContext/letters/letterContext';
import AWTextField from '../../../../../components/AWComponents/AWTextField';
import AWAutocomplete from '../../../../../components/AWComponents/AWAutoComplete';
import AWButton from '../../../../../components/AWComponents/AWButton';
import AWBox from '../../../../../components/AWComponents/AWBox';
import CcDescription from './ccDescription';
import AWDivider from '../../../../../components/AWComponents/AWDivider';
import LetterBody from './letter-body';

const useStyles = makeStyles()((theme) => ({
  autoComplete: {
    '& .MuiInputBase-root': {
      height: 'unset',
      paddingLeft: '64px !important',
    },
  },
  autoCompleteRecipientBox: {
    display: 'flex',
    '& .MuiAutocomplete-root': {
      flex: 1,
    },
    '& .bcc': {
      // background: '#faf6ff',
      color: theme.palette.background.secondColor,
      borderRadius: 7,
      padding: '8px 12px',
      margin: '0 8px 0 0',
      [theme.breakpoints.down('sm')]: {
        padding: '20px 15px',
      },
      '&.Mui-disabled': {
        color: theme.palette.text.lightGray,
      },
    },
  },
  textArea: {
    height: 'unset',
  },
  marginTop2: {
    marginTop: theme.spacing(2),
  },
  editorRoot: {
    border: '1px solid rgba(0, 0, 0, 0.23)',
    borderRadius: 8,
    height: '100%',
    minHeight: 'inherit',
    '& ol,ul': {
      paddingInlineStart: '40px',
    },
    '& ol': {
      // background: 'red !important'
      listStyle: 'number',
    },
    '& ul': {
      listStyle: 'disc',
    },
    // '& div,h1,h2,h3,h4,h5,h6': {
    //   margin: 'inherit !important',
    //   padding: 'inherit !important',
    //   // border: 0;
    //   // font- size: 100%;
    //   // font: inherit;
    //   // vertical- align: baseline;
    // },
  },
  editorPlaceHolder: {
    padding: 5,
    height: 'calc(100% - 35px)',
    zIndex: 9,
    [theme.breakpoints.down('sm')]: {
      height: 'calc(100% - 80px)',
    },
  },
  editorContainer: {
    margin: 0,
    height: '100%',
    minHeight: 'inherit',
  },
  editorToolbar: {
    background: '#f3f3f3',
    borderBottom: `1px solid ${theme.palette.text.lightGray}`,
    zIndex: 9,
    '& .MuiIconButton-root': {
      // color: theme.palette.text.gray,
      fontSize: '1rem',
      padding: 8,
      '& .MuiSvgIcon-root': {
        width: '0.8em',
        height: '0.8em',
      },
    },
  },
  editor: {
    padding: 5,
    height: 'calc(100% - 35px)',
    outline: 'none',
    position: 'absolute',
    width: '100%',
    top: 35,
    // overflowX: 'hidden',
    [theme.breakpoints.down('sm')]: {
      top: 'unset',
      height: 'calc(100% - 70px)',
    },
  },
  rootGrid: {
    flex: 1,
    flexWrap: 'nowrap',
    flexDirection: 'column',
    [theme.breakpoints.down('sm')]: {
      marginTop: 0,
      height: '100% !important',
    },
  },
  recipientBox: {
    flex: 1,
    minWidth: 0,
    [theme.breakpoints.down('sm')]: {
      minWidth: '100%',
    },
  },
  itemGrid: {
    flexDirection: 'unset',
    flexBasis: 'auto',
    flexGrow: 'unset',
  },
  itemGridRichText: {
    display: 'flex',
    flexDirection: 'column',
    flexGrow: 1,
    minHeight: 270,
    flex: 1,
    // maxHeight: 'calc(100vh - 467px)',
    marginBottom: 0,
    [theme.breakpoints.down('sm')]: {
      minHeight: 250,
      marginBottom: 16,
    },
  },
  editorDisabled: {
    opacity: '0.5',
    cursor: 'default',
  },
}));

const MainInfo = ({ isMobile }) => {
  const {
    isInvalid,
    getValidationError,
    typeOfLetter,
    users,
    contacts,
    state,
    initState,
    onChange,
    showBcc,
    handleShowBcc,
    senderSelection,
    handleChangeSender,
    recipientSelection,
    handleChangeRecipient,
    ccSelection,
    handleChangeCc,
    bccSelection,
    handleChangeBcc,
    handleChangeBody,
    isLetterEditable,
    openPositionEmployeeTreeDialogRecipient,
    handleOpenPositionEmployeeTreeDialogRecipient,
    handleClosePositionEmployeeTreeDialogRecipient,
    openPositionEmployeeTreeDialogSender,
    handleOpenPositionEmployeeTreeDialogSender,
    handleClosePositionEmployeeTreeDialogSender,
    organizationId,
    openPositionEmployeeTreeDialogCC,
    openPositionEmployeeTreeDialogBCC,
    handleOpenPositionEmployeeTreeDialogCC,
    handleClosePositionEmployeeTreeDialogCC,
    handleOpenPositionEmployeeTreeDialogBCC,
    handleClosePositionEmployeeTreeDialogBCC,
    operationInfo,
    extraItems,
    security,
    createLetterSampleState,
    setCreateLetterSampleState,
    handleCreateLetterSample,
    openForCreateLetterSampleDialog,
    handleOpenCreateLetterSampleDialog,
    handleCloseCreateLetterSampleDialog,
    loadingCreateLetterSample,
    letterSamplesListAnchorElAnchorEl,
    handleOpenLetterSamplesListPopover,
    handleCloseLetterSamplesPopover,
    correspondenceSamplesListLoading,
    correspondenceSamplesList,
    deleteCorrespondenceSample,
    applyCorrespondenceSample,
    deleteCorrespondenceSampleLoading,
    openLetterAiAnchorElement,
    handleCloseLetterAiPopover,
    handleClickLetterAiPopover,
    letterAiAnchorEl,
    selectedTabCreateLetterByTopic,
    handleToggleChangeTabGenai,
    setTextFieldGenai,
    submitCreateLetterByTopic,
    genaiLoading,
    textFieldGenai,
  } = useLetter();
  const { classes, cx } = useStyles();

  return (
    <Grid container spacing={2} classes={{ root: classes.rootGrid }} gap={2}>
      <Grid item container gap={2}>
        <Grid item flexGrow={1}>
          {typeOfLetter !== 'incoming' ? (
            <PositionEmployeeInput
              organizationId={organizationId}
              label={t('forwardLetter.labels.sender')}
              name="sender"
              isSingleSelection
              selectedPositionEmployee={senderSelection}
              handleChangeAutocomplete={handleChangeSender}
              openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogSender}
              handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogSender}
              handleClosePositionEmployeeViewerDialog={handleClosePositionEmployeeTreeDialogSender}
              onSubmitPositionEmployee={handleChangeSender}
              isMobile={isMobile}
              isLetterEditable={isLetterEditable}
              isInvalid={isInvalid}
              getValidationError={getValidationError}
              showFreePosition={false}
            />
          ) : (
            <AWAutocomplete
              noOptionsText={t('common.messages.noOptions')}
              id="sender"
              name="sender"
              value={senderSelection || null}
              onChange={(event, newValue) => handleChangeSender(event, newValue)}
              options={typeOfLetter !== 'incoming' ? users : contacts}
              getOptionLabel={(option) => contactInfo(option)}
              PaperComponent={SenderPaperComponent}
              filterSelectedOptions
              disabled={isLetterEditable()}
              renderInput={(params) => (
                <AWTextField
                  // eslint-disable-next-line react/jsx-props-no-spreading
                  {...params}
                  label={t('forwardLetter.labels.sender')}
                  variant="outlined"
                  error={isInvalid('sender')}
                  helperText={getValidationError('sender') || ''}
                />
              )}
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip label={contactInfo(option)} {...getTagProps({ index })} />
                ))
              }
            />
          )}
        </Grid>

        <Grid item container xs={12} classes={{ root: classes.itemGrid }}>
          {typeOfLetter === 'outgoing' ? (
            <Grid item container xs={12} gap={0.5}>
              <Grid item flexGrow={1}>
                <AWAutocomplete
                  noOptionsText={t('common.messages.noOptions')}
                  multiple
                  id="recipient"
                  name="recipient"
                  options={typeOfLetter !== 'outgoing' ? users : contacts}
                  value={recipientSelection}
                  onChange={handleChangeRecipient}
                  getOptionLabel={(option) => contactInfo(option)}
                  PaperComponent={RecipientPaperComponent}
                  filterSelectedOptions
                  className={classes.autoComplete}
                  disabled={isLetterEditable()}
                  renderInput={(params) => (
                    <AWTextField
                      // eslint-disable-next-line react/jsx-props-no-spreading
                      {...params}
                      variant="outlined"
                      label={t('forwardLetter.labels.recipient')}
                      error={isInvalid('recipient')}
                      helperText={getValidationError('recipient') || ''}
                    />
                  )}
                  renderTags={(value, getTagProps) =>
                    value.map((option, index) => (
                      <Chip label={contactInfo(option)} {...getTagProps({ index })} />
                    ))
                  }
                />
              </Grid>
              <AWButton
                startIcon={<ArrowDropDown />}
                variant="outlined"
                disabled={showBcc}
                onClick={handleShowBcc}
                className="bcc"
              >
                <Typography>{t('forwardLetter.labels.transcript')}</Typography>
              </AWButton>
            </Grid>
          ) : (
            <>
              <Grid item container xs={12} gap={0.5}>
                <Grid item flexGrow={1}>
                  <PositionEmployeeInput
                    organizationId={organizationId}
                    label={t('forwardLetter.labels.recipients')}
                    name="recipient"
                    selectedPositionEmployee={recipientSelection}
                    handleChangeAutocomplete={handleChangeRecipient}
                    openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogRecipient}
                    handleOpenPositionEmployeeViewerDialog={
                      handleOpenPositionEmployeeTreeDialogRecipient
                    }
                    handleClosePositionEmployeeViewerDialog={
                      handleClosePositionEmployeeTreeDialogRecipient
                    }
                    onSubmitPositionEmployee={handleChangeRecipient}
                    isMobile={isMobile}
                    isLetterEditable={isLetterEditable}
                    isInvalid={isInvalid}
                    getValidationError={getValidationError}
                    showFreePosition={false}
                  />
                </Grid>
                <AWButton
                  startIcon={<ArrowDropDown />}
                  variant="outlined"
                  disabled={showBcc}
                  onClick={handleShowBcc}
                  className="bcc"
                >
                  <Typography>{t('forwardLetter.labels.transcript')}</Typography>
                </AWButton>
              </Grid>
            </>
          )}
        </Grid>
        {showBcc && (
          <>
            <Grid item container gap={0.5} classes={{ root: classes.itemGrid }}>
              <Grid item xs={12}>
                {typeOfLetter === 'outgoing' ? (
                  <AWAutocomplete
                    noOptionsText={t('common.messages.noOptions')}
                    multiple
                    id="cc"
                    name="cc"
                    value={ccSelection}
                    onChange={handleChangeCc}
                    options={typeOfLetter !== 'outgoing' ? users : contacts}
                    getOptionLabel={(option) => contactInfo(option)}
                    PaperComponent={RecipientPaperComponent}
                    filterSelectedOptions
                    className={classes.autoCompleteRecipient}
                    disabled={isLetterEditable()}
                    renderInput={(params) => (
                      <AWTextField
                        // eslint-disable-next-line react/jsx-props-no-spreading
                        {...params}
                        variant="outlined"
                        label={t('secretariats.labels.cc')}
                        error={isInvalid('cc')}
                        helperText={getValidationError('cc') || ''}
                      />
                    )}
                  />
                ) : (
                  <PositionEmployeeInput
                    organizationId={organizationId}
                    placeholder={t('secretariats.labels.cc')}
                    name="cc"
                    selectedPositionEmployee={ccSelection}
                    handleChangeAutocomplete={handleChangeCc}
                    openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogCC}
                    handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogCC}
                    handleClosePositionEmployeeViewerDialog={
                      handleClosePositionEmployeeTreeDialogCC
                    }
                    onSubmitPositionEmployee={handleChangeCc}
                    isMobile={isMobile}
                    isLetterEditable={isLetterEditable}
                    isInvalid={isInvalid}
                    getValidationError={getValidationError}
                    showFreePosition={false}
                    hiddenSelectedItem
                  />
                )}
              </Grid>

              <Grid item xs={12}>
                <CcDescription
                  disabled={isLetterEditable()}
                  onChange={handleChangeCc}
                  rows={ccSelection}
                  isMobile={isMobile}
                />
              </Grid>
            </Grid>
            <Grid item xs={12} container classes={{ root: classes.itemGrid }}>
              {typeOfLetter === 'outgoing' ? (
                <AWAutocomplete
                  noOptionsText={t('common.messages.noOptions')}
                  multiple
                  fullWidth
                  id="bcc"
                  name="bcc"
                  value={bccSelection}
                  onChange={handleChangeBcc}
                  options={typeOfLetter !== 'outgoing' ? users : contacts}
                  getOptionLabel={(option) => contactInfo(option)}
                  PaperComponent={RecipientPaperComponent}
                  filterSelectedOptions
                  className={classes.autoComplete}
                  disabled={isLetterEditable()}
                  renderInput={(params) => (
                    <AWTextField
                      // eslint-disable-next-line react/jsx-props-no-spreading
                      {...params}
                      variant="outlined"
                      label={t('secretariats.labels.hiddenCc')}
                      error={isInvalid('bcc')}
                      helperText={getValidationError('bcc') || ''}
                    />
                  )}
                />
              ) : (
                <PositionEmployeeInput
                  organizationId={organizationId}
                  label={t('secretariats.labels.hiddenCc')}
                  name="bcc"
                  selectedPositionEmployee={bccSelection}
                  handleChangeAutocomplete={handleChangeBcc}
                  openPositionEmployeeViewerDialog={openPositionEmployeeTreeDialogBCC}
                  handleOpenPositionEmployeeViewerDialog={handleOpenPositionEmployeeTreeDialogBCC}
                  handleClosePositionEmployeeViewerDialog={handleClosePositionEmployeeTreeDialogBCC}
                  onSubmitPositionEmployee={handleChangeBcc}
                  isMobile={isMobile}
                  isLetterEditable={isLetterEditable}
                  isInvalid={isInvalid}
                  getValidationError={getValidationError}
                  showFreePosition={false}
                />
              )}
            </Grid>
          </>
        )}
        <Grid item xs={12} container classes={{ root: classes.itemGrid }}>
          <AWTextField
            id="subject"
            name="subject"
            fullWidth
            label={`${t('letter.eceLetters.subject')}*` || ''}
            variant="outlined"
            value={state.subject}
            onChange={onChange}
            error={isInvalid('subject')}
            helperText={getValidationError('subject') || ''}
            disabled={isLetterEditable()}
          />
        </Grid>
      </Grid>
      <Grid key={initState?.body} item classes={{ root: classes.itemGridRichText }}>
        <LetterBody
          disabled={isLetterEditable()}
          value={state?.body}
          onChange={handleChangeBody}
          createLetterSampleState={createLetterSampleState}
          setCreateLetterSampleState={setCreateLetterSampleState}
          onCreateLetterSample={handleCreateLetterSample}
          openForCreateLetterSampleDialog={openForCreateLetterSampleDialog}
          onOpenCreateLetterSampleDialog={handleOpenCreateLetterSampleDialog}
          onCloseCreateLetterSampleDialog={handleCloseCreateLetterSampleDialog}
          loadingCreateLetterSample={loadingCreateLetterSample}
          security={security}
          letterSamplesListAnchorElAnchorEl={letterSamplesListAnchorElAnchorEl}
          handleOpenLetterSamplesListPopover={handleOpenLetterSamplesListPopover}
          handleCloseLetterSamplesPopover={handleCloseLetterSamplesPopover}
          correspondenceSamplesListLoading={correspondenceSamplesListLoading}
          correspondenceSamplesList={correspondenceSamplesList}
          deleteCorrespondenceSample={deleteCorrespondenceSample}
          applyCorrespondenceSample={applyCorrespondenceSample}
          deleteCorrespondenceSampleLoading={deleteCorrespondenceSampleLoading}
          openLetterAiAnchorElement={openLetterAiAnchorElement}
          handleCloseLetterAiPopover={handleCloseLetterAiPopover}
          handleClickLetterAiPopover={handleClickLetterAiPopover}
          letterAiAnchorEl={letterAiAnchorEl}
          selectedTabCreateLetterByTopic={selectedTabCreateLetterByTopic}
          handleToggleChangeTabGenai={handleToggleChangeTabGenai}
          setTextFieldGenai={setTextFieldGenai}
          submitCreateLetterByTopic={submitCreateLetterByTopic}
          genaiLoading={genaiLoading}
          textFieldGenai={textFieldGenai}
          isMobile={isMobile}
        />
      </Grid>
      {operationInfo && operationInfo.type === 'forward' && <LastForward />}
    </Grid>
  );
};
MainInfo.propTypes = {
  isMobile: PropTypes.bool,
};

MainInfo.defaultProps = {
  isMobile: false,
};

const Tags = (value, getTagProps) =>
  value.map((option, index) => (
    <Chip
      label={
        <Typography style={{ whiteSpace: 'normal', direction: 'rtl' }}>
          {option && (option.firstName || option.lastName)
            ? `${option.firstName || ''} ${option.lastName || ''}`
            : `${option.username}`}
        </Typography>
      }
      {...getTagProps({ index })}
      style={{ height: '100%' }}
    />
  ));

const withCustomPaperComponent =
  (name) =>
  ({ children }) => {
    const { handleOpenContactDialog } = useLetter();
    return (
      <Paper elevation={4}>
        {children}
        <AWDivider />
        <AWButton
          sx={{ color: '#0099DB' }}
          onMouseDown={(e) => {
            e.stopPropagation();
            handleOpenContactDialog?.(name);
          }}
          onTouchStart={(e) => {
            handleOpenContactDialog?.(name);
          }}
          startIcon={<Add />}
        >
          {t('contacts.labels.createContact')}
        </AWButton>
      </Paper>
    );
  };

const RecipientPaperComponent = withCustomPaperComponent('recipient');
const SenderPaperComponent = withCustomPaperComponent('sender');

export default MainInfo;
