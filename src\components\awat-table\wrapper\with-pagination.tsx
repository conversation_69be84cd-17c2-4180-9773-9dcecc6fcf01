import React, { useMemo, useState } from 'react';

import { IAWTableProps } from '../interfaces';
import { SelectChangeEvent } from '@mui/material';

const withPagination = (WrappedComponent: React.ElementType) => (props: IAWTableProps) => {
  const {
    rows,
    limit,
    total,
    offset,
    handleNextPage,
    handlePrevPage,
    handleChangePage,
    handleChangeLimit,
  } = props;

  //* pagination utils
  const pageCount = Math.ceil(total / limit);
  const from = offset === 0 ? 1 : offset;
  const currentPage = offset / limit + 1;
  const to = (currentPage - 1) * limit + rows.length;

  //* change page
  const handleChangePagination = (e: React.ChangeEvent<unknown>, value: number) => {
    // if (value > currentPage) handleNextPage();
    // if (value < currentPage) handlePrevPage();
    handleChangePage(value);
  };

  //* Go to page
  const [goToPageValue, setGoToPageValue] = useState<number>(currentPage);

  //* page limit
  const [pageLimit, setPageLimit] = useState<number>(limit);

  const pageLimitOptions = useMemo(
    () => [
      { value: 10, label: '10' },
      { value: 25, label: '25' },
      { value: 50, label: '50' },
    ],
    [],
  );

  const showLimit = !!handleChangeLimit && total > 10;

  const onChangeGoToPage = (e: Event) => {
    const { value } = e.target as HTMLInputElement;
    setGoToPageValue(Number(value));
  };

  const submitGoToPage = () => {
    if (typeof goToPageValue === 'number' && goToPageValue !== 0 && goToPageValue <= pageCount) {
      handleChangePage(goToPageValue);
    }
  };

  const onChangePageLimit = (e: SelectChangeEvent) => {
    const { value } = e.target;
    setPageLimit(parseInt(value, 10));
    handleChangeLimit?.(parseInt(value, 10));
  };

  const pagination = {
    pageCount,
    onChange: handleChangePagination,
    currentPage,
    from,
    to,
    goToPageValue,
    onChangeGoToPage,
    submitGoToPage,
    onChangePageLimit,
    showLimit,
    pageLimit,
    pageLimitOptions,
  };

  return <WrappedComponent {...props} pagination={pagination} />;
};

export default withPagination;
