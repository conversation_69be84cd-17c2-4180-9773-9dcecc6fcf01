import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import Checkbox from '@mui/material/Checkbox';
import FormControlLabel from '@mui/material/FormControlLabel';
import Box from '@mui/material/Box';
import green from '@mui/material/colors/green';
import { makeStyles } from 'tss-react/mui';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import KeyboardReturn from '@mui/icons-material/KeyboardReturn';
import Divider from '@mui/material/Divider';

import DocumentViewer from 'components/document-previewer';
import SettingsIcon from '@mui/icons-material/Settings';
import { useTranslation } from 'react-i18next';
import ExtraButtons from './components/extra-buttons';
import BackdropCom from '../../../../components/backdrop';
import MainInfo from './components/main-info';
import RegistrationInfo from './components/registration-info';
import AdditionalAttachments from './components/additional-attachments';
import DialogDetails from './components/dialog-details';
import DialogSaveAndSetLetterNumber from './components/dialog-save-set-letter-number';
import DialogNumberedLetter from './components/dialog-numbered-letter';
import Loading from '../../../../components/Loading';
import DialogPreParaphList from './components/forward-letter/components/dialog-pre-paraph-list';
import { useMainLayoutStyles } from '../../../../contexts/mainLayoutStylesContext';
import { useLetter } from '../../../../contexts/pageContext/letters/letterContext';
import LetterInfoMenu from './components/letter-info-menu';
import LetterHistoryDialog from './components/dialog/letter-history';
import ErrorOfVoidOperationDialog from './components/dialog/void-letter/error-of-void-operation-dialog';
import DialogDiscardLetter from './components/dialog-discard-letter';
import DialogTerminateLetter from './components/dialog-terminate-letter';
import AWInlineBack from '../../../../components/AWComponents/AWInlineBack';
import PrintLetterDialog from './components/dialog/print-letter/print-letter-dialog';
import SendECEDialog from './components/dialog/send-ece/send-ece-dialog';
import ExportECEDialog from './components/dialog/export-ece/export-ece-dialog';
import WarningDialogForNumberForIndicatorStatus from './components/dialog/number-error/number-error-dialog';
import { formatLetterNumber } from '../../../../utils/letterNumberUtils';
import DialogSignAndSaveOrCancel from './components/dialog-sign-and-save-or-cancel';
import ContactsDialog from '../../../management-center/manageContacts/components/contactDialog';
import AttachmentsDesktop from './components/attachments-desktop';
import ConfirmationVoidLetterDialog from './components/dialog/void-letter/confirmation-void-letter-dialog';
import LetterReferences from './components/letter-references';
import DialogForwardLetter from './components/dialog-forward-letter';
import LetterInfoConfigDialog from './components/letter-info-config-dialog';
import AWIconButton from '../../../../components/AWComponents/AWIconButton';
import AWTooltip from '../../../../components/AWComponents/AWTooltip';
import WarningDialogNumberRepetitive from './components/dialog/numberRepetitive';
import DialogParaphCreator from './components/forward-letter/components/dialog-paraph-creator';

const useStyles = makeStyles()((theme) => ({
  border: {
    borderLeft: '1px solid #cbcbcb',
    padding: '0px 15px',
  },
  paper: {
    height: 'calc(100vh - 108px)',
    borderRadius: 12,
    display: 'flex',
    flexDirection: 'column',
  },
  scrollBox: {
    overflow: 'auto',
    overflowX: 'hidden',
    position: 'relative',
    flex: 1,
  },
  grid: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'flex-start',
    padding: theme.spacing(1.5, 0, 1.5, 1),
    minHeight: 485,
    flex: 1,
    height: '100%',
    [theme.breakpoints.down('sm')]: {
      padding: theme.spacing(3),
    },
  },
  marginTop2: {
    marginTop: theme.spacing(2),
  },
  paddingBottom2: {
    paddingBottom: theme.spacing(2),
  },
  titleBox: {
    '& p': {
      display: 'flex',
      alignItems: 'center',
      fontWeight: 'bold',
      color: '#6c6c6c',
    },
    '& p:after': {
      content: '""',
      flex: 1,
      height: '1px',
      backgroundColor: theme.palette.text.lightGray,
      marginRight: 8,
    },
    '& .infoIcon': {
      fontSize: 14,
      margin: theme.spacing(0, 1, 0, 0.5),
    },
    '& span': {
      color: theme.palette.text.gray,
      fontSize: '0.75rem',
      fontWeight: 100,
    },
  },
  greenCheckboxRoot: {
    color: green[400],
    paddingLeft: 0,
    '&$checked': {
      color: green[600],
    },
  },
  attachmentsGrid: {
    // display: 'flex',
  },
  attachmentsLabel: {
    minWidth: 115,
  },
  dataPicker: {
    display: 'inherit !important',
    zIndex: 999,
    '& .MuiInputBase-root': {
      paddingLeft: 0,
    },
  },
  allItems: {
    display: 'flex',
    borderBottom: ' 1px solid #d1d1d1',
    padding: '8px 0',
    justifyContent: 'space-between',
    height: 52,
    alignItems: 'center',
    '& > div': {
      // margin: '0 10%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'space-between',
      flexGrow: 1,
    },
  },
  allItemsUl: {
    padding: `0 0px`,
    listStyle: 'none',
    [theme.breakpoints.down('lg')]: {
      padding: `0 8px`,
    },
    '& > :first-child:after': {
      content: '""',
      borderRight: '1px solid #9a9a9a',
      padding: theme.spacing(0, 1),
    },
    '& li': {
      float: 'right',
      margin: theme.spacing(0, 2),
      '& .label': {
        alignItems: 'end',
        fontSize: '0.875rem',
        color: '#6c6c6c',
      },
      '& .MuiCheckbox-root': {
        padding: 0,
      },
      '& .MuiCheckbox-root.Mui-checked': {
        color: theme.palette.background.lightGreen,
      },
      '& .MuiSvgIcon-root': {
        width: '0.8em',
        height: '0.8em',
        fontSize: '1.3rem',
      },
    },
  },
  buttonsBox: {
    display: 'flex',
    // border: '1px solid #d1d1d1',
    boxShadow: '0px 1px 8px rgb(0 0 0 / 12%)',
    borderRadius: 16,
    border: 'unset',
    justifyContent: 'space-between',
    alignItems: 'center',
    // position: 'absolute',
    bottom: 20,
    background: '#fff',
    zIndex: 999,
    padding: '14px 16px',
    // margin: '0px 8px',
    width: '100%',
    '& .padding': {
      padding: '0 56px',
      margin: '0 10%',
      width: '100%',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
  },
  cancelBtn: {
    marginLeft: theme.spacing(1),
  },
  back: {
    position: 'absolute',
    padding: 9,
    zIndex: 9,
    top: 5,
    '& svg': {
      background: theme.palette.background.secondColor,
      color: theme.palette.background.default,
      padding: 3,
      borderRadius: 50,
    },
  },
  referencesList: {
    borderRadius: 6,
    boxShadow: '0 1px 5px 0 rgba(0, 0, 0, 0.08)',
    backgroundColor: '#fff',
    padding: 0,
    width: '49%',
    listStyle: 'none',
    '& li': {
      padding: theme.spacing(1),
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    '& .d-flex-center': {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
    },
    '& h6': {
      color: '#000',
      display: 'inline',
    },
    '& span.dot': {
      width: 5,
      height: 5,
      borderRadius: 10,
      display: 'inline-block',
      background: theme.palette.background.secondColor,
      margin: '0 8px',
    },
  },
  margin: {
    margin: theme.spacing(2, 0, 0),
    width: '100%',
    display: 'flex',
    justifyContent: 'space-between',
  },
  numberLetter: {
    display: 'flex',
    alignItems: 'center',
    padding: '0 0 0 56px',
    '& .label': {
      color: theme.palette.text.gray,
      lineHeight: 1,
    },
    '& button': {
      margin: '0 16px 0 0',
      padding: 0,
    },
  },
  letterInfoButton: {
    padding: [theme.spacing(0.5), '!important'],
  },
  forwardBtn: {
    minWidth: '110px',
  },
  descriptionText: {
    marginTop: theme.spacing(1),
    color: theme.palette.text.gray,
    fontWeight: 'normal',
  },
  extraItems: {
    display: 'flex',
    flexDirection: 'row',
    gap: 4,
    background: theme.palette.background.shadow,
    width: '100%',
    borderRadius: '6px',
    padding: theme.spacing(0.5, 1),
  },
  divider: {
    margin: theme.spacing(2, 0),
  },
  actionBar: {
    marginTop: theme.spacing(1),
    padding: theme.spacing(0, 0, 0, 1.5),
  },
  attachmentListContainer: {
    display: 'flex',
    justifyContent: 'left',
  },
  attachmentListContent: {
    flex: 0.5,
  },
}));

const DesktopLetter = () => {
  const { classes, cx } = useStyles();
  const { stylesGenerator } = useMainLayoutStyles();
  const { classes: globalClasses } = makeStyles()(stylesGenerator)();
  const { t } = useTranslation();

  const {
    type,
    loading,
    extraItems,
    isInvalid,
    getValidationError,
    openBackdrop,
    handleChangeChecked,
    goBack,
    referenceTypes,
    referenceTypeSelection,
    handleChangeReferenceType,
    handleChangeReferenceNumber,
    selectedReferenceDate,
    handleSelectedReferenceDate,
    handleDeleteReferenceItem,
    referencesList,
    referencesItems,
    addReference,
    openDetails,
    isLetterEditable,
    showDialogSaveAndSetLetterNumber,
    showDialogNumberedLetter,
    additionalAttachmentsRef,
    referenceRef,
    registrationInfoRef,
    attachmentsRef,
    openForwardLetter,
    openPreParaphList,
    onForward,
    handleIsDisabledForward,
    handleAddAttachment,
    attachments,
    handleOpenLetterInfoMenu,
    isVisibleLetterHistoryDialog,
    showDialogDiscard,
    showDialogNumberRepetitive,
    showDialogTerminate,
    letterNumber,
    showDialogNumberedErrorForIndicatorStatus,
    setShowDialogNumberedErrorForIndicatorStatus,
    showDialogSignAndSaveOrCancel,
    setShowDialogSignAndSaveOrCancel,
    handleCreatedContact,
    handleCloseContactDialog,
    quicklyAddingContactDialog,
    voidLetterDialog,
    handleCloseVoidLetterDialog,
    onChangeValueNote,
    voidLetterNote,
    submitVoidLetter,
    voidLetterLoading,
    errorOfVoidOperationText,
    errorVoidLetterDialog,
    handleCloseErrorVoidLetterDialog,
    selectedLetter,
    openDocumentPreviewerDialog,
    handleCloseDocumentPreviewer,
    showFieldsReferenceLetter,
    handleShowReferenceLetter,
    setReferenceLetterNumber,
    referenceLetterNumberList,
    getReferenceLettersList,
    searchReferencesLettersLoading,
    referenceSearchPopoverOpen,
    handleCloseReferenceSearchPopover,
    handleSelectReferenceSearchPopover,
    handleNavigateToReference,
    resetSelectedReferenceLetter,
    composeInfoConfig,
    isOpenComposeInfoConfig,
    handleComposeInfoConfigDialog,
    handleSubmitComposeInfoConfig,
    onChangeComposeInfoConfig,
    openPreCreateParaph,
  } = useLetter();

  return (
    <>
      <BackdropCom {...{ openBackdrop }} />
      <Box className={globalClasses.rootPage}>
        <Paper elevation={0} className={classes.paper}>
          <AWInlineBack goBack={goBack}>
            <Box className={classes.extraItems}>
              {extraItems?.map((item) => (
                <Box key={item.id}>
                  <FormControlLabel
                    className={cx({ [classes.border]: item.id === 'allShow' })}
                    // classes={{ label: 'label' }}
                    control={
                      <Checkbox
                        checked={item.checked}
                        onChange={(e) => handleChangeChecked(e, item)}
                        name={item.id}
                        size="small"
                      />
                    }
                    label={item.title}
                  />
                </Box>
              ))}
              <AWTooltip
                title={t('correspondence.letters.labels.compose-info-config')}
                position="bottom"
              >
                <span>
                  <AWIconButton size="small" onClick={handleComposeInfoConfigDialog}>
                    <SettingsIcon />
                  </AWIconButton>
                </span>
              </AWTooltip>
              {type !== 'create' && (
                <>
                  <Box style={{ flex: 1 }} />
                  <Box className={classes.numberLetter}>
                    <Box>
                      <Typography variant="subtitle2" className="label">
                        {t('commands.letters.numberLetter')}{' '}
                      </Typography>
                      <Typography variant="subtitle1" dir="ltr">
                        {Boolean(letterNumber) && (
                          <>
                            {selectedLetter.status === 'void' &&
                              `${t('correspondence.letters.void-letter.labels.voided')} - `}
                            {formatLetterNumber(letterNumber)}
                          </>
                        )}
                        {!letterNumber && t('commands.letters.notNumberLetter')}
                      </Typography>
                    </Box>
                    <>
                      <IconButton
                        onClick={handleOpenLetterInfoMenu}
                        id="more-info-button"
                        className={classes.letterInfoButton}
                      >
                        <MoreVertIcon />
                      </IconButton>
                      <LetterInfoMenu />
                    </>
                    {/* <Tooltip title="مشاهده اطلاعات ثبتی نامه">
                    <IconButton onClick={handleOpenDetails} id="letter-details-button">
                      <InfoIcon className={classes.moreView} />
                    </IconButton>
                  </Tooltip> */}
                  </Box>
                </>
              )}
            </Box>
          </AWInlineBack>
          <Divider className={classes.divider} />

          {loading ? (
            <Loading />
          ) : (
            <>
              <Box className={classes.scrollBox}>
                <Box className={classes.grid}>
                  <MainInfo />
                  {extraItems[1].checked && (
                    <Box ref={attachmentsRef}>
                      <AttachmentsDesktop />
                    </Box>
                  )}
                  {extraItems[2].checked && (
                    <Grid
                      container
                      spacing={3}
                      className={cx(classes.marginTop2, classes.paddingBottom2)}
                      ref={registrationInfoRef}
                    >
                      <Grid item xs={12} className={classes.titleBox}>
                        <Typography variant="subtitle1">
                          {t('secretariats.labels.registerInformation')}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} className={classes.titleBox}>
                        <RegistrationInfo />
                      </Grid>
                    </Grid>
                  )}
                  {extraItems[3].checked && (
                    <LetterReferences
                      addReference={addReference}
                      getValidationError={getValidationError}
                      handleChangeReferenceNumber={handleChangeReferenceNumber}
                      handleChangeReferenceType={handleChangeReferenceType}
                      isInvalid={isInvalid}
                      handleDeleteReferenceItem={handleDeleteReferenceItem}
                      handleSelectedReferenceDate={handleSelectedReferenceDate}
                      handleShowReferenceLetter={handleShowReferenceLetter}
                      isLetterEditable={isLetterEditable}
                      referenceTypeSelection={referenceTypeSelection}
                      referenceTypes={referenceTypes}
                      referencesItems={referencesItems}
                      referencesList={referencesList}
                      selectedReferenceDate={selectedReferenceDate}
                      showFieldsReferenceLetter={showFieldsReferenceLetter}
                      ref={referenceRef}
                      getReferenceLettersList={getReferenceLettersList}
                      searchReferencesLettersLoading={searchReferencesLettersLoading}
                      referenceLetterNumberList={referenceLetterNumberList}
                      referenceSearchPopoverOpen={referenceSearchPopoverOpen}
                      onReferenceItemClick={handleNavigateToReference}
                      resetSelectedReferenceLetter={resetSelectedReferenceLetter}
                      handleCloseReferenceSearchPopover={handleCloseReferenceSearchPopover}
                      handleSelectReferenceSearchPopover={handleSelectReferenceSearchPopover}
                    />
                  )}
                  {extraItems[4].checked && (
                    <Grid
                      container
                      spacing={3}
                      className={cx(classes.marginTop2, classes.paddingBottom2)}
                      ref={additionalAttachmentsRef}
                    >
                      <Grid item xs={12} className={classes.titleBox}>
                        <Typography variant="subtitle1">
                          {t('secretariats.labels.additionalAttachments')}{' '}
                          <InfoOutlinedIcon className="infoIcon" />
                          <Typography variant="body2">
                            {t('secretariats.labels.additionalAttachmentsBody')}
                          </Typography>
                        </Typography>
                      </Grid>
                      <AdditionalAttachments />
                    </Grid>
                  )}
                </Box>
              </Box>
              <Box className={classes.actionBar}>
                <Box className={classes.buttonsBox}>
                  <ExtraButtons />
                  <Box>
                    <Button
                      variant="contained"
                      onClick={onForward}
                      disabled={handleIsDisabledForward()}
                      startIcon={<KeyboardReturn />}
                      classes={{ root: classes.forwardBtn }}
                      id="on-forward-letter"
                    >
                      {t(`solutionHistory.labels.letter.forward`)}
                    </Button>
                  </Box>
                </Box>
              </Box>
            </>
          )}
        </Paper>
      </Box>
      <DialogSignAndSaveOrCancel
        open={showDialogSignAndSaveOrCancel}
        onClose={() => setShowDialogSignAndSaveOrCancel(false)}
      />
      {openDetails && <DialogDetails />}
      {isVisibleLetterHistoryDialog && <LetterHistoryDialog />}
      {showDialogSaveAndSetLetterNumber && <DialogSaveAndSetLetterNumber />}
      {showDialogNumberedLetter && <DialogNumberedLetter />}
      {openForwardLetter && <DialogForwardLetter />}
      {openPreParaphList && <DialogPreParaphList />}
      {openPreCreateParaph && <DialogParaphCreator />}
      {showDialogTerminate && <DialogTerminateLetter />}
      {showDialogDiscard && <DialogDiscardLetter />}
      {showDialogNumberRepetitive && <WarningDialogNumberRepetitive />}
      <WarningDialogForNumberForIndicatorStatus
        open={showDialogNumberedErrorForIndicatorStatus}
        onClose={() => setShowDialogNumberedErrorForIndicatorStatus(() => false)}
      />
      <PrintLetterDialog />
      <SendECEDialog />
      <ExportECEDialog />
      <ContactsDialog
        onSave={handleCreatedContact}
        onClose={handleCloseContactDialog}
        open={quicklyAddingContactDialog.open}
      />
      <ConfirmationVoidLetterDialog
        onClose={handleCloseVoidLetterDialog}
        open={voidLetterDialog}
        valueNote={voidLetterNote}
        onChangeValueNote={onChangeValueNote}
        submit={submitVoidLetter}
        loading={voidLetterLoading}
      />
      <ErrorOfVoidOperationDialog
        title={errorOfVoidOperationText}
        open={errorVoidLetterDialog}
        onClose={handleCloseErrorVoidLetterDialog}
      />
      {openDocumentPreviewerDialog?.open && (
        <DocumentViewer
          currentIndex={openDocumentPreviewerDialog.currentIndex}
          documents={openDocumentPreviewerDialog.documents}
          open={openDocumentPreviewerDialog.open}
          onClose={handleCloseDocumentPreviewer}
        />
      )}
      {isOpenComposeInfoConfig && (
        <LetterInfoConfigDialog
          open={isOpenComposeInfoConfig}
          composeInfoConfig={composeInfoConfig}
          onClose={handleComposeInfoConfigDialog}
          onChangeComposeInfoConfig={onChangeComposeInfoConfig}
          handleSubmitComposeInfoConfig={handleSubmitComposeInfoConfig}
        />
      )}
    </>
  );
};

export default DesktopLetter;
