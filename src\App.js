import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Provider } from 'react-redux';
import { Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { CacheProvider } from '@emotion/react';
import createCache from '@emotion/cache';
import { TssCacheProvider } from 'tss-react';

import rtlPlugin from 'stylis-plugin-rtl';
import './App.css';
import { withAdministrationInitializer } from 'hoc/authz/administration/with-administration-initializer';
import * as reflect from 'reflect-metadata';
import { setAxiosInterceptors } from 'api/v1/services/api-client';
import { TourProvider } from 'contexts/tourContext/TourContext';
import CommonProvider from 'contexts/commonContext/provider';
import { setupStore } from './store';
import { loginByPasswordReset } from './pages/Auth/authSlice';
import { AuthContext, useAuth } from './contexts/authContext';
import { UserContext } from './contexts/userContext';
import { LayoutContext, useLayout } from './contexts/layoutContext';
import defaultTheme from './themes/default';
import MainLayout from './components/MainLayout';
import routes from './routes';
import Terms from './pages/terms';
import NotificationsProvider from './components/Notification/index';
import ChangeLogs from './pages/change-logs/page-change-logs';
import GuidePage from './pages/guide';
import SecurityContextProvider from './common/security/security-context';

const store = setupStore();

const cacheRtl = createCache({
  key: 'muirtl',
  stylisPlugins: [rtlPlugin],
  prepend: true,
});

const tssCache = createCache({
  key: 'tss',
});

const PrivateRoute = withAdministrationInitializer(({ children }) => {
  const { auth } = useAuth();
  const {
    title,
    setTitle,
    setIconTopBar,
    setShowSideBar,
    setShowAppBar,
    setShowBottomBar,
    setIsReadOnlyPositionSelection,
    setShowPositionSelection,
    setShowBreadcrumbs,
    setBreadcrumbsInfo,
    setSideMenus,
    setIsGuidePage,
    setShowBackButton,
    setBottomBarMenus,
    setBackURL,
  } = useLayout();
  const location = useLocation();
  const { props } = children;

  useEffect(() => {
    setTitle(props.title || title || '');
    setIconTopBar(props.iconTopBar || '');
    setShowSideBar(props.showSideBar || false);
    setShowAppBar(props.showAppBar || false);
    setShowBottomBar(props.showBottomBar || false);
    setIsReadOnlyPositionSelection(props.isReadOnlyPositionSelection || false);
    setShowPositionSelection(props.showPositionSelection || true);
    setShowBreadcrumbs(props.showBreadcrumbs || false);
    setBreadcrumbsInfo(props.breadcrumbsInfo || null);
    setSideMenus(props.sideMenus || []);
    setIsGuidePage(props.isGuidePage || false);
    setShowBackButton(props.showBackButton || false);
    setBottomBarMenus(props.bottomBarMenus || []);
    setBackURL(props.backURL || '/organizations');
  }, [location.pathname]);

  if (auth) {
    const childrenWithProps = React.Children.map(children, (child) => {
      if (React.isValidElement(child)) {
        return React.cloneElement(child, { security: props?.security });
      }
      return child;
    });

    return <>{childrenWithProps}</>;
  }

  window.location.href = '/login';
  return <></>;
});

function App() {
  const navigate = useNavigate();
  const location = useLocation();
  const [title, setTitle] = useState('');
  const [iconTopBar, setIconTopBar] = useState('');
  const [showSideBar, setShowSideBar] = useState(false); // isSideBarVisible
  const [showAppBar, setShowAppBar] = useState(false);
  const [showBottomBar, setShowBottomBar] = useState(false);
  const [showPositionSelection, setShowPositionSelection] = useState(false);
  const [isReadOnlyPositionSelection, setIsReadOnlyPositionSelection] = useState(false);
  const [showBreadcrumbs, setShowBreadcrumbs] = useState(false);
  const [breadcrumbsInfo, setBreadcrumbsInfo] = useState(null);
  const [bottomBarMenus, setBottomBarMenus] = useState([]);
  const [sideMenus, setSideMenus] = useState([]);
  const [backURL, setBackURL] = useState('');
  const [isGuidePage, setIsGuidePage] = useState(false);
  const [showBackButton, setShowBackButton] = useState(false);
  const [openCheckStatusOveredDialog, setOpenCheckStatusOveredDialog] = useState(false);
  const [commandName, setCommandName] = useState(null);
  const [auth, setAuth] = useState(JSON.parse(localStorage.getItem('tokens') || 'null'));
  const [signedInUser, setSignedInUser] = useState(
    JSON.parse(localStorage.getItem('user') || 'null'),
  );
  const [selectedPosition, setSelectedPosition] = useState(null);

  //* Redirect to organizations page if auth token is valid
  useEffect(() => {
    const { pathname } = location;
    if (pathname === '/' || pathname === '/login' || pathname === 'register?step=3') {
      if (auth) {
        navigate('/organizations');
      }
    }
  }, [location, navigate, auth]);

  const setAuthImpl = (data) => {
    if (data) {
      localStorage.setItem('tokens', JSON.stringify(data));
    } else {
      store.dispatch(loginByPasswordReset());
    }
    setAxiosInterceptors(data);
    setAuth(data);
  };

  const initialized = useRef(false);
  if (!initialized.current) {
    initialized.current = true;
    setAxiosInterceptors(JSON.parse(localStorage.getItem('tokens') || 'null'));
  }

  const setSignedInUserImpl = (data) => {
    if (data) {
      localStorage.setItem('user', JSON.stringify(data));
    } else {
      store.dispatch(loginByPasswordReset());
    }
    setSignedInUser(data);
  };

  useEffect(() => {
    const loggedInUser = localStorage.getItem('user');
    if (loggedInUser) {
      navigate(
        location.pathname !== ('/' || 'login')
          ? location.pathname + location.search
          : '/organizations',
        { replace: true },
      );
    }
  }, []);

  const handleOpenCheckStatusOveredDialog = (name) => {
    setCommandName(name);
    setOpenCheckStatusOveredDialog(true);
  };
  const handleCloseCheckStatusOveredDialog = () => {
    setCommandName(null);
    setOpenCheckStatusOveredDialog(false);
  };

  //! TODO: Use RxJS instead of context state for 'Task' and "Meeting" solution
  const [openTaskDialog, setOpenTaskDialog] = useState(false);
  const [openMeetingDialog, setOpenMeetingDialog] = useState(false);

  return (
    <Provider store={store}>
      <AuthContext.Provider value={{ auth, setAuth: setAuthImpl }}>
        <UserContext.Provider
          value={{
            signedInUser,
            setSignedInUser: setSignedInUserImpl,
            auth: store.getState().auth,
          }}
        >
          <CacheProvider value={cacheRtl}>
            <TssCacheProvider value={tssCache}>
              <ThemeProvider theme={defaultTheme}>
                <NotificationsProvider />
                <LayoutContext.Provider
                  value={{
                    showSideBar,
                    setShowSideBar,
                    showAppBar,
                    setShowAppBar,
                    title,
                    setTitle,
                    iconTopBar,
                    setIconTopBar,
                    showBottomBar,
                    setShowBottomBar,

                    // positions
                    isReadOnlyPositionSelection,
                    setIsReadOnlyPositionSelection,
                    showPositionSelection,
                    setShowPositionSelection,

                    showBreadcrumbs,
                    setShowBreadcrumbs,
                    breadcrumbsInfo,
                    setBreadcrumbsInfo,
                    sideMenus,
                    setSideMenus,
                    backURL,
                    setBackURL,
                    bottomBarMenus,
                    setBottomBarMenus,
                    isGuidePage,
                    setIsGuidePage,
                    showBackButton,
                    setShowBackButton,
                    selectedPosition,
                    setSelectedPosition,
                    commandName,
                    openCheckStatusOveredDialog,
                    handleOpenCheckStatusOveredDialog,
                    handleCloseCheckStatusOveredDialog,
                    openTaskDialog,
                    setOpenTaskDialog,
                    openMeetingDialog,
                    setOpenMeetingDialog,
                  }}
                >
                  <CssBaseline />
                  <TourProvider>
                    <Routes>
                      <Route path="/terms" element={<Terms />} />
                      <Route path="/logs" element={<ChangeLogs />} />
                      <Route path="/guide" element={<GuidePage />} />
                      <Route
                        path="/"
                        element={
                          <CommonProvider>
                            <SecurityContextProvider>
                              <MainLayout />
                            </SecurityContextProvider>
                          </CommonProvider>
                        }
                      >
                        {routes.map((route, idx) => (
                          <>
                            {route.isPublicRoute ? (
                              <Route
                                path={route.path}
                                element={<route.component />}
                                key={route.path}
                              />
                            ) : (
                              <Route
                                key={route.path}
                                path={route.path}
                                element={
                                  <PrivateRoute>
                                    <route.component
                                      showAppBar={route.showAppBar}
                                      showSideBar={route.showSideBar}
                                      showBottomBar={route.showBottomBar}
                                      isReadOnlyPositionSelection={
                                        route.isReadOnlyPositionSelection
                                      }
                                      showPositionSelection={route.showPositionSelection}
                                      showBreadcrumbs={route.showBreadcrumbs}
                                      breadcrumbsInfo={route.breadcrumbsInfo}
                                      title={route.title}
                                      iconTopBar={route.iconTopBar}
                                      sideMenus={route.sideMenus}
                                      bottomBarMenus={route.bottomBarMenus}
                                      isGuidePage={route.isGuidePage}
                                      showBackButton={route.showBackButton}
                                      security={route?.security}
                                      backURL={route?.backURL}
                                    />
                                  </PrivateRoute>
                                }
                              />
                            )}
                          </>
                        ))}
                      </Route>
                    </Routes>
                  </TourProvider>
                </LayoutContext.Provider>
              </ThemeProvider>
            </TssCacheProvider>
          </CacheProvider>
        </UserContext.Provider>
      </AuthContext.Provider>
    </Provider>
  );
}

PrivateRoute.propTypes = {
  children: PropTypes.shape({
    props: PropTypes.shape({}).isRequired,
  }).isRequired,
};

export default App;
