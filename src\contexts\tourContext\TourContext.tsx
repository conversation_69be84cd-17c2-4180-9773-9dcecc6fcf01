import React, { createContext, PropsWithChildren, useCallback, useContext, useState } from 'react';
import { TourContextProps, TourStep } from './types';
import Tour from './tour';
import { useUser } from 'contexts/userContext';

const defaultValue: TourContextProps = {
  currentStep: 0,
  tourSteps: [],
  isTourOpen: false,
  startTour: () => {},
  nextStep: () => {},
  endTour: () => {},
};

interface Config {
  showAgain: boolean;
}

export const TourContext = createContext<TourContextProps>(defaultValue);

export const TourProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { signedInUser } = useUser();
  const [currentStep, setCurrentStep] = useState(0);
  const [isTourOpen, setIsTourOpen] = useState(false);
  const [tourSteps, setTourSteps] = useState<TourStep[]>(() => []);

  const localStorageKey = `tourGuid`;

  const getLocalConfig = useCallback(
    (key: string) => {
      try {
        const configsString = localStorage.getItem(localStorageKey);
        if (configsString) {
          const configsObject = JSON.parse(configsString);
          const currentConfig = configsObject?.[`${key}_${signedInUser.id}`];

          if (currentConfig) {
            return currentConfig as Config;
          }
        }
      } catch {
        return null;
      }
      return null;
    },
    [signedInUser?.id],
  );

  const changeConfig = useCallback(
    (key: string, value: Config) => {
      const configsString = localStorage.getItem(localStorageKey);
      const configsObject = configsString ? JSON.parse(configsString) : {};
      configsObject[`${key}_${signedInUser.id}`] = value;
      const newConfig = JSON.stringify(configsObject);
      localStorage.setItem(localStorageKey, newConfig);
    },
    [signedInUser?.id],
  );

  const startTour = useCallback(
    (steps: TourStep[], options?: { showAgain: boolean; key: string }) => {
      if (!steps || steps?.length === 0) {
        return;
      }

      if (options && signedInUser.id) {
        const lastConfig = getLocalConfig(options.key);
        if (lastConfig && lastConfig.showAgain === false) {
          return;
        }

        changeConfig(options.key, { showAgain: options.showAgain });
      }

      setCurrentStep(0);
      setIsTourOpen(true);
      if (steps) {
        setTourSteps(() => steps);
      }
      scrollToTarget(steps[0].target);
    },
    [],
  );

  const nextStep = () => {
    if (currentStep < tourSteps.length - 1) {
      setCurrentStep((prev) => prev + 1);
      scrollToTarget(tourSteps[currentStep + 1].target);
    } else {
      endTour();
    }
  };

  const endTour = () => {
    setIsTourOpen(false);
    setCurrentStep(0);
  };

  const scrollToTarget = (targetId: string) => {
    const target = document.getElementById(targetId);
    if (target) {
      target.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  return (
    <TourContext.Provider
      value={{ currentStep, tourSteps, isTourOpen, startTour, nextStep, endTour }}
    >
      {children}
      {tourSteps.length > 0 && <Tour />}
    </TourContext.Provider>
  );
};

export const useTour = (): TourContextProps => useContext(TourContext);
