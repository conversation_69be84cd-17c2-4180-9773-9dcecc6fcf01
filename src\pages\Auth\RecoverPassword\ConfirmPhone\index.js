import React from 'react';
import useMediaQuery from '@mui/material/useMediaQuery';
import DesktopConfirmPhone from './confirm-phone-desktop';
import TabletConfirmPhone from './confirm-phone-tablet';
import MobileConfirmPhone from './confirm-phone-mobile';

const ConfirmPhone = () => {
  const mobile = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const tablet = useMediaQuery((theme) => theme.breakpoints.between('sm', 'lg'));
  const desktop = useMediaQuery((theme) => theme.breakpoints.up('lg'));
  if (mobile) {
    return <MobileConfirmPhone isMobile />;
  }
  if (tablet) {
    return <TabletConfirmPhone />;
  }
  if (desktop) {
    return <DesktopConfirmPhone />;
  }
  return <></>;
};

export default ConfirmPhone;
